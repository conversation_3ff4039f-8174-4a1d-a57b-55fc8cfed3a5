<div class="d-lg-flex justify-content-lg-center">
    <app-tab-menu [stepperList]="stepperList" (updatedStep)="onSelectionCallBack($event)"
        [lastStoredStep]="this.rateSheet.id ? 4 : 1"></app-tab-menu>
</div>

<ng-container [ngSwitch]="selectedStep">
    <app-rate-sheet-basic *ngSwitchCase="1" [request]="request" [rateSheet]="rateSheet"
        [onClickValidation]="onClickValidation" [filterParam]="filterParam" (saveButtonClicked)="saveRateSheetStep()"
        (onNextClick)="tabMenuUpdate($event,true)"></app-rate-sheet-basic>

    <app-rate-sheet-weight-charges *ngSwitchCase="2"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-rate-sheet-weight-charges>

    <app-rate-sheet-special-request *ngSwitchCase="3" [request]="request" [rateSheet]="rateSheet"
        [onClickValidation]="onClickValidation" [filterParam]="filterParam" (saveButtonClicked)="saveRateSheetStep()"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-rate-sheet-special-request>
</ng-container>