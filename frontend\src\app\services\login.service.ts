import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { RestResponse } from '../models/common/auth.model';

@Injectable({
    providedIn: 'root',
})
export class LoginService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/account', '/api/account');
    }

    login(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/account/login', data);
    }

    logout(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/account/logout', data);
    }

    resetPassword(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/account/forgot/password', data);
    }

    recoverPassword(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/account/reset/password', data);
    }
}
