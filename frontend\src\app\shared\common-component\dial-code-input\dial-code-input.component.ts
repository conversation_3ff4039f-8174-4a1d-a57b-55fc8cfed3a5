import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormsModule, NgForm } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Constant } from '../../../config/constants';
import { PhoneValidatorDirective } from '../../directives/phone-validator.directive';
import { ValidationMessageComponent } from '../validation-message/validation-message.component';

@Component({
    selector: 'app-dial-code-input',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        NgSelectModule,
        TranslateModule,
        ValidationMessageComponent,
        PhoneValidatorDirective,
        NgxMaskDirective,
    ],
    templateUrl: './dial-code-input.component.html',
    styleUrl: './dial-code-input.component.scss',
    providers: [provideNgxMask()],
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
})
export class DialCodeInputComponent implements OnInit {
    countryCodes: any[] = [];

    @Input() countryCode: string | null = null; // Ensure string init
    @Input() number: string | null = null;
    @Input() required: boolean = false;
    @Input() onClickValidation: boolean = false;
    @Input() disableCode: boolean = true;
    @Input() disableNumber: boolean = false;
    @Input() fieldName: string = 'number';
    @Input() nameCode: string = 'countryCode';
    @Input() labelName!: string;

    phoneMaskPattern = Constant.MASKS.PHONE_MASK;

    @Output() countryCodeChange = new EventEmitter<string>();
    @Output() numberChange = new EventEmitter<string>();

    constructor(
        protected http: HttpClient,
        protected cdRef: ChangeDetectorRef,
    ) {}

    ngAfterViewInit() {
        this.cdRef.detectChanges();
    }

    ngOnInit(): void {
        this.countryCodes = Constant.COUNTRY_CODES;
    }

    onCountryCodeChange(value: string) {
        this.countryCodeChange.emit(value);
    }

    onNumberChange(value: string) {
        this.numberChange.emit(value);
    }
}
