import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NgbAccordionModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { provideNgxMask } from 'ngx-mask';
import { BaseListServerSideComponent } from '../../../../../config/base.list.server.side.component';
import { Constant } from '../../../../../config/constants';
import { Customer } from '../../../../../models/customer/customer';
import { LoadingService } from '../../../../../services/loading.service';
import { CommonService } from '../../../../../shared/services/common.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { ShipmentComponent } from '../../../shipment/shipment.component';
import { CustomerShipmentsManager } from './customer-shipments.manger';

@Component({
    selector: 'app-customer-shipments',
    standalone: true,
    imports: [
        CommonModule,

        DataTablesModule,
        FormsModule,
        NgbTooltipModule,
        NgbAccordionModule,
        TranslateModule,
        NgSelectModule,
        ShipmentComponent,
        RouterModule,
    ],
    templateUrl: './customer-shipments.component.html',
    styleUrl: './customer-shipments.component.scss',
    providers: [provideNgxMask()],
})
export class CustomerShipmentsComponent extends BaseListServerSideComponent implements OnInit {
    @Input() customerId!: string;
    @Output() onNextOrBackClick = new EventEmitter<number>();

    customers!: Array<Customer>;

    alertMessage = Constant.ALERT_MESSAGES;
    phoneMaskPattern = Constant.MASKS.PHONE_MASK;
    statusList = Constant.STATUS;

    constructor(
        protected customerShipmentsManager: CustomerShipmentsManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
    ) {
        super(customerShipmentsManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.customers = new Array<Customer>();
        this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
        this.init();
    }

    override onFetchCompleted() {
        this.customers = this.records.map((data) => Customer.fromResponse(data));
        super.onFetchCompleted();
    }

    onBack() {
        this.onNextOrBackClick.emit(3);
    }
}
