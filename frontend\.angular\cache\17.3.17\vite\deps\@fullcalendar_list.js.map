{"version": 3, "sources": ["../../../../../node_modules/@fullcalendar/list/internal.js", "../../../../../node_modules/@fullcalendar/list/index.js"], "sourcesContent": ["import { BaseComponent, getUniqueDomId, getDateMeta, buildNavLinkAttrs, ContentContainer, getDayClassNames, formatDayString, createFormatter, EventContainer, getSegAnchorAttrs, isMultiDayRange, buildSegTimeText, DateComponent, memoize, ViewContainer, Scroller, NowTimer, sortEventSegs, getSegMeta, sliceEventStore, intersectRanges, startOfDay, addDays, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, Fragment } from '@fullcalendar/core/preact.js';\n\nclass ListViewHeaderRow extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.state = {\n            textId: getUniqueDomId(),\n        };\n    }\n    render() {\n        let { theme, dateEnv, options, viewApi } = this.context;\n        let { cellId, dayDate, todayRange } = this.props;\n        let { textId } = this.state;\n        let dayMeta = getDateMeta(dayDate, todayRange);\n        // will ever be falsy?\n        let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : '';\n        // will ever be falsy? also, BAD NAME \"alt\"\n        let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : '';\n        let renderProps = Object.assign({ date: dateEnv.toDate(dayDate), view: viewApi, textId,\n            text,\n            sideText, navLinkAttrs: buildNavLinkAttrs(this.context, dayDate), sideNavLinkAttrs: buildNavLinkAttrs(this.context, dayDate, 'day', false) }, dayMeta);\n        // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n        return (createElement(ContentContainer, { elTag: \"tr\", elClasses: [\n                'fc-list-day',\n                ...getDayClassNames(dayMeta, theme),\n            ], elAttrs: {\n                'data-date': formatDayString(dayDate),\n            }, renderProps: renderProps, generatorName: \"dayHeaderContent\", customGenerator: options.dayHeaderContent, defaultGenerator: renderInnerContent, classNameGenerator: options.dayHeaderClassNames, didMount: options.dayHeaderDidMount, willUnmount: options.dayHeaderWillUnmount }, (InnerContent) => ( // TODO: force-hide top border based on :first-child\n        createElement(\"th\", { scope: \"colgroup\", colSpan: 3, id: cellId, \"aria-labelledby\": textId },\n            createElement(InnerContent, { elTag: \"div\", elClasses: [\n                    'fc-list-day-cushion',\n                    theme.getClass('tableCellShaded'),\n                ] })))));\n    }\n}\nfunction renderInnerContent(props) {\n    return (createElement(Fragment, null,\n        props.text && (createElement(\"a\", Object.assign({ id: props.textId, className: \"fc-list-day-text\" }, props.navLinkAttrs), props.text)),\n        props.sideText && ( /* not keyboard tabbable */createElement(\"a\", Object.assign({ \"aria-hidden\": true, className: \"fc-list-day-side-text\" }, props.sideNavLinkAttrs), props.sideText))));\n}\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    meridiem: 'short',\n});\nclass ListViewEventRow extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg, timeHeaderId, eventHeaderId, dateHeaderId } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TIME_FORMAT;\n        return (createElement(EventContainer, Object.assign({}, props, { elTag: \"tr\", elClasses: [\n                'fc-list-event',\n                seg.eventRange.def.url && 'fc-event-forced-url',\n            ], defaultGenerator: () => renderEventInnerContent(seg, context) /* weird */, seg: seg, timeText: \"\", disableDragging: true, disableResizing: true }), (InnerContent, eventContentArg) => (createElement(Fragment, null,\n            buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId),\n            createElement(\"td\", { \"aria-hidden\": true, className: \"fc-list-event-graphic\" },\n                createElement(\"span\", { className: \"fc-list-event-dot\", style: {\n                        borderColor: eventContentArg.borderColor || eventContentArg.backgroundColor,\n                    } })),\n            createElement(InnerContent, { elTag: \"td\", elClasses: ['fc-list-event-title'], elAttrs: { headers: `${eventHeaderId} ${dateHeaderId}` } })))));\n    }\n}\nfunction renderEventInnerContent(seg, context) {\n    let interactiveAttrs = getSegAnchorAttrs(seg, context);\n    return (createElement(\"a\", Object.assign({}, interactiveAttrs), seg.eventRange.def.title));\n}\nfunction buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId) {\n    let { options } = context;\n    if (options.displayEventTime !== false) {\n        let eventDef = seg.eventRange.def;\n        let eventInstance = seg.eventRange.instance;\n        let doAllDay = false;\n        let timeText;\n        if (eventDef.allDay) {\n            doAllDay = true;\n        }\n        else if (isMultiDayRange(seg.eventRange.range)) { // TODO: use (!isStart || !isEnd) instead?\n            if (seg.isStart) {\n                timeText = buildSegTimeText(seg, timeFormat, context, null, null, eventInstance.range.start, seg.end);\n            }\n            else if (seg.isEnd) {\n                timeText = buildSegTimeText(seg, timeFormat, context, null, null, seg.start, eventInstance.range.end);\n            }\n            else {\n                doAllDay = true;\n            }\n        }\n        else {\n            timeText = buildSegTimeText(seg, timeFormat, context);\n        }\n        if (doAllDay) {\n            let renderProps = {\n                text: context.options.allDayText,\n                view: context.viewApi,\n            };\n            return (createElement(ContentContainer, { elTag: \"td\", elClasses: ['fc-list-event-time'], elAttrs: {\n                    headers: `${timeHeaderId} ${dateHeaderId}`,\n                }, renderProps: renderProps, generatorName: \"allDayContent\", customGenerator: options.allDayContent, defaultGenerator: renderAllDayInner, classNameGenerator: options.allDayClassNames, didMount: options.allDayDidMount, willUnmount: options.allDayWillUnmount }));\n        }\n        return (createElement(\"td\", { className: \"fc-list-event-time\" }, timeText));\n    }\n    return null;\n}\nfunction renderAllDayInner(renderProps) {\n    return renderProps.text;\n}\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nclass ListView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.computeDateVars = memoize(computeDateVars);\n        this.eventStoreToSegs = memoize(this._eventStoreToSegs);\n        this.state = {\n            timeHeaderId: getUniqueDomId(),\n            eventHeaderId: getUniqueDomId(),\n            dateHeaderIdRoot: getUniqueDomId(),\n        };\n        this.setRootEl = (rootEl) => {\n            if (rootEl) {\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                });\n            }\n            else {\n                this.context.unregisterInteractiveComponent(this);\n            }\n        };\n    }\n    render() {\n        let { props, context } = this;\n        let { dayDates, dayRanges } = this.computeDateVars(props.dateProfile);\n        let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges);\n        return (createElement(ViewContainer, { elRef: this.setRootEl, elClasses: [\n                'fc-list',\n                context.theme.getClass('table'),\n                context.options.stickyHeaderDates !== false ?\n                    'fc-list-sticky' :\n                    '',\n            ], viewSpec: context.viewSpec },\n            createElement(Scroller, { liquid: !props.isHeightAuto, overflowX: props.isHeightAuto ? 'visible' : 'hidden', overflowY: props.isHeightAuto ? 'visible' : 'auto' }, eventSegs.length > 0 ?\n                this.renderSegList(eventSegs, dayDates) :\n                this.renderEmptyMessage())));\n    }\n    renderEmptyMessage() {\n        let { options, viewApi } = this.context;\n        let renderProps = {\n            text: options.noEventsText,\n            view: viewApi,\n        };\n        return (createElement(ContentContainer, { elTag: \"div\", elClasses: ['fc-list-empty'], renderProps: renderProps, generatorName: \"noEventsContent\", customGenerator: options.noEventsContent, defaultGenerator: renderNoEventsInner, classNameGenerator: options.noEventsClassNames, didMount: options.noEventsDidMount, willUnmount: options.noEventsWillUnmount }, (InnerContent) => (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-list-empty-cushion'] }))));\n    }\n    renderSegList(allSegs, dayDates) {\n        let { theme, options } = this.context;\n        let { timeHeaderId, eventHeaderId, dateHeaderIdRoot } = this.state;\n        let segsByDay = groupSegsByDay(allSegs); // sparse array\n        return (createElement(NowTimer, { unit: \"day\" }, (nowDate, todayRange) => {\n            let innerNodes = [];\n            for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n                let daySegs = segsByDay[dayIndex];\n                if (daySegs) { // sparse array, so might be undefined\n                    let dayStr = formatDayString(dayDates[dayIndex]);\n                    let dateHeaderId = dateHeaderIdRoot + '-' + dayStr;\n                    // append a day header\n                    innerNodes.push(createElement(ListViewHeaderRow, { key: dayStr, cellId: dateHeaderId, dayDate: dayDates[dayIndex], todayRange: todayRange }));\n                    daySegs = sortEventSegs(daySegs, options.eventOrder);\n                    for (let seg of daySegs) {\n                        innerNodes.push(createElement(ListViewEventRow, Object.assign({ key: dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */, seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: false, timeHeaderId: timeHeaderId, eventHeaderId: eventHeaderId, dateHeaderId: dateHeaderId }, getSegMeta(seg, todayRange, nowDate))));\n                    }\n                }\n            }\n            return (createElement(\"table\", { className: 'fc-list-table ' + theme.getClass('table') },\n                createElement(\"thead\", null,\n                    createElement(\"tr\", null,\n                        createElement(\"th\", { scope: \"col\", id: timeHeaderId }, options.timeHint),\n                        createElement(\"th\", { scope: \"col\", \"aria-hidden\": true }),\n                        createElement(\"th\", { scope: \"col\", id: eventHeaderId }, options.eventHint))),\n                createElement(\"tbody\", null, innerNodes)));\n        }));\n    }\n    _eventStoreToSegs(eventStore, eventUiBases, dayRanges) {\n        return this.eventRangesToSegs(sliceEventStore(eventStore, eventUiBases, this.props.dateProfile.activeRange, this.context.options.nextDayThreshold).fg, dayRanges);\n    }\n    eventRangesToSegs(eventRanges, dayRanges) {\n        let segs = [];\n        for (let eventRange of eventRanges) {\n            segs.push(...this.eventRangeToSegs(eventRange, dayRanges));\n        }\n        return segs;\n    }\n    eventRangeToSegs(eventRange, dayRanges) {\n        let { dateEnv } = this.context;\n        let { nextDayThreshold } = this.context.options;\n        let range = eventRange.range;\n        let allDay = eventRange.def.allDay;\n        let dayIndex;\n        let segRange;\n        let seg;\n        let segs = [];\n        for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n            segRange = intersectRanges(range, dayRanges[dayIndex]);\n            if (segRange) {\n                seg = {\n                    component: this,\n                    eventRange,\n                    start: segRange.start,\n                    end: segRange.end,\n                    isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n                    isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n                    dayIndex,\n                };\n                segs.push(seg);\n                // detect when range won't go fully into the next day,\n                // and mutate the latest seg to the be the end.\n                if (!seg.isEnd && !allDay &&\n                    dayIndex + 1 < dayRanges.length &&\n                    range.end <\n                        dateEnv.add(dayRanges[dayIndex + 1].start, nextDayThreshold)) {\n                    seg.end = range.end;\n                    seg.isEnd = true;\n                    break;\n                }\n            }\n        }\n        return segs;\n    }\n}\nfunction renderNoEventsInner(renderProps) {\n    return renderProps.text;\n}\nfunction computeDateVars(dateProfile) {\n    let dayStart = startOfDay(dateProfile.renderRange.start);\n    let viewEnd = dateProfile.renderRange.end;\n    let dayDates = [];\n    let dayRanges = [];\n    while (dayStart < viewEnd) {\n        dayDates.push(dayStart);\n        dayRanges.push({\n            start: dayStart,\n            end: addDays(dayStart, 1),\n        });\n        dayStart = addDays(dayStart, 1);\n    }\n    return { dayDates, dayRanges };\n}\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs) {\n    let segsByDay = []; // sparse array\n    let i;\n    let seg;\n    for (i = 0; i < segs.length; i += 1) {\n        seg = segs[i];\n        (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = []))\n            .push(seg);\n    }\n    return segsByDay;\n}\n\nvar css_248z = \":root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:\\\"\\\";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}\";\ninjectStyles(css_248z);\n\nexport { ListView };\n", "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { ListView } from './internal.js';\nimport { identity, createFormatter } from '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\n\nconst OPTION_REFINERS = {\n    listDayFormat: createFalsableFormatter,\n    listDaySideFormat: createFalsableFormatter,\n    noEventsClassNames: identity,\n    noEventsContent: identity,\n    noEventsDidMount: identity,\n    noEventsWillUnmount: identity,\n    // noEventsText is defined in base options\n};\nfunction createFalsableFormatter(input) {\n    return input === false ? null : createFormatter(input);\n}\n\nvar index = createPlugin({\n    name: '@fullcalendar/list',\n    optionRefiners: OPTION_REFINERS,\n    views: {\n        list: {\n            component: ListView,\n            buttonTextKey: 'list',\n            listDayFormat: { month: 'long', day: 'numeric', year: 'numeric' }, // like \"January 1, 2016\"\n        },\n        listDay: {\n            type: 'list',\n            duration: { days: 1 },\n            listDayFormat: { weekday: 'long' }, // day-of-week is all we need. full date is probably in headerToolbar\n        },\n        listWeek: {\n            type: 'list',\n            duration: { weeks: 1 },\n            listDayFormat: { weekday: 'long' },\n            listDaySideFormat: { month: 'long', day: 'numeric', year: 'numeric' },\n        },\n        listMonth: {\n            type: 'list',\n            duration: { month: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n        listYear: {\n            type: 'list',\n            duration: { year: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC1C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,QAAQ;AAAA,MACT,QAAQ,eAAe;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,SAAS,SAAS,QAAQ,IAAI,KAAK;AAChD,QAAI,EAAE,QAAQ,SAAS,WAAW,IAAI,KAAK;AAC3C,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,UAAU,YAAY,SAAS,UAAU;AAE7C,QAAI,OAAO,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,QAAQ,aAAa,IAAI;AAEpF,QAAI,WAAW,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,QAAQ,iBAAiB,IAAI;AAChG,QAAI,cAAc,OAAO,OAAO;AAAA,MAAE,MAAM,QAAQ,OAAO,OAAO;AAAA,MAAG,MAAM;AAAA,MAAS;AAAA,MAC5E;AAAA,MACA;AAAA,MAAU,cAAc,kBAAkB,KAAK,SAAS,OAAO;AAAA,MAAG,kBAAkB,kBAAkB,KAAK,SAAS,SAAS,OAAO,KAAK;AAAA,IAAE,GAAG,OAAO;AAEzJ,WAAQ,EAAc,kBAAkB,EAAE,OAAO,MAAM,WAAW;AAAA,MAC1D;AAAA,MACA,GAAG,iBAAiB,SAAS,KAAK;AAAA,IACtC,GAAG,SAAS;AAAA,MACR,aAAa,gBAAgB,OAAO;AAAA,IACxC,GAAG,aAA0B,eAAe,oBAAoB,iBAAiB,QAAQ,kBAAkB,kBAAkB,oBAAoB,oBAAoB,QAAQ,qBAAqB,UAAU,QAAQ,mBAAmB,aAAa,QAAQ,qBAAqB,GAAG,CAAC;AAAA;AAAA,MACzR;AAAA,QAAc;AAAA,QAAM,EAAE,OAAO,YAAY,SAAS,GAAG,IAAI,QAAQ,mBAAmB,OAAO;AAAA,QACvF,EAAc,cAAc,EAAE,OAAO,OAAO,WAAW;AAAA,UAC/C;AAAA,UACA,MAAM,SAAS,iBAAiB;AAAA,QACpC,EAAE,CAAC;AAAA,MAAC;AAAA,KAAE;AAAA,EAClB;AACJ;AACA,SAAS,mBAAmB,OAAO;AAC/B,SAAQ;AAAA,IAAc;AAAA,IAAU;AAAA,IAC5B,MAAM,QAAS,EAAc,KAAK,OAAO,OAAO,EAAE,IAAI,MAAM,QAAQ,WAAW,mBAAmB,GAAG,MAAM,YAAY,GAAG,MAAM,IAAI;AAAA,IACpI,MAAM;AAAA,IAAyC,EAAc,KAAK,OAAO,OAAO,EAAE,eAAe,MAAM,WAAW,wBAAwB,GAAG,MAAM,gBAAgB,GAAG,MAAM,QAAQ;AAAA,EAAE;AAC9L;AAEA,IAAM,sBAAsB,gBAAgB;AAAA,EACxC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACd,CAAC;AACD,IAAM,mBAAN,cAA+B,cAAc;AAAA,EACzC,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,EAAE,KAAK,cAAc,eAAe,aAAa,IAAI;AACzD,QAAI,aAAa,QAAQ,mBAAmB;AAC5C,WAAQ,EAAc,gBAAgB,OAAO,OAAO,CAAC,GAAG,OAAO,EAAE,OAAO,MAAM,WAAW;AAAA,MACjF;AAAA,MACA,IAAI,WAAW,IAAI,OAAO;AAAA,IAC9B,GAAG,kBAAkB,MAAM,wBAAwB,KAAK,OAAO,GAAe,KAAU,UAAU,IAAI,iBAAiB,MAAM,iBAAiB,KAAK,CAAC,GAAG,CAAC,cAAc,oBAAqB;AAAA,MAAc;AAAA,MAAU;AAAA,MACnN,iBAAiB,KAAK,YAAY,SAAS,cAAc,YAAY;AAAA,MACrE;AAAA,QAAc;AAAA,QAAM,EAAE,eAAe,MAAM,WAAW,wBAAwB;AAAA,QAC1E,EAAc,QAAQ,EAAE,WAAW,qBAAqB,OAAO;AAAA,UACvD,aAAa,gBAAgB,eAAe,gBAAgB;AAAA,QAChE,EAAE,CAAC;AAAA,MAAC;AAAA,MACZ,EAAc,cAAc,EAAE,OAAO,MAAM,WAAW,CAAC,qBAAqB,GAAG,SAAS,EAAE,SAAS,GAAG,aAAa,IAAI,YAAY,GAAG,EAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EACpJ;AACJ;AACA,SAAS,wBAAwB,KAAK,SAAS;AAC3C,MAAI,mBAAmB,kBAAkB,KAAK,OAAO;AACrD,SAAQ,EAAc,KAAK,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,IAAI,WAAW,IAAI,KAAK;AAC5F;AACA,SAAS,iBAAiB,KAAK,YAAY,SAAS,cAAc,cAAc;AAC5E,MAAI,EAAE,QAAQ,IAAI;AAClB,MAAI,QAAQ,qBAAqB,OAAO;AACpC,QAAI,WAAW,IAAI,WAAW;AAC9B,QAAI,gBAAgB,IAAI,WAAW;AACnC,QAAI,WAAW;AACf,QAAI;AACJ,QAAI,SAAS,QAAQ;AACjB,iBAAW;AAAA,IACf,WACS,gBAAgB,IAAI,WAAW,KAAK,GAAG;AAC5C,UAAI,IAAI,SAAS;AACb,mBAAW,iBAAiB,KAAK,YAAY,SAAS,MAAM,MAAM,cAAc,MAAM,OAAO,IAAI,GAAG;AAAA,MACxG,WACS,IAAI,OAAO;AAChB,mBAAW,iBAAiB,KAAK,YAAY,SAAS,MAAM,MAAM,IAAI,OAAO,cAAc,MAAM,GAAG;AAAA,MACxG,OACK;AACD,mBAAW;AAAA,MACf;AAAA,IACJ,OACK;AACD,iBAAW,iBAAiB,KAAK,YAAY,OAAO;AAAA,IACxD;AACA,QAAI,UAAU;AACV,UAAI,cAAc;AAAA,QACd,MAAM,QAAQ,QAAQ;AAAA,QACtB,MAAM,QAAQ;AAAA,MAClB;AACA,aAAQ,EAAc,kBAAkB,EAAE,OAAO,MAAM,WAAW,CAAC,oBAAoB,GAAG,SAAS;AAAA,QAC3F,SAAS,GAAG,YAAY,IAAI,YAAY;AAAA,MAC5C,GAAG,aAA0B,eAAe,iBAAiB,iBAAiB,QAAQ,eAAe,kBAAkB,mBAAmB,oBAAoB,QAAQ,kBAAkB,UAAU,QAAQ,gBAAgB,aAAa,QAAQ,kBAAkB,CAAC;AAAA,IAC1Q;AACA,WAAQ,EAAc,MAAM,EAAE,WAAW,qBAAqB,GAAG,QAAQ;AAAA,EAC7E;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,aAAa;AACpC,SAAO,YAAY;AACvB;AAKA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACjC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,kBAAkB,QAAQ,eAAe;AAC9C,SAAK,mBAAmB,QAAQ,KAAK,iBAAiB;AACtD,SAAK,QAAQ;AAAA,MACT,cAAc,eAAe;AAAA,MAC7B,eAAe,eAAe;AAAA,MAC9B,kBAAkB,eAAe;AAAA,IACrC;AACA,SAAK,YAAY,CAAC,WAAW;AACzB,UAAI,QAAQ;AACR,aAAK,QAAQ,6BAA6B,MAAM;AAAA,UAC5C,IAAI;AAAA,QACR,CAAC;AAAA,MACL,OACK;AACD,aAAK,QAAQ,+BAA+B,IAAI;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,UAAU,UAAU,IAAI,KAAK,gBAAgB,MAAM,WAAW;AACpE,QAAI,YAAY,KAAK,iBAAiB,MAAM,YAAY,MAAM,cAAc,SAAS;AACrF,WAAQ;AAAA,MAAc;AAAA,MAAe,EAAE,OAAO,KAAK,WAAW,WAAW;AAAA,QACjE;AAAA,QACA,QAAQ,MAAM,SAAS,OAAO;AAAA,QAC9B,QAAQ,QAAQ,sBAAsB,QAClC,mBACA;AAAA,MACR,GAAG,UAAU,QAAQ,SAAS;AAAA,MAC9B,EAAc,UAAU,EAAE,QAAQ,CAAC,MAAM,cAAc,WAAW,MAAM,eAAe,YAAY,UAAU,WAAW,MAAM,eAAe,YAAY,OAAO,GAAG,UAAU,SAAS,IAClL,KAAK,cAAc,WAAW,QAAQ,IACtC,KAAK,mBAAmB,CAAC;AAAA,IAAC;AAAA,EACtC;AAAA,EACA,qBAAqB;AACjB,QAAI,EAAE,SAAS,QAAQ,IAAI,KAAK;AAChC,QAAI,cAAc;AAAA,MACd,MAAM,QAAQ;AAAA,MACd,MAAM;AAAA,IACV;AACA,WAAQ,EAAc,kBAAkB,EAAE,OAAO,OAAO,WAAW,CAAC,eAAe,GAAG,aAA0B,eAAe,mBAAmB,iBAAiB,QAAQ,iBAAiB,kBAAkB,qBAAqB,oBAAoB,QAAQ,oBAAoB,UAAU,QAAQ,kBAAkB,aAAa,QAAQ,oBAAoB,GAAG,CAAC,iBAAkB,EAAc,cAAc,EAAE,OAAO,OAAO,WAAW,CAAC,uBAAuB,EAAE,CAAC,CAAE;AAAA,EAC9c;AAAA,EACA,cAAc,SAAS,UAAU;AAC7B,QAAI,EAAE,OAAO,QAAQ,IAAI,KAAK;AAC9B,QAAI,EAAE,cAAc,eAAe,iBAAiB,IAAI,KAAK;AAC7D,QAAI,YAAY,eAAe,OAAO;AACtC,WAAQ,EAAc,UAAU,EAAE,MAAM,MAAM,GAAG,CAAC,SAAS,eAAe;AACtE,UAAI,aAAa,CAAC;AAClB,eAAS,WAAW,GAAG,WAAW,UAAU,QAAQ,YAAY,GAAG;AAC/D,YAAI,UAAU,UAAU,QAAQ;AAChC,YAAI,SAAS;AACT,cAAI,SAAS,gBAAgB,SAAS,QAAQ,CAAC;AAC/C,cAAI,eAAe,mBAAmB,MAAM;AAE5C,qBAAW,KAAK,EAAc,mBAAmB,EAAE,KAAK,QAAQ,QAAQ,cAAc,SAAS,SAAS,QAAQ,GAAG,WAAuB,CAAC,CAAC;AAC5I,oBAAU,cAAc,SAAS,QAAQ,UAAU;AACnD,mBAAS,OAAO,SAAS;AACrB,uBAAW,KAAK,EAAc,kBAAkB,OAAO,OAAO,EAAE,KAAK,SAAS,MAAM,IAAI,WAAW,SAAS,YAAsD,KAAU,YAAY,OAAO,YAAY,OAAO,iBAAiB,OAAO,YAAY,OAAO,cAA4B,eAA8B,aAA2B,GAAG,WAAW,KAAK,YAAY,OAAO,CAAC,CAAC,CAAC;AAAA,UAC/X;AAAA,QACJ;AAAA,MACJ;AACA,aAAQ;AAAA,QAAc;AAAA,QAAS,EAAE,WAAW,mBAAmB,MAAM,SAAS,OAAO,EAAE;AAAA,QACnF;AAAA,UAAc;AAAA,UAAS;AAAA,UACnB;AAAA,YAAc;AAAA,YAAM;AAAA,YAChB,EAAc,MAAM,EAAE,OAAO,OAAO,IAAI,aAAa,GAAG,QAAQ,QAAQ;AAAA,YACxE,EAAc,MAAM,EAAE,OAAO,OAAO,eAAe,KAAK,CAAC;AAAA,YACzD,EAAc,MAAM,EAAE,OAAO,OAAO,IAAI,cAAc,GAAG,QAAQ,SAAS;AAAA,UAAC;AAAA,QAAC;AAAA,QACpF,EAAc,SAAS,MAAM,UAAU;AAAA,MAAC;AAAA,IAChD,CAAC;AAAA,EACL;AAAA,EACA,kBAAkB,YAAY,cAAc,WAAW;AACnD,WAAO,KAAK,kBAAkB,gBAAgB,YAAY,cAAc,KAAK,MAAM,YAAY,aAAa,KAAK,QAAQ,QAAQ,gBAAgB,EAAE,IAAI,SAAS;AAAA,EACpK;AAAA,EACA,kBAAkB,aAAa,WAAW;AACtC,QAAI,OAAO,CAAC;AACZ,aAAS,cAAc,aAAa;AAChC,WAAK,KAAK,GAAG,KAAK,iBAAiB,YAAY,SAAS,CAAC;AAAA,IAC7D;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,YAAY,WAAW;AACpC,QAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,QAAI,EAAE,iBAAiB,IAAI,KAAK,QAAQ;AACxC,QAAI,QAAQ,WAAW;AACvB,QAAI,SAAS,WAAW,IAAI;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,CAAC;AACZ,SAAK,WAAW,GAAG,WAAW,UAAU,QAAQ,YAAY,GAAG;AAC3D,iBAAW,gBAAgB,OAAO,UAAU,QAAQ,CAAC;AACrD,UAAI,UAAU;AACV,cAAM;AAAA,UACF,WAAW;AAAA,UACX;AAAA,UACA,OAAO,SAAS;AAAA,UAChB,KAAK,SAAS;AAAA,UACd,SAAS,WAAW,WAAW,SAAS,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ;AAAA,UAChF,OAAO,WAAW,SAAS,SAAS,IAAI,QAAQ,MAAM,MAAM,IAAI,QAAQ;AAAA,UACxE;AAAA,QACJ;AACA,aAAK,KAAK,GAAG;AAGb,YAAI,CAAC,IAAI,SAAS,CAAC,UACf,WAAW,IAAI,UAAU,UACzB,MAAM,MACF,QAAQ,IAAI,UAAU,WAAW,CAAC,EAAE,OAAO,gBAAgB,GAAG;AAClE,cAAI,MAAM,MAAM;AAChB,cAAI,QAAQ;AACZ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,oBAAoB,aAAa;AACtC,SAAO,YAAY;AACvB;AACA,SAAS,gBAAgB,aAAa;AAClC,MAAI,WAAW,WAAW,YAAY,YAAY,KAAK;AACvD,MAAI,UAAU,YAAY,YAAY;AACtC,MAAI,WAAW,CAAC;AAChB,MAAI,YAAY,CAAC;AACjB,SAAO,WAAW,SAAS;AACvB,aAAS,KAAK,QAAQ;AACtB,cAAU,KAAK;AAAA,MACX,OAAO;AAAA,MACP,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC5B,CAAC;AACD,eAAW,QAAQ,UAAU,CAAC;AAAA,EAClC;AACA,SAAO,EAAE,UAAU,UAAU;AACjC;AAEA,SAAS,eAAe,MAAM;AAC1B,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACjC,UAAM,KAAK,CAAC;AACZ,KAAC,UAAU,IAAI,QAAQ,MAAM,UAAU,IAAI,QAAQ,IAAI,CAAC,IACnD,KAAK,GAAG;AAAA,EACjB;AACA,SAAO;AACX;AAEA,IAAI,WAAW;AACf,aAAa,QAAQ;;;ACnQrB,IAAM,kBAAkB;AAAA,EACpB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA;AAEzB;AACA,SAAS,wBAAwB,OAAO;AACpC,SAAO,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AACzD;AAEA,IAAI,QAAQ,aAAa;AAAA,EACrB,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,OAAO;AAAA,IACH,MAAM;AAAA,MACF,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe,EAAE,OAAO,QAAQ,KAAK,WAAW,MAAM,UAAU;AAAA;AAAA,IACpE;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU,EAAE,MAAM,EAAE;AAAA,MACpB,eAAe,EAAE,SAAS,OAAO;AAAA;AAAA,IACrC;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,MACrB,eAAe,EAAE,SAAS,OAAO;AAAA,MACjC,mBAAmB,EAAE,OAAO,QAAQ,KAAK,WAAW,MAAM,UAAU;AAAA,IACxE;AAAA,IACA,WAAW;AAAA,MACP,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,MACrB,mBAAmB,EAAE,SAAS,OAAO;AAAA;AAAA,IACzC;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,UAAU,EAAE,MAAM,EAAE;AAAA,MACpB,mBAAmB,EAAE,SAAS,OAAO;AAAA;AAAA,IACzC;AAAA,EACJ;AACJ,CAAC;", "names": []}