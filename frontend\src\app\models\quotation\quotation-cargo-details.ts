import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

export class QuotationCargoDetail extends BaseModel {
    tenantId!: number;
    slug!: string;
    description!: string;
    cargoType!: string;
    weight!: number;
    volume!: number;
    length!: number;
    freight: number = 0;
    gst: number = 0;
    total: number = 0;
    weightInPounds!: number;
    fuelCharges: number = 0;
    height!: number;
    freightRate!: number;
    weightType!: string;
    width!: number;
    quotation!: string;
    quantity: number = 1;
    rateType: string = 'WEIGHT';

    constructor(data?: Partial<QuotationCargoDetail>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): QuotationCargoDetail {
        return new QuotationCargoDetail(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        return this;
    }
}
