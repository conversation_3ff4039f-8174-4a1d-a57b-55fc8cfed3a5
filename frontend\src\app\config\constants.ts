export const Constant = {
    MASKS: {
        PHONE_MASK: '(*************',
    },

    ALERT_MESSAGES: {
        deActivateMsg: 'Are you sure you want to deactivate this',
        activateMsg: 'Are you sure you want to activate this',
        deleteMsg: 'Would you like to delete this?',
        confirmShipmentCompletionMsg: 'Are you sure you want to mark this shipment as Completed?',
        contactUsStatusUpdate: 'Are you sure you want to update the Contact Us status?',
    },

    PATTERNS: {
        EMAIL_VALIDATOR_PATTERN: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$',
        PHONE_NUMBER_VALIDATOR: /^.{9,12}$/,
        EMAIL_PATTERN_REGEX_TYPE: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
        VALID_CHAR_REGEX: /^[a-zA-Z0-9\s]$/,
        WEBSITE_VALIDATOR: '^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.[a-zA-Z]{2,})(\.[a-zA-Z]{2,})?(\/[^\s]*)?$',
    },

    ROLES: [
        { id: 'ROLE_OFFICE_ADMIN', name: 'Office Admin' },
        { id: 'ROLE_SUPER_ADMIN', name: 'Super Admin' },
        { id: 'ROLE_DRIVER', name: 'Driver' },
        { id: 'ROLE_CUSTOMER', name: 'Customer' },
    ],

    EMPLOYEE_ROLES: [
        { id: 'ROLE_OFFICE_ADMIN', name: 'Admin' },
        { id: 'ROLE_DRIVER', name: 'Driver' },
    ],

    COUNTRY_CODES: [
        {
            name: 'Canada',
            dial_code: '+1',
            code: 'CA',
            countryValue: '+1-CA',
            countryLabel: '+1 (CA)',
        },
        {
            name: 'United States',
            dial_code: '+1',
            code: 'US',
            countryValue: '+1-US',
            countryLabel: '+1 (US)',
        },
    ],

    RESOURCE_TYPE: {
        EMPLOYEE: 'Employee',
        CUSTOMER: 'Customer',
        DRIVER: 'DRIVER',
        WORK_ORDER: 'WORK_ORDER',
        RATE_SHEET: 'RateSheet',
        RATE_SHEET_WEIGHT_CHARGES: 'RateSheet Weight Change',
        SHIPMENT: 'Shipment',
        SHIPMENT_CARGO: 'Shipment Cargo',
        VEHICLE: 'Vehicle',
        QUOTATIONS: 'Quotations',
        BARCODES: 'Barcodes',
        FUELRECEIPT: 'Fuel Receipt',
        CONTACTS: 'Contacts'
    },

    ERROR_MESSAGES: {
        password:
            'Password must contain at least one capital letter, one number, one special character and must be longer than 8 characters',
    },

    FILE_ACCEPT_TYPES: '.txt, .pdf, .doc, .docx, .jpg, .jpeg, .png',

    EXPORT_ENTITY_COLUMNS: {
        CUSTOMER: [
            'Company Name',
            'Email',
            'Account Payable Email',
            'Phone Number',
            'Address',
            'City',
            'Province',
            'Status',
            'Created On',
        ],
        EMPLOYEES: ['Name', 'Email', 'Phone Number', 'Address', 'Position', 'Status', 'Hire Date', 'Created On'],
        RATE_SHEET_LIST: ['Rate Sheet #', 'Pickup Address', 'Delivery Address', 'Start Date', 'End Date', 'Created On'],
        FUEL_RECEPIT: ["Driver Name", "Vehicle Number", "Meter Reading(in KM)", "Fuel(in LTR.)", "Fuel Cost(in $)", "Fuel Type", "Created On"],
        RATE_SHEET_WEIGHT_CHARGES: [
            'Rate Type',
            'From Weight',
            'To Weight',
            'Freight',
            'Fuel Charges',
            'Gst Amount',
            'Total',
        ],
        SHIPMENT: [
            'Ref.Id',
            'Customer',
            'Driver Name',
            'Shipment Type',
            'Rate Sheet',
            'Pickup City',
            'Delivery City',
            'BarCode',
            'Shipment Status',
            'Payment Type',
            'Payment Status',
            'Total',
            'Etd',
            'Created On',
        ],
        QUOTATION: [
            'Ref.Id',
            'Customer',
            'Contact Name',
            'Contact Phone',
            'Pickup City',
            'Delivery City',
            'Status',
            'Created On',
        ],
        BARCODE: ['Barcode No.', 'Barcode'],
        CONTACTS: ["Company Name", "Person Name", "Contact No.", "Email", "Subject", "Message", "Status", "Created On"],
    },

    ORDERING_ENTITY_COLUMNS: {
        CUSTOMER: [
            'Name',
            'Email',
            'Payable Email',
            'Phone Number',
            'Address',
            'City',
            'State',
            'Status',
            'Created On',
        ],
        EMPLOYEES: ['Name', 'Email', 'Phone Number', 'Address', 'Role', 'Status', 'Hire Date', 'Created On'],
        RATE_SHEET_LIST: ['Name', 'Pickup Address', 'Delivery Address', 'Start Date', 'End Date', 'Created On'],
        FUEL_RECEPIT: ['Driver', 'Vehicle', 'Meter Reading', 'Fuel quantity', 'Fuel Cost', 'Fuel Type', 'Created On'],
        RATE_SHEET_WEIGHT_CHARGES_LIST: [
            'Rate Type',
            'From Weight',
            'To Weight',
            'Freight',
            'Fuel Charges',
            'GST',
            'Total',
        ],
        SHIPMENT: ['RI', 'C', 'D', 'ST', 'RS', 'PA', 'DA', 'BN', 'S', 'PT', 'PS', 'T', 'ED', 'CREATED'],
        SHIPMENT_ITEMS: ['Description', 'Cargo Type', 'Weight', 'Volume', 'Freight', 'Fuel Charges', 'Gst', 'Total'],
        QUOTATION: [
            'RefID',
            'Customer',
            'ContactPersonName',
            'ContactPersonPhone',
            'PickupAddress',
            'DeliveryAddress',
            'Status',
            'CreateOn',
        ],
        VEHICLE: ['Name', 'Created On'],
        BARCODE: ['BarcodeNo', 'RefID', 'Driver', 'Status', 'PrintStatus', 'CreatedOn'],
        CONTACTS: ["CompanyName", "Name", "Phone", "Email", "Subject", "Message", "Status", "CreatedOn"],
    },

    STATUS: [
        { id: 'ACTIVE', name: 'Active' },
        { id: 'INACTIVE', name: 'Inactive' },
    ],

    CONTACT_US_STATUS: [
        { id: "PENDING", name: "Pending" },
        { id: "IN_PROGRESS", name: "In Progress" },
        { id: "COMPLETED", name: "Completed" },
        { id: "CANCELLED", name: "Cancelled" },
    ],

    DURATION_FILTERS: [
        { id: 30, name: '30 Days' },
        { id: 60, name: '60 Days' },
        { id: 90, name: '90 Days' },
        { id: 180, name: '180 Days' },
        { id: 1, name: '1 Year' },
        { id: 'AllData', name: 'All Data' },
    ],
    BARCODE_STATUS: [
        { id: 'USED', name: 'USED' },
        { id: 'UNUSED', name: 'UNUSED' },
    ],

    BARCODE_PRINTED_STATUS: [
        { id: 'PRINTED', name: 'Printed' },
        { id: 'NON_PRINTED', name: 'Not Printed' },
        { id: 'ALL', name: 'All' },
    ],

    SHIPMENT_TYPE_OPTIONS: [
        { id: 'RUSH', name: 'Rush' },
        { id: 'FLAT_DECK', name: 'Flat Deck' },
        { id: 'TRUCK_&_TRAILER', name: 'Truck & Trailer' },
        { id: 'REGULAR', name: 'Regular' },
    ],

    SHIPMENT_STATUS_OPTIONS: [
        { id: 'NEW', name: 'New' },
        { id: 'ASSIGNED', name: 'Assigned' },
        { id: 'IN_TRANSIT', name: 'In Transit' },
        { id: 'DELIVERED', name: 'Delivered' },
        { id: 'COMPLETED', name: 'Completed' },
    ],

    SHIPMENT_STATUS_AFTER_DELIVERY: [
        { id: 'DELIVERED', name: 'Delivered' },
        { id: 'COMPLETED', name: 'Completed' },
    ],

    SHIPMENT_PAYMENT_TYPE_OPTIONS: [
        { id: 'PREPAID', name: 'Prepaid' },
        { id: 'COLLECT', name: 'Collect' },
    ],

    SHIPMENT_PAYMENT_STATUS_OPTIONS: [
        { id: 'PENDING', name: 'Pending' },
        { id: 'INVOICED', name: 'Invoiced' },
    ],

    CARGO_TYPE_OPTIONS: [
        { id: 'BOX', name: 'Box' },
        { id: 'PALLET', name: 'Pallet' },
        { id: 'CRATE', name: 'Crate' },
        { id: 'DRUM', name: 'Drum' },
        { id: 'BAG', name: 'Bag' },
        { id: 'SACK', name: 'Sack' },
        { id: 'ROLL', name: 'Roll' },
        { id: 'BUNDLE', name: 'Bundle' },
        { id: 'CASE', name: 'Case' },
        { id: 'BIN', name: 'Bin' },
        { id: 'HALF_SKID', name: 'Half Skid' },
        { id: 'FULL_SKID', name: 'Full Skid' },
        { id: 'CONTAINER', name: 'Container' },
        { id: 'CAGE', name: 'Cage' },
        { id: 'TRAY', name: 'Tray' },
        { id: 'CART', name: 'Cart' },
    ],

    WEIGHT_TYPE_OPTIONS: [
        {
            id: 'LBS',
            name: 'Lbs',
        },
        {
            id: 'KGS',
            name: 'Kgs',
        },
    ],

    STATUS_OPTIONS: [
        {
            id: 'NEW',
            name: 'New',
        },
        {
            id: 'DRIVER_ASSIGNED',
            name: 'Driver Assigned',
        },
        {
            id: 'IN_PROGRESS',
            name: 'In Progress',
        },
        {
            id: 'DELIVERED',
            name: 'Delivered',
        },
    ],

    ORDER_SEQUENCE: ['asc', 'desc'],

    EXPORT_TO_EXCEL: { CONTENT_TYPE: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
    EXPORT_TO_PDF: { CONTENT_TYPE: 'application/pdf' },

    PAYMENT_STATUS: [
        {
            id: 'PENDING',
            name: 'Shipment.PAYMENTSTATUS_PENDING',
        },
        {
            id: 'INVOICED',
            name: 'Shipment.PAYMENTSTATUS_INVOICED',
        },
        {
            id: 'RECEIVED',
            name: 'Shipment.PAYMENTSTATUS_RECEIVED',
        },
    ],

    MY_DATE_PICKER: {
        DATE_TYPE: 'dd/mm/yyyy',
    },

    CUSTOMER_STATUS: [
        { id: true, name: 'Active' },
        { id: false, name: 'Inactive' },
    ],

    RATE_TYPES: [
        { id: 'WEIGHT', name: 'Weight' },
        { id: 'VOLUME', name: 'Volume' },
        { id: 'FULL_SKID', name: 'Full Skid' },
        { id: 'HALF_SKID', name: 'Half Skid' },
    ],

    QUOTATION_STATUS: [
        { id: 'REQUESTED', name: 'Requested' },
        { id: 'CLIENT_APPROVAL', name: 'Client Approval' },
        { id: 'CONFIRMED', name: 'Confirmed' },
        { id: 'REJECTED', name: 'Rejected' },
    ],
};
