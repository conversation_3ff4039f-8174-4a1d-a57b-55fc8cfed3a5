<div class="Shipment-Tab-menu">
    <app-tab-menu [stepperList]="stepperList" (updatedStep)="onSelectionCallBack($event)"
        [lastStoredStep]="this.quotation.step > 2 ? 8:this.quotation.step"></app-tab-menu>
</div>

<ng-container [ngSwitch]="selectedStep">
    <app-quotation-basic-info *ngSwitchCase="1" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveQuotationStep()" [quotation]="quotation"
        (onNextClick)="tabMenuUpdate($event,true)" (onfetchDropdownDataCompleted)="onfetchDropdownDataCompleted($event)"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-quotation-basic-info>

    <app-quotation-pickup-delivery *ngSwitchCase="2" [onClickValidation]="onClickValidation" [filterParam]="filterParam"
        (saveButtonClicked)="saveQuotationStep()" [quotation]="quotation" (onNextClick)="tabMenuUpdate($event,true)"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-quotation-pickup-delivery>

    <app-quotation-cargo-details *ngSwitchCase="3" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" [quotation]="quotation"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-quotation-cargo-details>

    <app-quotation-special-request *ngSwitchCase="4" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveQuotationStep()" [quotation]="quotation"
        (onNextClick)="tabMenuUpdate($event,true)" [rateSheetId]="quotation.rateSheet"
        (onNextOrBackClick)="tabMenuUpdate($event,false)">
    </app-quotation-special-request>

    <app-quotation-customer-calculations *ngSwitchCase="5" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" [quotation]="quotation"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-quotation-customer-calculations>

    <app-quotation-documents *ngSwitchCase="6" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" [quotation]="quotation" (saveButtonClicked)="saveQuotationStep()"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-quotation-documents>

</ng-container>