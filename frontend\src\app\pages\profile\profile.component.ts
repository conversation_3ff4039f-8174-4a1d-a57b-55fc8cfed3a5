// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Angular router modules
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { provideNgxMask } from 'ngx-mask';

// Base components
import { BaseEditComponent } from '../../config/base.edit.component';

// Models
import { RestResponse } from '../../models/common/auth.model';
import { FilterParam } from '../../models/common/filter-param';

// Services
import { LoadingService } from '../../services/loading.service';

// Shared utilities and services
import { CommonUtil } from '../../shared/common.util';
import { AuthService } from '../../shared/services/auth.services';
import { CommonService } from '../../shared/services/common.service';
import { ToastService } from '../../shared/services/toast.service';

// UI Components
import { User } from '../../models/access/user';
import { DialCodeInputComponent } from '../../shared/common-component/dial-code-input/dial-code-input.component';
import { ValidationMessageComponent } from '../../shared/common-component/validation-message/validation-message.component';
import { RippleEffectDirective } from '../../shared/directives/ripple-effect.directive';
import { LocalStorageService } from '../../shared/services/local-storage.service';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { EmployeesManager } from '../layout/employees/employees.manager';

@Component({
    selector: 'app-profile',
    standalone: true,
    imports: [
        CommonModule,
        TranslateModule,
        DataTablesModule,
        ChangePasswordComponent,
        CommonModule,
        DataTablesModule,

        TranslateModule,
        FormsModule,
        NgSelectModule,
        DialCodeInputComponent,
        RouterLink,
        TranslateModule,
        ValidationMessageComponent,
        RippleEffectDirective,
    ],
    providers: [provideNgxMask()],
    templateUrl: './profile.component.html',
    styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent extends BaseEditComponent implements OnInit {
    user: User;
    tabView!: string;

    override request: any;

    override filterParam!: FilterParam;
    override onClickValidation!: boolean;

    constructor(
        public commonUtil: CommonUtil,
        protected employeesManager: EmployeesManager,
        protected override route: ActivatedRoute,
        public override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
        protected authService: AuthService,
        private readonly localStorageService: LocalStorageService,
    ) {
        super(employeesManager, commonService, toastService, loadingService, route, router, translateService);
        this.user = new User();

        this.request = {} as any;
        this.request.recordId = this.route.snapshot.paramMap.get('id');
        this.request.isNewRecord = true;
        this.request.onClickValidation = false;
        this.filterParam = new FilterParam();
        this.tabView = 'BASIC_INFO';
    }

    ngOnInit() {
        this.fetchMyProfileDetails();
    }

    fetchMyProfileDetails() {
        this.loadingService.show();
        this.employeesManager.fetchMyProfileDetails().subscribe({
            next: (response: RestResponse) => {
                this.user = response.data;
                this.loadingService.hide();
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    onClickTab(tabType: string) {
        this.tabView = tabType;
    }

    updateMyProfile(form: any) {
        this.onClickValidation = !form.valid;
        if (!form.valid) {
            return;
        }

        this.loadingService.show();
        this.employeesManager.updateMyProfile(this.user).subscribe({
            next: (response) => {
                this.loadingService.hide();
                const user = JSON.parse(this.localStorageService.get('user') || '{}') as User;

                const { firstName, lastName, phoneNumber } = this.user;
                user.firstName = firstName;
                user.lastName = lastName;
                user.phoneNumber = phoneNumber;
                user.fullName = firstName + ' ' + lastName;
                this.authService.updateUser(user);

                this.toastService.success(response.message);
            },

            // Handle errors that occur during the request.
            error: (error: any) => {
                this.loadingService.hide();
                this.toastService.error(error.title);
            },
        });
    }
}
