// Angular core and common modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Quotation } from '../../../../../models/quotation/quotation';

// Services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared and custom components

// Shared directives
import { AllowNumberOnlyDirective } from '../../../../../shared/directives/allow-number-only.directive';
import { CurrencyFormatterDirective } from '../../../../../shared/directives/custom-currency.directive';

// Managers
import { CustomerManager } from '../../../customer/customer.manager';
import { EmployeesManager } from '../../../employees/employees.manager';
import { RateSheetManager } from '../../../ratesheet/rate-sheet.manager';
import { ShipmentManager } from '../../../shipment/shipment.manager';
import { VehicleManager } from '../../../vehicle/vehicle.manager';

@Component({
    selector: 'app-quotation-customer-calculations',
    standalone: true,
    imports: [
        AllowNumberOnlyDirective,
        CommonModule,
        CurrencyFormatterDirective,
        FormsModule,
        NgSelectModule,
        NgxMaskDirective,
        TranslateModule,
    ],
    templateUrl: './quotation-customer-calculations.component.html',
    styleUrl: './quotation-customer-calculations.component.scss',
    providers: [provideNgxMask()],
})
export class QuotationCustomerCalculationsComponent {
    @Input() quotation!: Quotation;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();
    @Output() onfetchDropdownDataCompleted = new EventEmitter<boolean>();

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected customerManager: CustomerManager,
        protected shipmentManager: ShipmentManager,
        protected vehicleManager: VehicleManager,
        protected employeesManager: EmployeesManager,
        protected rateSheetManager: RateSheetManager,
        protected typeAheadService: TypeAheadService,
        protected modalService: NgbModal,
    ) { }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        const areAllControlsDisabled = Object.values(form.controls).every((control: any) => control.disabled);

        if (!isFormValid && !areAllControlsDisabled) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.onNextOrBackClick.emit(6);
    }

    save(form: any) {
        const isFormValid = form.valid;
        const areAllControlsDisabled = Object.values(form.controls).every((c: any) => c.disabled);

        if (!isFormValid && !areAllControlsDisabled) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.router.navigate(['/dashboard/quotations']);
    }

    onBack() {
        this.onNextOrBackClick.emit(4);
    }
}
