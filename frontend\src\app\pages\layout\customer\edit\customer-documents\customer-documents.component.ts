// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';

// Third-party modules
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Customer } from '../../../../../models/customer/customer';

// Custom components
import { FileUploaderComponent } from '../../../../../shared/common-component/file-uploader/file-uploader.component';

@Component({
    selector: 'app-customer-documents',
    standalone: true,
    imports: [FormsModule, TranslateModule, CommonModule, FileUploaderComponent, RouterModule],
    templateUrl: './customer-documents.component.html',
    styleUrl: './customer-documents.component.scss',
})
export class CustomerDocumentsComponent {
    @Input() customer!: Customer;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    customerStatus = Constant.CUSTOMER_STATUS;
    uploaderVisibility: boolean = true;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['customer']?.currentValue?.id) {
            this.uploaderVisibility = false;
            setTimeout(() => {
                this.uploaderVisibility = true;
            });
        }
    }

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected route: ActivatedRoute,
        protected loadingService: LoadingService,
    ) { }

    onNext(form: any) {
        if (this.isFormInvalid(form)) return;
        this.onNextClick.emit(4);
    }

    onBack() {
        this.onNextOrBackClick.emit(2);
    }

    save(form: any) {
        if (this.isFormInvalid(form)) return;
        this.saveButtonClicked.emit();
    }

    private isFormInvalid(form: any): boolean {
        if (form.valid) return false;
        this.onClickValidation = true;
        return true;
    }
}
