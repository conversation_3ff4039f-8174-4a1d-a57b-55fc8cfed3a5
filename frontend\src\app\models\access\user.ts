import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

import { AuthToken } from './auth-token';

export class User extends BaseModel {
    countryCode!: string;
    email!: string;
    emailConfirmed!: boolean;
    firstName!: string;
    fullName!: string;
    lastName!: string;
    originCountry!: string;
    phoneNumber!: string;
    roles!: string[];
    tenantId!: number;
    userName!: string;
    uniqueCode!: string;

    override isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return;
    }

    override forRequest() {
        return;
    }
}

export class ApiResponse {
    token?: AuthToken;
    user?: User;
}
