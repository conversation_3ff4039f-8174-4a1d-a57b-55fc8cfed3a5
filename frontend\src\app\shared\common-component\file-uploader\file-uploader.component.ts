// Angular core imports
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

// Angular common module imports
import { CommonModule } from '@angular/common';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Third-party library imports
import { FileUploadModule, FileUploader, Headers } from '@augwit/ng2-file-upload';

// Project-specific components
import { BaseEditComponent } from '../../../config/base.edit.component';
import { Attachment } from '../../../models/common/attachment';

// Project-specific services
import { LoadingService } from '../../../services/loading.service';
import { CommonUtil } from '../../common.util';
import { AuthService } from '../../services/auth.services';
import { CommonService } from '../../services/common.service';
import { ToastService } from '../../services/toast.service';

// Project-specific managers

// Constants File
import { Constant } from '../../../config/constants';
import { FileUploaderManager } from './file-uploader.manager';

@Component({
    selector: 'app-file-uploader',
    standalone: true,
    imports: [CommonModule, NgbTooltipModule, TranslateModule, FileUploadModule],
    templateUrl: './file-uploader.component.html',
    styleUrls: ['./file-uploader.component.scss'],
})
export class FileUploaderComponent extends BaseEditComponent {
    @Input() documents!: any[]; // Changed to array for multiple documents
    @Input() uploaderId!: string;
    @Input() viewOnly!: boolean;
    @Input() uploaderTitle!: string;
    @Input() disabled!: boolean;
    @Input() fileTypes!: string;
    @Input() uploaderInfo!: string;

    @Output() successCallback = new EventEmitter<boolean>();
    @Output() removeFileCallbackData = new EventEmitter<any>();

    DocumentsUploader!: FileUploader;
    fileAcceptType = Constant.FILE_ACCEPT_TYPES;

    headers!: Headers[];

    hasBaseDropZoneOver: boolean = false;
    showUploaderIcon: boolean = true;
    timeoutId: any;

    deleteAlertMsg = Constant.ALERT_MESSAGES;

    constructor(
        protected fileUploaderManager: FileUploaderManager,
        protected override route: ActivatedRoute,
        public override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        public commonUtil: CommonUtil,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
        protected authService: AuthService,
    ) {
        super(fileUploaderManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit(): void {
        this.fileAcceptType = this.fileTypes ?? Constant.FILE_ACCEPT_TYPES;
        this.loadingService.show();
        this.headers = [
            {
                name: 'Authorization',
                value: 'Bearer ' + this.authService.getToken().accessToken,
            },
        ];
        this.loadingService.hide();
    }

    ngOnDestroy(): void {
        // Clean up the timeout when the component is destroyed to avoid memory leaks
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        // Check if 'documents' input property has changed
        if (changes['documents']) {
            // Clear any previous timeouts to prevent multiple calls if 'documents' changes rapidly
            if (this.timeoutId) {
                clearTimeout(this.timeoutId);
            }

            // Set a timeout to delay the plugin initialization (2 seconds after 'documents' change)
            this.timeoutId = setTimeout(() => {
                this.pluginInitialization();
            }, 2000);
        }
    }

    override onFetchCompleted() {
        if (!this.documents) {
            this.documents = new Array<Attachment>();
        }
        this.pluginInitialization();
    }

    override pluginInitialization(): void {
        this.DocumentsUploader = this.initializeUploader(
            this.documents,
            this.fileAcceptType,
            5120,
            this.toastService,
            this.headers,
        );
    }

    override removeFileCallback(file: { isDeleted: boolean }) {
        file.isDeleted = true;
        const index = this.documents.indexOf(file);
        this.documents[index].isDeleted = true;
        this.removeFileCallbackData.emit(file);
    }

    override onUploadSuccess() {}

    override allFilesUploadSuccess(): void {
        this.loadingService.hide();
        this.successCallback.emit(true);
    }

    customRemoveFile(file: any, event: Event): void {
        event.stopPropagation();
        const index = this.documents.indexOf(file);

        if (index > -1) {
            this.commonService.confirmation(this.deleteAlertMsg.deleteMsg, this.removeFileCallback.bind(this), file);
        }
    }

    downloadFile(file: Attachment) {
        window.open(file.secureUrl, '_blank');
    }

    fileChange(event: any) {
        this.showUploaderIcon = false;
        setTimeout(() => {
            this.showUploaderIcon = true;
        });
    }

    fileOverBase(e: any): void {
        this.hasBaseDropZoneOver = e;
    }

    getImagePath(file: any): string {
        return file.secureUrl;
    }

    getDocIcon(file: any): string | undefined {
        const extension = file.originalName.split('.').pop()?.toLowerCase();
        return extension && this.extensionIconMap[extension] ? this.extensionIconMap[extension] : undefined;
    }

    hasFiles() {
        let data = this.documents && this.documents.length > 0 && this.documents.some((doc) => !doc.isDeleted);
        return data;
    }

    triggerFileInput(id: string): void {
        document.getElementById(id)?.click();
    }

    formatFileSize(size: number): string {
        if (size >= 1048576) {
            return (size / 1048576).toFixed(2) + ' MB';
        } else if (size >= 1024) {
            return (size / 1024).toFixed(2) + ' KB';
        } else {
            return size + ' B';
        }
    }

    private readonly extensionIconMap: { [key: string]: string } = {
        pdf: 'bi bi-file-earmark-pdf',
        doc: 'bi bi-file-earmark-word',
        docx: 'bi bi-file-earmark-word',
        txt: 'bi bi-file-earmark-text',
        rtf: 'bi bi-file-earmark-text',
        odt: 'bi bi-file-earmark-text',
    };
}
