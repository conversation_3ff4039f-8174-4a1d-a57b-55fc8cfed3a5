// Angular core
import { Injectable } from '@angular/core';

// services
import { LoadingService } from '../../../../../services/loading.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { ShipmentCargoService } from './shipment-cargo.service';

// Manager
import { BaseManager } from '../../../../../config/base.manager';
import { FilterParam } from '../../../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class ShipmentCargoManager extends BaseManager {
    constructor(
        protected shipmentCargoService: ShipmentCargoService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(shipmentCargoService, loadingService, toastService);
    }

    getFreightAmount(filterParam: FilterParam) {
        return this.fetchDropdownData(() => this.shipmentCargoService.getFreightAmount(filterParam));
    }
}
