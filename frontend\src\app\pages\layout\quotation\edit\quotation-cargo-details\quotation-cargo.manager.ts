// Angular core
import { Injectable } from '@angular/core';

// services
import { LoadingService } from '../../../../../services/loading.service';
import { ToastService } from '../../../../../shared/services/toast.service';

// Manager
import { BaseManager } from '../../../../../config/base.manager';
import { FilterParam } from '../../../../../models/common/filter-param';
import { QuotationCargoService } from './quotation-cargo.service';

@Injectable({
    providedIn: 'root',
})
export class QuotationCargoManager extends BaseManager {
    constructor(
        protected quotationCargoService: QuotationCargoService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(quotationCargoService, loadingService, toastService);
    }

    getFreightAmount(filterParam: FilterParam) {
        return this.fetchDropdownData(() => this.quotationCargoService.getFreightAmount(filterParam));
    }
}
