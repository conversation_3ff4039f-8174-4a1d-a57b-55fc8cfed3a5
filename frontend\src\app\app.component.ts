import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ToastContainerComponent } from './shared/common-component/toast-container/toast-container.component';

@Component({
    selector: '.app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    imports: [RouterOutlet, ToastContainerComponent],
    standalone: true,
})
export class AppComponent {
    title = 'Welcome to Bees Express';

    constructor(private translate: TranslateService) {
        translate.setDefaultLang('en');
    }
}
