<div class="row g-0 align-items-center">
    <div class="col-auto">
        <div class="form-floating form-group custom-ng-select country-code-ng-select-container">
            <ng-select [items]="countryCodes" bindLabel="countryLabel" bindValue="countryValue"
                #countryCodeModel="ngModel" [ngModel]="countryCode" (ngModelChange)="onCountryCodeChange($event)"
                [name]="nameCode" [required]="required" [clearable]="false" [ngClass]="{
            'is-invalid': !countryCodeModel.valid && !disableCode && onClickValidation
          }" [disabled]="disableCode">
            </ng-select>
        </div>
    </div>

    <div class="col ps-0">
        <div class="form-floating form-group country-code-input-container w-100">
            <input class="form-control" type="text" [name]="fieldName" #PhoneNumber="ngModel" [ngModel]="number"
                (ngModelChange)="onNumberChange($event)" placeholder="{{'Address.phoneNumber'| translate }}" [ngClass]="{
            'is-invalid': !PhoneNumber.valid && !disableNumber && onClickValidation
          }" [required]="required" [disabled]="disableNumber" appPhoneValidator [mask]="phoneMaskPattern" />
            <label [for]="fieldName">
                {{labelName | translate}}
            </label>
        </div>
    </div>

    <app-validation-message [field]="PhoneNumber" [onClickValidation]="onClickValidation"></app-validation-message>
</div>