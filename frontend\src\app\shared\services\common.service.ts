import { Injectable } from '@angular/core';
import * as moment from 'moment-timezone';
import Swal from 'sweetalert2';

@Injectable({
    providedIn: 'root',
})
export class CommonService {
    confirmation(
        heading: string,
        callback: any,
        param1 = null,
        param2 = null,
        param3 = null,
        isIconTypeSuccess?: boolean,
    ) {
        Swal.fire({
            title: heading,
            icon: isIconTypeSuccess ? 'error' : 'success',
            // timer: 5000,
            confirmButtonColor: '#d9534f',
            confirmButtonText: 'Yes',
            allowEscapeKey: false,
            showCancelButton: true,
            cancelButtonText: 'No',
            background: '#f2f2f2',
            backdrop: `
            rgba(0,0,123,0.4)
            left top
            no-repeat
          `,
        }).then((result) => {
            if (result.value) {
                callback(param1, param2, param3);
            }
        });
    }
    showImageOnClick: any;
    today = new Date().toISOString().split('T')[0];
    constructor() {}

    confirmActionDialog(heading: string, callback: (data: any) => void, data: any = {}, isIconTypeSuccess?: boolean) {
        Swal.fire({
            title: heading,
            icon: isIconTypeSuccess ? 'success' : 'error',
            confirmButtonColor: '#d9534f',
            confirmButtonText: 'Yes',
            allowEscapeKey: false,
            showCancelButton: true,
            cancelButtonText: 'No',
            background: '#f2f2f2',
            backdrop: `rgba(0,0,123,0.4) left top no-repeat`,
        }).then((result) => {
            if (result.value) {
                callback(data); // just one clean parameter
            }
        });
    }

    confirmWithChoiceDialog(
        heading: string,
        callback: (param1?: any, param2?: any, param3?: any) => void,
        NoCallback?: () => void,
        param1: any = null,
        param2: any = null,
        param3: any = null,
    ): void {
        Swal.fire({
            title: heading,
            icon: 'success',
            // timer: 5000,
            confirmButtonColor: '#d9534f',
            confirmButtonText: 'Yes',
            allowEscapeKey: false,
            showCancelButton: true,
            cancelButtonText: 'No',
            background: '#f2f2f2',
            backdrop: `
            rgba(0,0,123,0.4)
            left top
            no-repeat
          `,
        }).then((result) => {
            if (result.isConfirmed) {
                callback(param1, param2, param3);
            } else if (NoCallback) {
                NoCallback(); // Call the NoCallback if provided
            }
        });
    }

    getCurrentYear(): number {
        const currentDate = new Date();
        return currentDate.getFullYear();
    }

    // Method to focus on the first invalid field, including custom components
    focusInvalidField() {
        const invalidControls = [
            ...Array.from(document.querySelectorAll('.form-control.ng-invalid')),
            ...Array.from(document.querySelectorAll('.ng-select.ng-invalid')),
        ];
        if (invalidControls.length > 0) {
            // Focus the first invalid control
            const firstInvalidControl = invalidControls[0] as HTMLElement;

            if (firstInvalidControl.tagName.toLowerCase() === 'ng-select') {
                // Special handling for ng-select
                const ngSelectComponent = (firstInvalidControl as any).querySelector('.ng-select-container');
                if (ngSelectComponent) {
                    ngSelectComponent.focus();
                }
            } else {
                firstInvalidControl.focus();
            }
        }
    }

    getTimeZoneInfo() {
        const timeZoneId = moment.tz.guess(); // Get user's time zone
        const timeZoneOffset = moment.tz(timeZoneId).utcOffset(); // Get UTC offset in minutes

        return { timeZoneId, timeZoneOffset };
    }
}
