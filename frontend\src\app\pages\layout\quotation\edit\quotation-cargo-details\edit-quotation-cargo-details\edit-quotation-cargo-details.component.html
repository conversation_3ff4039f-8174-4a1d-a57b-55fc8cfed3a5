<div class="site-page-container">
    <div class="site-card">
        <form #quotationCargoDetailForm="ngForm" novalidate>
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-12 px-0">
                    <!-- SECTION: Basic Details -->
                    <div class="section-card mb-3 px-3 pt-3 border rounded shadow-sm">
                        <div class="label-wrap-box">
                            <span>{{ 'Quotation.Cargo' | translate }} {{ 'COMMON.Information' | translate }}</span>
                        </div>

                        <div class="row gx-0">
                            <!-- Description -->
                            <div class="col-md-12 mb-14">
                                <div
                                    class="form-floating textarea-custom"
                                    [ngClass]="{ 'has-error-textarea': !Description.valid && onClickValidation }"
                                >
                                    <textarea
                                        class="form-control"
                                        placeholder="Description"
                                        rows="4"
                                        id="description"
                                        name="Description"
                                        #Description="ngModel"
                                        required
                                        [(ngModel)]="quotationCargoDetail.description"
                                        [ngClass]="{ 'is-invalid': !Description.valid && onClickValidation }"
                                    >
                                    </textarea>
                                    <label for="description">{{ 'ShipmentItem.description' | translate }}</label>
                                </div>
                                <app-validation-message
                                    [field]="Description"
                                    [onClickValidation]="onClickValidation"
                                ></app-validation-message>
                            </div>

                            <!-- Cargo Type -->
                            <div class="col-md-6 mb-14 ps-md-0 pe-md-2">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select
                                        bindLabel="name"
                                        bindValue="id"
                                        [items]="cargoTypeOptions"
                                        [(ngModel)]="quotationCargoDetail.cargoType"
                                        #CargoType="ngModel"
                                        name="Cargo Type"
                                        required
                                        [ngClass]="{ 'is-invalid': !CargoType.valid && onClickValidation }"
                                        [clearable]="false"
                                    >
                                    </ng-select>
                                    <label class="ng-select-label" for="CargoType"
                                        >{{ 'QuotationItem.cargoType' | translate }}
                                    </label>
                                    <app-validation-message
                                        [field]="CargoType"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Rate Type -->
                            <div class="col-md-6 mb-14 ps-md-2 pe-md-0">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select
                                        bindLabel="name"
                                        bindValue="id"
                                        [items]="rateTypes"
                                        [(ngModel)]="quotationCargoDetail.rateType"
                                        #RateType="ngModel"
                                        name="rateType"
                                        required
                                        [ngClass]="{ 'is-invalid': !RateType.valid && onClickValidation }"
                                        [clearable]="false"
                                        (change)="getFreightValues()"
                                    >
                                    </ng-select>
                                    <label class="ng-select-label" for="rateType">{{
                                        'RateSheetWeightCharge.rateType' | translate
                                    }}</label>
                                    <app-validation-message
                                        [field]="RateType"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <div class="col-md-6 mb-14 ps-md-0 pe-md-2">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select
                                        bindLabel="name"
                                        bindValue="id"
                                        [items]="weightTypeOptions"
                                        [(ngModel)]="quotationCargoDetail.weightType"
                                        #WeightType="ngModel"
                                        name="Weight Type"
                                        required
                                        [ngClass]="{ 'is-invalid': !WeightType.valid && onClickValidation }"
                                        [clearable]="false"
                                        (change)="setWeightToLbs(); getFreightValues()"
                                    >
                                    </ng-select>
                                    <label class="ng-select-label" for="WeightType">{{
                                        'SHIPMENT.weightType' | translate
                                    }}</label>
                                    <app-validation-message
                                        [field]="WeightType"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Weight -->
                            <div class="col-md-6 mb-14 ps-md-2 pe-md-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="weight"
                                        #Weight="ngModel"
                                        [(ngModel)]="quotationCargoDetail.weight"
                                        required
                                        placeholder="Weight"
                                        [maxlength]="10"
                                        appAllowNumberOnly
                                        [ngClass]="{ 'is-invalid': !Weight.valid && onClickValidation }"
                                        (change)="setWeightToLbs(); getFreightValues()"
                                        mask="separator.2"
                                        [allowedValue]="'.'"
                                        [thousandSeparator]="''"
                                        [dropSpecialCharacters]="true"
                                    />
                                    <label for="weight">{{ 'QuotationItem.weight' | translate }}</label>
                                    <app-validation-message
                                        [field]="Weight"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Weight Lbs -->
                            <div class="col-md-6 mb-14 pe-md-2 ps-md-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="weightLbs"
                                        #WeightLbs="ngModel"
                                        [(ngModel)]="quotationCargoDetail.weightInPounds"
                                        placeholder="Weight (LBS)"
                                        [maxlength]="10"
                                        mask="separator.2"
                                        [allowedValue]="'.'"
                                        appAllowNumberOnly
                                        [thousandSeparator]="''"
                                        [dropSpecialCharacters]="true"
                                        disabled
                                    />
                                    <label for="weightLbs">{{ 'SHIPMENT.weightLbs' | translate }}</label>
                                </div>
                            </div>

                            <!-- Quantity -->
                            <div class="col-md-6 mb-14 pe-md-0 ps-md-2">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="quantity"
                                        #Quantity="ngModel"
                                        [(ngModel)]="quotationCargoDetail.quantity"
                                        required
                                        placeholder="Quantity"
                                        (change)="onQuantityChange()"
                                        [ngClass]="{ 'is-invalid': !Quantity.valid && onClickValidation }"
                                        [maxlength]="10"
                                        appAllowNumberOnly
                                    />
                                    <label for="quantity">{{ 'SHIPMENT.numberOfUnits' | translate }}</label>
                                    <app-validation-message
                                        [field]="Quantity"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SECTION: Dimensions -->
                    <div class="section-card mb-3 px-3 pt-3 border rounded shadow-sm">
                        <div class="label-wrap-box">
                            <span>Dimensions</span>
                        </div>

                        <div class="row gx-0">
                            <!-- Length -->
                            <div class="col-12 col-sm-6 col-lg-3 mb-14 pe-sm-2 pe-lg-0 ps-sm-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="length"
                                        #Length="ngModel"
                                        [(ngModel)]="quotationCargoDetail.length"
                                        required
                                        placeholder="Length"
                                        [ngClass]="{ 'is-invalid': !Length.valid && onClickValidation }"
                                        (change)="getVolume()"
                                        mask="separator.2"
                                        [allowedValue]="'.'"
                                        appAllowNumberOnly
                                        [thousandSeparator]="''"
                                        [dropSpecialCharacters]="true"
                                    />
                                    <label for="length"
                                        >{{ 'QuotationItem.length' | translate }}
                                        {{ 'COMMON.inInches' | translate }}</label
                                    >
                                    <app-validation-message
                                        [field]="Length"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Width -->
                            <div class="col-12 col-sm-6 col-lg-3 mb-14 pe-sm-0 pe-lg-2 ps-sm-2">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="width"
                                        #Width="ngModel"
                                        [(ngModel)]="quotationCargoDetail.width"
                                        placeholder="Width"
                                        (change)="getVolume()"
                                        mask="separator.2"
                                        [allowedValue]="'.'"
                                        appAllowNumberOnly
                                        required
                                        [thousandSeparator]="''"
                                        [dropSpecialCharacters]="true"
                                        [ngClass]="{ 'is-invalid': !Width.valid && onClickValidation }"
                                        [maxlength]="10"
                                    />
                                    <label for="width"
                                        >{{ 'QuotationItem.width' | translate }}
                                        {{ 'COMMON.inInches' | translate }}</label
                                    >
                                    <app-validation-message
                                        [field]="Width"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Height -->
                            <div class="col-12 col-sm-6 col-lg-3 mb-14 ps-sm-0 pe-sm-2">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="height"
                                        #Height="ngModel"
                                        [(ngModel)]="quotationCargoDetail.height"
                                        required
                                        placeholder="Height"
                                        (change)="getVolume()"
                                        mask="separator.2"
                                        [allowedValue]="'.'"
                                        appAllowNumberOnly
                                        [thousandSeparator]="''"
                                        [dropSpecialCharacters]="true"
                                        [ngClass]="{ 'is-invalid': !Height.valid && onClickValidation }"
                                        [maxlength]="10"
                                    />
                                    <label for="height"
                                        >{{ 'QuotationItem.height' | translate }}
                                        {{ 'COMMON.inInches' | translate }}</label
                                    >
                                    <app-validation-message
                                        [field]="Height"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Volume -->
                            <div class="col-12 col-sm-6 col-lg-3 mb-14 pe-sm-0 ps-sm-2 ps-lg-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="volume"
                                        #Volume="ngModel"
                                        [(ngModel)]="quotationCargoDetail.volume"
                                        placeholder="Volume"
                                        disabled
                                    />
                                    <label for="volume"
                                        >{{ 'QuotationItem.volume' | translate }}
                                        {{ 'COMMON.cubicInches' | translate }}</label
                                    >
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SECTION: Weight Charges -->
                    <div class="section-card mb-3 px-3 pt-3 border rounded shadow-sm">
                        <div class="label-wrap-box">
                            <span>Weight Charges</span>
                        </div>

                        <div class="row gx-0">
                            <!-- Freight field  -->
                            <div class="col-md-6 mb-14 pe-md-2 ps-md-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="freight"
                                        #Freight="ngModel"
                                        [(ngModel)]="quotationCargoDetail.freight"
                                        placeholder="freight"
                                        [maxlength]="10"
                                        appCurrencyFormatter
                                        required
                                        (change)="generateTotalAmount()"
                                        [ngClass]="{ 'is-invalid': !Freight.valid && onClickValidation }"
                                    />
                                    <label for="Freight">{{ 'SHIPMENT.freight' | translate }}</label>

                                    <app-validation-message
                                        [field]="Freight"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- Fuel Levy field  -->
                            <div class="col-md-6 mb-14 pe-md-0 ps-md-2">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="fuel Levy"
                                        #FuelLevy="ngModel"
                                        [(ngModel)]="quotationCargoDetail.fuelCharges"
                                        placeholder=""
                                        [maxlength]="10"
                                        appCurrencyFormatter
                                        required
                                        (change)="generateTotalAmount()"
                                        [ngClass]="{ 'is-invalid': !FuelLevy.valid && onClickValidation }"
                                    />
                                    <label for="FuelLevy">{{ 'SHIPMENT.fuelLevy' | translate }}</label>

                                    <app-validation-message
                                        [field]="FuelLevy"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- GST Amount  field -->
                            <div class="col-md-6 mb-14 pe-md-2 ps-md-0">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="gst"
                                        #GST="ngModel"
                                        [(ngModel)]="quotationCargoDetail.gst"
                                        placeholder="Gst Amount"
                                        appCurrencyFormatter
                                        required
                                        (change)="generateTotalAmount()"
                                        [ngClass]="{ 'is-invalid': !GST.valid && onClickValidation }"
                                    />
                                    <label for="GST"
                                        >{{ 'QuotationItem.gstAmount' | translate }}
                                        {{ 'COMMON.inDollar' | translate }}</label
                                    >

                                    <app-validation-message
                                        [field]="GST"
                                        [onClickValidation]="onClickValidation"
                                    ></app-validation-message>
                                </div>
                            </div>

                            <!-- total field -->
                            <div class="col-md-6 mb-14 pe-md-0 ps-md-2">
                                <div class="form-floating form-group">
                                    <input
                                        class="form-control"
                                        type="text"
                                        name="total"
                                        #Total="ngModel"
                                        [(ngModel)]="quotationCargoDetail.total"
                                        placeholder=""
                                        disabled
                                        appCurrencyFormatter
                                    />
                                    <label for="total"
                                        >{{ 'SHIPMENT.TotalAmount' | translate }}
                                        {{ 'COMMON.inDollar' | translate }}</label
                                    >
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-12 custom-buttons-container justify-content-end mb-0">
                        <button
                            class="btn custom-medium-button save-button"
                            appRippleEffect
                            type="button"
                            (click)="save(quotationCargoDetailForm.form)"
                        >
                            <div class="site-button-inner">
                                {{ 'COMMON.SAVE' | translate }}
                            </div>
                        </button>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
