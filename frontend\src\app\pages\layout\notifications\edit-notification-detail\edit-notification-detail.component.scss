@import '../../../../../variables.scss';
@import '../../../../../assets/scss/mixins.scss';

.bell-icon-button {
    padding: 8px 10px;

    i {
        font-size: 24px !important;
    }

    &:focus,
    &:focus-visible,
    &:active {
        outline: none;
        box-shadow: none;
    }
}

.notification-panel {
    background: $white-color;
    width: 100% !important;
    scroll-behavior: smooth;
    height: 350px;

    .notification-group {
        .sticky-header {
            position: sticky;
            top: 0;
            background: var(--primary-color);
            color: $white-color;
            z-index: 1;
            padding: 4px 12px;
            font-weight: $font-weight-600;
            border-bottom: 1px solid #eee;
            font-size: $font-size-12;
            text-transform: capitalize;
            border-radius: 8px 8px 0 0;
        }

        .notification-item {
            margin-bottom: 4px;

        }

    }

    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: #f0f0f0;
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 10px;
        border: 2px solid transparent;
        background-clip: content-box;
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: #a0a0a0;
    }

    hr {
        border-top: 1px solid #868484;
        margin: 0.25rem 0 0.25rem !important;
    }

    .header {
        @include flex-space-between;
        position: relative;

        h3 {
            font-size: 16px;
            font-weight: $font-weight-600;
            margin: 0;
        }

        .new-badge {
            background-color: #fff8d2;
            color: #ffc107;
            border-radius: 8px;
            font-size: 12px;
            padding: 4px 8px;
        }

        .icon-envelope {
            position: absolute;
            right: 0;
            font-size: 18px;
            color: #666;
        }
    }

    .scrolling-section {
        overflow-y: auto;
        height: 100%;
    }

    .scrolling-section-length-zero {
        @include flex-center;
    }


    .notification-list {
        max-height: 100%;
        padding-right: 4px;

        .notification-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            border-radius: 12px;
            background: #fff;
            margin-bottom: 4px;
            box-shadow: 0 2px 6px #0000000d;
            transition: all .2s ease;
            position: relative;

            &:hover {
                cursor: pointer;
                background: #fcd61696;
                border-radius: 16px;
                margin-top: 10px;
                margin-bottom: 10px !important;
            }

            &.read {
                opacity: 0.7;
            }

            &.time {
                color: $black-color
            }

            .avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 8px;
                object-fit: cover;
                background: #fff;
                padding: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                /* Light shadow */

                &.placeholder {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    color: #555;
                }
            }

            .content {
                flex-grow: 1;

                .title {
                    margin: 0;
                    font-weight: $font-weight-600;
                    font-size: 12px;
                    word-break: keep-all;
                }

                .message {
                    font-size: 13px;
                    color: #666;
                    margin-top: 2px;
                }
            }

            .icon-close {
                cursor: pointer;
                font-size: $font-size-16;
                color: #aaa;
            }

            .dot {
                width: 10px;
                height: 10px;
                background: var(--primary-color);
                border-radius: 50%;
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
            }
        }


    }

    .view-all {
        margin-top: 8px;
        width: 100%;
        padding: 10px;
        background-color: var(--primary-color);
        color: $white-color;
        border: none;
        border-radius: 8px;
        font-weight: $font-weight-600;
        cursor: pointer;

        &:hover {
            background-color: var(--primary-color);
        }
    }
}

.bell-container {
    position: relative;

    .notification-badge {
        position: absolute;
        top: 0px;
        right: -2px;
        background-color: red;
        color: $white-color;
        font-size: 10px;
        font-weight: bold;
        padding: 0 5px;
        height: 20px;
        min-width: 20px;
        border-radius: 50%;
        @include flex-center;
        line-height: 1;
        z-index: 10;
        white-space: nowrap;
    }

    .bell-icon {
        font-size: 20px;
    }
}


.no-data-box {
    img {

        height: 120px;
        display: flex;
        margin: 0 auto;
    }
}

::ng-deep ngb-popover-window {
    background-color: $white-color !important;
    border-radius: 10px;

    .popover-body {
        color: $white-color;
    }
}

::ng-deep .notification-option-popup {
    max-width: none !important;
    width: 350px;

    .popover-body {
        padding: 8px !important;
    }
}

@media (max-width: 425px) {

    ::ng-deep ngb-popover-window {
        inset: 0px -55px auto auto !important;
    }

    ::ng-deep .notification-option-popup {
        width: 300px !important;
    }

    ::ng-deep ngb-popover-window .popover-arrow {
        transform: translate3d(216px, 0px, 0px) !important;
    }
}

.loader {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    position: relative;
    animation: rotate 1s linear infinite;
}

.loader::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    inset: 0px;
    border-radius: 50%;
    border: 5px solid #ffc107;
    animation: prixClipFix 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
    }

    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    }

    100% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
    }
}

::ng-deep .accordion-item>.accordion-header .accordion-button {
    border-radius: 8px !important;
    background-color: $white-color;
    box-shadow: none;
    color: #ffc107 !important;
}

.color-red {
    color: red;
}

.color-yellow {
    color: #fd7e14;
}

.color-green {
    color: green;
}

.color-blue {
    color: #007bff;
}

.line-1 {
    border: 1px solid var(--login-color);
}