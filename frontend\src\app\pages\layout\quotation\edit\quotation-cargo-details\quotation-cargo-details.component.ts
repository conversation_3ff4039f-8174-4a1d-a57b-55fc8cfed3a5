// Angular core and common modules
import { CommonModule, CurrencyPipe, NgClass, TitleCasePipe } from '@angular/common';
import { Component, EventEmitter, Input, Output, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router, RouterModule } from '@angular/router';

// Third-party modules
import { NgbAccordionModule, NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { Subscription } from 'rxjs';

// Configuration and base classes
import { BaseListServerSideComponent } from '../../../../../config/base.list.server.side.component';
import { Constant } from '../../../../../config/constants';

// Models
import { Quotation } from '../../../../../models/quotation/quotation';
import { QuotationCargoDetail } from '../../../../../models/quotation/quotation-cargo-details';
import { Shipment } from '../../../../../models/shipment/shipment';

// Services
import { LoadingService } from '../../../../../services/loading.service';
import { CommonService } from '../../../../../shared/services/common.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Directives
import { DelayedInputDirective } from '../../../../../shared/directives/delayed-input.directive';

// Managers
import { QuotationCargoManager } from './quotation-cargo.manager';

// Feature components
import { RemoveUnderscorePipe } from '../../../../../shared/pipes/remove-underscore.pipe';
import { EditQuotationCargoDetailsComponent } from './edit-quotation-cargo-details/edit-quotation-cargo-details.component';

@Component({
    selector: 'app-quotation-cargo-details',
    standalone: true,
    imports: [
        CurrencyPipe,
        CommonModule,
        DataTablesModule,
        DelayedInputDirective,
        EditQuotationCargoDetailsComponent,
        NgClass,
        NgSelectModule,
        NgbAccordionModule,
        RouterModule,
        TitleCasePipe,
        TranslateModule,
        RemoveUnderscorePipe
    ],
    templateUrl: './quotation-cargo-details.component.html',
    styleUrl: './quotation-cargo-details.component.scss',
})
export class QuotationCargoDetailsComponent extends BaseListServerSideComponent {
    @ViewChild('description') description!: TemplateRef<string>;
    @ViewChild('cargoType') cargoType!: TemplateRef<string>;
    @ViewChild('WeightInPounds') WeightInPounds!: TemplateRef<string>;
    @ViewChild('volume') volume!: TemplateRef<string>;
    @ViewChild('freight') freight!: TemplateRef<string>;
    @ViewChild('FuelCharges') FuelCharges!: TemplateRef<string>;
    @ViewChild('gst') gst!: TemplateRef<string>;
    @ViewChild('total') total!: TemplateRef<string>;
    @ViewChild('action') action!: TemplateRef<any>;

    @Input() quotation!: Quotation;
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() override request!: any;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    modalRef!: NgbModalRef;
    title!: string;
    quotationCargoDetails!: QuotationCargoDetail[];
    quotationCargoDetail!: QuotationCargoDetail;

    routerSubscription?: Subscription;

    resourceType: string = Constant.RESOURCE_TYPE.SHIPMENT_CARGO;

    constructor(
        protected quotationCargoManager: QuotationCargoManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected override router: Router,
        protected typeAheadService: TypeAheadService,
        public modalService: NgbModal,
        protected route: ActivatedRoute,
    ) {
        super(quotationCargoManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.quotationCargoDetails = new Array<QuotationCargoDetail>();

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.SHIPMENT_ITEMS;

        this.filterParam.fileName = this.resourceType;
        this.filterParam.filtering.quotationId = this.route.snapshot.paramMap.get('id') as string;

        this.init();

        this.routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart && this.modalRef) {
                this.modalRef.close();
            }
        });
    }

    ngAfterViewInit() {
        const templates = {
            description: this.description,
            cargoType: this.cargoType,
            WeightInPounds: this.WeightInPounds,
            volume: this.volume,
            freight: this.freight,
            FuelCharges: this.FuelCharges,
            gst: this.gst,
            total: this.total,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
        super.ngOnDestroy();
    }

    override onFetchCompleted() {
        this.quotationCargoDetails = this.records.map((data) => QuotationCargoDetail.fromResponse(data));
        super.onFetchCompleted();
    }

    openModal(content: TemplateRef<any>, title: string, data?: any) {
        this.modalRef = this.modalService.open(content, { centered: true, size: 'lg', backdrop: 'static' });
        this.title = title;
        this.quotationCargoDetail = data;
    }

    onNext() {
        this.onNextOrBackClick.emit(4);
    }

    onBack() {
        this.onNextOrBackClick.emit(2);
    }

    onModalClose(eventData: string) {
        this.refreshRecord();
    }
}
