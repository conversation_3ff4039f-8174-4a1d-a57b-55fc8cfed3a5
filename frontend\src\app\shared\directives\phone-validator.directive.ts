import { Directive, Input } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, Validator, Validators } from '@angular/forms';
import { Constant } from '../../config/constants';

@Directive({
    selector: '[appPhoneValidator]',
    standalone: true,
    providers: [{ provide: NG_VALIDATORS, useExisting: PhoneValidatorDirective, multi: true }],
})
export class PhoneValidatorDirective implements Validator {
    @Input() appPhoneValidator!: string;
    @Input() validateFax!: boolean;

    constructor() {}

    validate(control: AbstractControl): Validators | null {
        let pattern = Constant.PATTERNS.PHONE_NUMBER_VALIDATOR; // Default pattern for phone numbers

        pattern = new RegExp(this.appPhoneValidator);

        if (control.value && !pattern.test(control.value)) {
            return { invalidPhone: true }; // Set custom error key
        }

        return null;
    }
}
