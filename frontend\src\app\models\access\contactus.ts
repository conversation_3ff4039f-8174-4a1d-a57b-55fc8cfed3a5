import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

export class ContactUs extends BaseModel {
    tenantId!: number;
    slug!: string;
    name!: string;
    firstName!: string;
    lastName!: string;
    email!: string;
    phone!: string;
    subject!: string;
    message!: string;
    isResponded!: boolean;
    responseMessage!: string;
    respondedOn!: Date;
    // respondedOnCalendar!: any;
    iPAddress!: string;
    status!: string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.isResponded = false;
    }

    static fromResponse(data: any): ContactUs {
        const ContactUs = { ...data };

        return ContactUs;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.name = this.trimMe(this.name);
        this.firstName = this.trimMe(this.firstName);
        this.lastName = this.trimMe(this.lastName);
        this.email = this.trimMe(this.email);
        this.phone = this.trimMe(this.phone);
        this.subject = this.trimMe(this.subject);
        this.message = this.trimMe(this.message);
        this.responseMessage = this.trimMe(this.responseMessage);
        this.iPAddress = this.trimMe(this.iPAddress);
        this.status = this.trimMe(this.status);
        return this;
    }
}
