<!-- Recover Password Page -->

<div class="public-page-wrapper min-vh-100 display-flex-column login-bg">
  <div class="container-fluid">
    <div class="row justify-content-md-center justify-content-xl-end ">
      <div class="col-lg-5 col-md-6 display-flex-column public-page-left-section login-page">
        <div class="from-container">
          <div class="logo-section mb-4">
            <img src="/assets/images/svg/password.svg" class="img-fluid mb-3 app-logo forgot-logo" alt="Logo" />
            <h2 class="welcome-label-text"> <span>{{headerText}}</span></h2>
            <p>
              Choose a new password to finish resetting your account.
            </p>
          </div>
          <form autocomplete="off" class="form-section mb-3 mb-md-4" #changePasswordForm="ngForm">
            <!-- New Password -->
            <div class="form-floating mb-3 site-form-control toggle-password-visible">
              <input class="form-control input-control" type="password" id="newPassword"
                placeholder="{{ 'NEW PASSWORD' | translate }}" name="newPassword" #newPassword="ngModel"
                [(ngModel)]="resetPasswordData.password" required [minlength]="8" [maxlength]="20"
                [ngClass]="{ 'is-invalid': !newPassword.valid && onClickValidation }" />
              <label for="newPassword">{{ 'NEW PASSWORD' | translate }}</label>

              @if(resetPasswordData.password && resetPasswordData.password.length > 0){
              <i class="eye-icon bi bi-eye-slash" [ngClass]="{'eye-icon-left':!newPassword.valid && onClickValidation}"
                appTogglePasswordVisibility></i>
              }

              <app-validation-message [field]="newPassword" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'COMMON.REQUIRED_PASSWORD_COMBINATION' | translate"></app-validation-message>
            </div>

            <!-- Confirm Password -->
            <div class="form-floating mb-3 site-form-control toggle-password-visible">
              <input class="form-control input-control" type="password" id="confirmPassword"
                placeholder="{{ 'CONFIRM PASSWORD' | translate }}" name="confirmPassword" #confirmPassword="ngModel"
                [minlength]="8" [maxlength]="20" [(ngModel)]="resetPasswordData.confirmPassword" required [ngClass]="{
                                    'is-invalid':
                                        (!confirmPassword.valid && onClickValidation) ||
                                        resetPasswordData.password !== resetPasswordData.confirmPassword,
                                }" />
              <label for="confirmPassword">{{ 'CONFIRM PASSWORD' | translate }}</label>

              <!-- Toggle Password Visibility Icon -->
              @if(resetPasswordData.confirmPassword && resetPasswordData.confirmPassword.length > 0){
              <i class="eye-icon bi bi-eye-slash" [ngClass]="{
                            'eye-icon-left':(!confirmPassword.valid && onClickValidation) || 
                            resetPasswordData.password !== resetPasswordData.confirmPassword
                        }" appTogglePasswordVisibility></i>
              }

              <app-validation-message [field]="confirmPassword" [onClickValidation]="onClickValidation"
                [comparableField]="newPassword"></app-validation-message>
            </div>
            <!-- Submit Button -->
            <div class="action-button-container mt-2">
              <button class="btn btn-login custom-medium-button overflow-hidden btn-login w-100"
                (click)="processPasswordReset(changePasswordForm.form)">
                Submit
              </button>
            </div>
          </form>
          <!-- Terms and conditions link -->
          <p class="term-condition-text mb-2">
            Back to
            <a class="text-decoration-underline cursor-pointer" routerLink="/login"><strong>Login</strong></a>
          </p>
        </div>
        <!-- Footer with copyright notice -->
        <!-- <div class="copy-right-text text-center text-md-start">
          © <span>Bees Express</span> 2025 All Rights Reserved
        </div> -->
      </div>
      <!-- Right Section: Informational Content -->
      <div class="col-xl-8 col-lg-6 px-0 d-none public-page-right-section login-page">
        <app-auth-right-section></app-auth-right-section>
      </div>
    </div>
  </div>
</div>