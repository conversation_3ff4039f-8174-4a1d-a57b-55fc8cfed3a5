// Angular & Third-Party Modules
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

// Services
import { LoadingService } from '../../../services/loading.service';
import { LoginService } from '../../../services/login.service';
import { AuthService } from '../../../shared/services/auth.services';
import { CommonService } from '../../../shared/services/common.service';
import { LocalStorageService } from '../../../shared/services/local-storage.service';

// Models
import { Login } from '../../../models/access/login';

// Managers
import { FormHandlerManager } from '../../../managers/form-handler.manager';

// Directives
import { EmailValidatorDirective } from '../../../shared/directives/email-validator.directive';
import { RippleEffectDirective } from '../../../shared/directives/ripple-effect.directive';
import { TogglePasswordVisibilityModule } from '../../../shared/directives/toggle-password-visibility.directive';

// Components
import { ValidationMessageComponent } from '../../../shared/common-component/validation-message/validation-message.component';
import { AuthRightSectionComponent } from '../auth-right-section/auth-right-section.component';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'],
    standalone: true,
    imports: [
        AuthRightSectionComponent,
        CommonModule,
        EmailValidatorDirective,
        FormsModule,
        RippleEffectDirective,
        RouterLink,
        TogglePasswordVisibilityModule,
        TranslateModule,
        ValidationMessageComponent,
    ],
})
export class LoginComponent implements OnInit {
    loginData: Login = new Login();

    onClickValidation: boolean = false;
    showPassword: boolean = false;
    isButtonDisabled: boolean = false;

    constructor(
        public commonService: CommonService,
        private readonly authService: AuthService,
        private readonly formHandlerManager: FormHandlerManager,
        private readonly loginService: LoginService,
        private readonly router: Router,
        private readonly localStorageService: LocalStorageService,
        protected loadingService: LoadingService,
    ) { }

    ngOnInit() {
        this.loginData = new Login();

        // Clear all local storage and perform initial setup
        this.localStorageService.clearAll();

        // const timeZoneInfo = this.commonService.getTimeZoneInfo();
        // this.loginData.timeZoneId = timeZoneInfo.timeZoneId;
        // this.loginData.timeZoneOffset = timeZoneInfo.timeZoneOffset.toString();
    }

    login(form: { valid: boolean }) {
        if (!form.valid) {
            this.onClickValidation = true; // Update the onClickValidation variable
            return;
        }

        this.formHandlerManager.handleFormSubmission(this.loginService.login(this.loginData), (response) => {
            this.authService.processSuccessfulResponse(response.data);
            this.router.navigate(['/dashboard']);
        });
    }

    // Navigate to the forgot password page
    gotoForgotPassword() {
        this.router.navigate(['/forgot/password']);
    }

    // Navigate to the reset password page
    gotoResetPassword() {
        this.router.navigate(['reset/reset-password']);
    }
}
