import { Directive, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';

// third party imports
import { fromEvent, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, takeUntil } from 'rxjs/operators';

@Directive({
    selector: '[appDelayedInput]',
    standalone: true,
})
export class DelayedInputDirective implements OnInit, OnDestroy {
    @Input() delayTime = 300; // Default debounce time
    @Output() delayedInput = new EventEmitter<string>();

    private readonly destroy$ = new Subject<void>();

    constructor(private readonly elementRef: ElementRef<HTMLInputElement>) {}

    ngOnInit(): void {
        fromEvent<Event>(this.elementRef.nativeElement, 'input') // Specify Event type for fromEvent
            .pipe(
                debounceTime(this.delayTime),
                map((event: Event) => (event.target as HTMLInputElement).value.trim()),
                distinctUntilChanged(),
                takeUntil(this.destroy$),
            )
            .subscribe((value: string) => {
                this.delayedInput?.emit(value); // Emit the trimmed value directly
            });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}

export class DelayedInputModule {}
