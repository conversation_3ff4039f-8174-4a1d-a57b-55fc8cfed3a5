import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class EmployeeService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/account/user', '/api/account/users');
    }

    updateEmployeeStatus(id: string, data: any): Observable<RestResponse> {
        return this.updateRecord(`/api/account/user/${id}/status`, data);
    }

    fetchMyProfileDetails(): Observable<RestResponse> {
        return this.getRecord('/api/account/userProfile');
    }

    updateMyProfile(data: any): Observable<RestResponse> {
        return this.updateRecord('/api/account/userProfile', data);
    }

    fetchForDriverDropdown(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/user/driver/selection', filterParam);
    }
}
