@import "../../../../variables.scss";
@import "../../../../assets/scss/mixins.scss";

.notification-scroll-wrapper {
    overflow-y: auto;
    max-height: 90vh;
    padding-right: 5px;
}

.notification-card {
    gap: 1rem;
    padding: 0.5rem 1rem;
    background-color: $white-color;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;
    transition: all 0.3s ease;
    border-top: 4px solid var(--primary-color);

    &.unread {
        background-color: var(--uploader-background-color);
    }

    &.read {
        opacity: 0.85;
    }

    &:hover {
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
    }

    .notification-header {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 14px;

        img {
            height: 20px;
            width: 20px;
        }

        @media(min-width: 768px) {
            img {
                height: 25px;
                width: 25px;
            }
        }
    }

    .title {
        font-weight: $font-weight-600;
        font-size: 14px;
        color: #1d1d1f;
    }

    .notification-message {
        font-size: 14px;
        color: #555;
        line-height: 1.4;
    }

    @media(min-width: 992px) {
        .message-container {
            margin-top: -4px;
        }
    }

    .actions {
        display: flex;
        gap: 0.5rem;

        button {
            border: none;
            background: transparent;
            cursor: pointer;
            padding: 6px;
            border-radius: 6px;
            transition: background 0.2s ease;
            @include flex-center;

            i {
                font-size: 1.1rem;
            }

            &.read {
                color: #3f51b5;
            }

            &.delete {
                color: #ef5350;
            }

            &:hover {
                background-color: rgba(0, 0, 0, 0.05);
            }
        }
    }
}

.scrollable-area {
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }

    scrollbar-width: none;
    -ms-overflow-style: none;

    &.scrollable-full {
        height: 100vh;
    }

    &.scrollable-reduced {
        height: calc(100vh - 100px);
    }
}

.notification-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 16px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.25s ease-in-out;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    color: #1e293b;

    svg {
        width: 18px;
        height: 18px;
        fill: currentColor;
        transition: transform 0.2s ease;
    }

    &:hover svg {
        transform: scale(1.1);
    }

    &:active {
        transform: scale(0.98);
    }

    &.read-btn {
        background: #fff8e1;
        border: 2px solid #facc15; // yellow-400
        color: #d97706; // amber-700
    }

    &.clear-btn {
        background: #ffe4e6;
        border: 2px solid #f87171; // rose-400
        color: #b91c1c; // red-700
    }
}

.no-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 70vh;
    text-align: center;
    color: #555;

    .no-notifications-img {
        max-width: 280px;
        width: 100%;
        margin-bottom: 20px;
    }

    h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: #222;
    }

    p {
        font-size: 1rem;
        color: #777;
    }
}

.loading-container {
    height: calc(100vh - 80px);
}

.loader {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    position: relative;
    animation: rotate 1s linear infinite;
}

.loader::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    inset: 0px;
    border-radius: 50%;
    border: 5px solid #ffc107;
    animation: prixClipFix 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
    }

    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    }

    100% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
    }
}