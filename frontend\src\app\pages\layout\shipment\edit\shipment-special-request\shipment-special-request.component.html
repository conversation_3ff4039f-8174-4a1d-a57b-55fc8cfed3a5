<div class="site-page-container mt-3">
    <div class="site-card">
        <form #shipmentSpecialRequestForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <!--Label  -->
                        <div class="label-wrap-box mt-2 mb-24">
                            <span>{{"RATE_SHEET.SpecialRequestInfo" | translate}}</span>
                        </div>

                        <!-- Oversize Rate Request -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="oversizeRadio">{{"SHIPMENT.oversize" |
                                    translate}}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oversize" [value]="true"
                                                [(ngModel)]="shipment.isOversize" id="oversizeYes" [disabled]="disabled"
                                                (change)="onRadioButtonChange(shipment.isOversize,'oversizeRate')" />
                                            <label class="form-check-label" for="oversizeYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oversize" [value]="false"
                                                [(ngModel)]="shipment.isOversize" id="oversizeNo" [disabled]="disabled"
                                                (change)="onRadioButtonChange(shipment.isOversize,'oversizeRate')" />
                                            <label class="form-check-label" for="oversizeNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #OversizeRate="ngModel"
                                            appCurrencyFormatter [maxlength]="15" [(ngModel)]="shipment.oversizeRate"
                                            placeholder="" required="required" name="oversizeRate"
                                            [disabled]="!shipment.isOversize"
                                            [ngClass]="{'is-invalid': !OversizeRate.valid && shipment.isOversize && onClickValidation}"
                                            [required]="shipment.isOversize"
                                            placeholder="'RATE_SHEET.oversizeRate | translate'">

                                        <label for="oversizeRate">{{
                                            "RATE_SHEET.oversizeRate" | translate
                                            }} </label>

                                        <app-validation-message [field]="OversizeRate" *ngIf="shipment.isOversize"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Enclosed -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="enclosedRadio">{{"Quotation.isEnclosed" |
                                    translate}}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enclosed" [value]="true"
                                                id="enclosedYes" [(ngModel)]="shipment.isEnclosed" [disabled]="disabled"
                                                (change)="onRadioButtonChange(shipment.isEnclosed,'enclosedRate')" />
                                            <label class="form-check-label" for="enclosedYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enclosed" [value]="false"
                                                id="enclosedNo" [(ngModel)]="shipment.isEnclosed" [disabled]="disabled"
                                                (change)="onRadioButtonChange(shipment.isEnclosed,'enclosedRate')" />
                                            <label class="form-check-label" for="enclosedNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #EnclosedRate="ngModel"
                                            appCurrencyFormatter [(ngModel)]="shipment.enclosedRate" name="enclosedRate"
                                            [maxlength]="15" [disabled]="!shipment.isEnclosed"
                                            placeholder="'Quotation.enclosedRate' | translate'"
                                            [ngClass]="{'is-invalid': !EnclosedRate.valid && shipment.isEnclosed && onClickValidation}"
                                            [required]="shipment.isEnclosed">
                                        <label for="enclosedRate">
                                            {{ "RATE_SHEET.enclosedRate" | translate }}
                                        </label>

                                        <app-validation-message [field]="EnclosedRate" *ngIf="shipment.isEnclosed"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Perishable -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="PerishableRadio">{{ "Quotation.isPerishable" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="perishableRadio"
                                                [value]="true" [(ngModel)]="shipment.isPerishable" [disabled]="disabled"
                                                id="PerishableRadioYes"
                                                (change)="onRadioButtonChange(shipment.isPerishable,'perishableRate')" />
                                            <label class="form-check-label" for="PerishableRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="perishableRadio"
                                                [value]="false" [(ngModel)]="shipment.isPerishable"
                                                [disabled]="disabled" id="PerishableRadioNo"
                                                (change)="onRadioButtonChange(shipment.isPerishable,'perishableRate')" />
                                            <label class="form-check-label" for="PerishableRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #PerishableRate="ngModel"
                                            appCurrencyFormatter [(ngModel)]="shipment.perishableRate" placeholder=""
                                            name="perishableRate" [maxlength]="15" [disabled]="!shipment.isPerishable"
                                            [ngClass]="{'is-invalid': !PerishableRate.valid && shipment.isPerishable && onClickValidation}"
                                            [required]="shipment.isPerishable">
                                        <label for="perishableRate">{{
                                            "RATE_SHEET.perishableRate" | translate
                                            }}</label>

                                        <app-validation-message [field]="PerishableRate" *ngIf="shipment.isPerishable"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Rush Rate Request -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="rushRequestRate">{{
                                    "SHIPMENT.RushedRateImmediately" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="rushRequestRate"
                                                [value]="true" id="rushRequestRateYes" [disabled]="disabled"
                                                [(ngModel)]="shipment.isRushRequest" id="rushRequestRateYes"
                                                (change)="onRadioButtonChange(shipment.isRushRequest,'rushRequestRate')" />
                                            <label class="form-check-label" for="rushRequestRateYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="rushRequestRate"
                                                [value]="false" [(ngModel)]="shipment.isRushRequest"
                                                [disabled]="disabled" id="rushRequestRateNo"
                                                (change)="onRadioButtonChange(shipment.isRushRequest,'rushRequestRate')" />
                                            <label class="form-check-label" for="rushRequestRateNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #RateRushRequest="ngModel"
                                            [(ngModel)]="shipment.rushRequestRate" placeholder="" name="rateRushRequest"
                                            appCurrencyFormatter [maxlength]="15"
                                            placeholder="'SHIPMENT.RushedRateImmediately | translate'"
                                            [disabled]="!shipment.isRushRequest"
                                            [ngClass]="{'is-invalid': !RateRushRequest.valid && shipment.isRushRequest && onClickValidation}"
                                            [required]="shipment.isRushRequest">
                                        <label for="floatingInputValue">
                                            {{ "SHIPMENT.RushedRateImmediately" | translate }} Rate
                                        </label>

                                        <app-validation-message [field]="RateRushRequest" *ngIf="shipment.isRushRequest"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Fragile Rate -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="fragileRadio"> {{ "Quotation.isFragile" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fragileRadio"
                                                [value]="true" name="fragile" [(ngModel)]="shipment.isFragile"
                                                id="fragileRadioYes" [disabled]="disabled"
                                                (change)="onRadioButtonChange(shipment.isFragile,'fragileRate')" />
                                            <label class="form-check-label" for="fragileRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fragileRadio"
                                                (change)="onRadioButtonChange(shipment.isFragile,'fragileRate')"
                                                id="fragileRadioNo" [value]="false" name="secured" [disabled]="disabled"
                                                [(ngModel)]="shipment.isFragile" />
                                            <label class="form-check-label" for="fragileRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #FragileRate="ngModel"
                                            [(ngModel)]="shipment.fragileRate" placeholder="" appCurrencyFormatter
                                            name="fragileRate" [maxlength]="15" [disabled]="!shipment.isFragile"
                                            placeholder="'Quotation.fragileRate | translate'"
                                            [ngClass]="{'is-invalid': !FragileRate.valid && shipment.isFragile && onClickValidation}"
                                            [required]="shipment.isFragile">
                                        <label for="fragileRate">{{
                                            "Quotation.isFragile" | translate
                                            }} Rate</label>

                                        <app-validation-message [field]="FragileRate" *ngIf="shipment.isFragile"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Dangerous Goods -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="DangerousGoodsRadio">{{
                                    "Quotation.isDangerousGoods" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="dangerousGoodsRadio"
                                                [value]="true" id="dangerousGoodsRadioYes" [disabled]="disabled"
                                                [(ngModel)]="shipment.isDangerousGoods" id="DangerousGoodsRadioYes"
                                                (change)="onRadioButtonChange(shipment.isDangerousGoods,'dangerousGoodsRate')" />
                                            <label class="form-check-label" for="DangerousGoodsRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="dangerousGoodsRadio"
                                                [value]="false" [(ngModel)]="shipment.isDangerousGoods"
                                                [disabled]="disabled" id="DangerousGoodsRadioNo"
                                                (change)="onRadioButtonChange(shipment.isDangerousGoods,'dangerousGoodsRate')" />
                                            <label class="form-check-label" for="DangerousGoodsRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #DangerousGoodsRate="ngModel"
                                            [(ngModel)]="shipment.dangerousGoodsRate" placeholder=""
                                            placeholder="'RATE_SHEET.dangerousGoodsRate| translate'"
                                            [disabled]="!shipment.isDangerousGoods" name="dangerousGoodsRate"
                                            appCurrencyFormatter [maxlength]="15"
                                            [ngClass]="{'is-invalid': !DangerousGoodsRate.valid && shipment.isDangerousGoods && onClickValidation}"
                                            [required]="shipment.isDangerousGoods">
                                        <label for="dangerousGoodsRate">
                                            {{ "RATE_SHEET.dangerousGoodsRate" | translate }}
                                        </label>

                                        <app-validation-message [field]="DangerousGoodsRate"
                                            *ngIf="shipment.isDangerousGoods"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                [routerLink]="['/dashboard/shipments']">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="save(shipmentSpecialRequestForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="onNext(shipmentSpecialRequestForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" *ngIf="disabled" appRippleEffect
                                type="button" (click)="onNextOrBackClick.emit(6)">
                                <div class="site-button-inner">
                                    {{ "COMMON.NEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>