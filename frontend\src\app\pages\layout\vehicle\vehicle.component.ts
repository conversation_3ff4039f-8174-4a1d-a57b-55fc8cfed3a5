// Angular Core Modules
import { CommonModule } from '@angular/common';
import { <PERSON>mpo<PERSON>, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-party Modules
import { NgbAccordionModule, NgbPopoverModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';

// Application Components, Directives, Pipes and Services
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { LoadingService } from '../../../services/loading.service';
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';

// Application Configurations and Models
import { Constant } from '../../../config/constants';
import { Vehicle } from '../../../models/vehicle';

// Feature-specific Manager
import { FormsModule } from '@angular/forms';
import { TypeAheadService } from '../../../shared/services/typeahead-search.service';
import { VehicleManager } from './vehicle.manager';

@Component({
    selector: 'app-vehicle',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        RouterLink,
        DataTablesModule,
        NgbAccordionModule,
        NgbTooltipModule,
        NgSelectModule,
        TranslateModule,
        DateFormatPipe,
        DelayedInputDirective,
        TooltipEllipsisDirective,
        NgbPopoverModule,
    ],
    templateUrl: './vehicle.component.html',
    styleUrls: ['./vehicle.component.scss'],
})
export class VehicleComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
    @ViewChild('action') action!: TemplateRef<any>;
    @ViewChild('createdOn') createdOn!: TemplateRef<string>;
    @ViewChild('deliveryAddress') deliveryAddress!: TemplateRef<string>;
    @ViewChild('endDate') endDate!: TemplateRef<string>;
    @ViewChild('name') name!: TemplateRef<string>;
    @ViewChild('pickupAddress') pickupAddress!: TemplateRef<string>;
    @ViewChild('startDate') startDate!: TemplateRef<string>;

    vehicles!: Array<Vehicle>;
    resourceType: string = Constant.RESOURCE_TYPE.VEHICLE;

    constructor(
        protected vehicleManager: VehicleManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
        protected typeAheadService: TypeAheadService,
    ) {
        super(vehicleManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.vehicles = new Array<Vehicle>();

        this.filterParam.fileName = this.resourceType;

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.VEHICLE;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.RATE_SHEET_LIST;

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            name: this.name,
            createdOn: this.createdOn,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override onFetchCompleted() {
        this.vehicles = this.records.map((data) => Vehicle.fromResponse(data));
        super.onFetchCompleted();
    }
}
