<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [routerLink]="['/dashboard/vehicle/edit/0']">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': vehicles.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'USERS.Name' | translate }}</th>
                            <th>{{ 'COMMON.CREATED_ON' | translate }}</th>
                            <th class="th-action text-center">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #name let-data="adtData">
                <a class="action-button text-capitalize ellipsis-2-line"
                    [routerLink]="['/dashboard/rate-sheet/edit/' + data.id]" [appTooltipEllipsis]="data?.name"
                    ngbTooltip>
                    <strong>{{ data?.name }}</strong>
                </a>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }} </span>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons justify-content-center">
                    <button class="edit-btn" [routerLink]="['/dashboard/vehicle/edit/' + data.id]"
                        ngbTooltip="Edit vehicle">
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>

                    <button class="delete-btn" (click)="remove(data.id, resourceType)" ngbTooltip="Delete vehicle">
                        <img src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>