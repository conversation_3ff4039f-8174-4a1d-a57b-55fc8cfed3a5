import { CommonUtil } from '../../shared/common.util';

export class Profile {
    confirmPassword!: string;
    fullName!: string;
    oldPassword!: string;
    password!: string;
    phoneNumber!: string;
    token!: string;
    uniqueCode!: string;

    // Helper method to trim all string properties
    private trimProperties() {
        this.oldPassword = this.oldPassword?.trim();
        this.password = this.password?.trim();
        this.confirmPassword = this.confirmPassword?.trim();
        this.token = this.token?.trim();
        this.uniqueCode = this.uniqueCode?.trim();
    }

    // Helper method to trim a single string property
    private trimString(value: string): string {
        return value?.trim() ?? '';
    }

    // Validate change password request
    isValidChangePasswordRequest(form: any): boolean {
        const validators = {
            oldPassword: this.oldPassword,
            password: this.password,
            confirmPassword: this.confirmPassword,
        };

        for (const [key, value] of Object.entries(validators)) {
            if (CommonUtil.isNullOrUndefined(value) || this.trimString(value) === '') {
                form.controls[key].setErrors({ invalid: true });
                return false;
            }
        }

        return true;
    }

    // Prepare for request
    forRequest(): this {
        this.trimProperties();
        return this;
    }
}
