import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

export class ShipmentSpecialRequest extends BaseModel {
    tenantId!: number;
    slug!: string;

    oversizeRate!: number;
    rushRequestRate!: number;
    enclosedRate!: number;
    fragileRate!: number;
    perishableRate!: number;
    dangerousGoodsRate!: number;

    // variables for special request
    isOversize: boolean = false;
    isEnclosed: boolean = false;
    isPerishable: boolean = false;
    isRushedRateImmediately: boolean = false;
    isFragile: boolean = false;
    isDangerousGoods: boolean = false;

    constructor(data?: Partial<ShipmentSpecialRequest>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): ShipmentSpecialRequest {
        return new ShipmentSpecialRequest(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        return this;
    }
}
