// Angular core modules
import { TranslateService } from '@ngx-translate/core';

// App-specific modules
import { CommonUtil } from '../shared/common.util';
import { ToastService } from '../shared/services/toast.service';

export abstract class BaseModel {
    id!: string;
    createdOn!: Date;
    updatedOn!: Date;
    isDeleted: boolean;
    isActive: boolean;
    createdBy!: number;
    updatedBy!: number;
    totalCount: any;

    constructor() {
        this.isDeleted = false;
        this.isActive = true;
    }

    isNullOrUndefinedAndEmpty(name: string) {
        return CommonUtil.isNullOrUndefined(name) || name.trim() === '';
    }

    abstract isValidateRequest(form: any, toastService: ToastService, translate: TranslateService): any;

    abstract forRequest(): any;

    trimMe(val: string) {
        return CommonUtil.isNullOrUndefined(val) ? val : val.trim();
    }
}
