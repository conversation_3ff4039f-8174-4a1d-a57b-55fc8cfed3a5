@import '../../../variables.scss';

.card-background {
    background-color: $card-background-color;
    box-shadow: 0 0.1875rem 0.75rem 0 rgba(47, 43, 61, 0.14);
    border: none;
}

.profile-form-wrapper {
    display: inline-block;
    width: 100%;
    max-width: 100vw !important;
}

// Tabs Wrapper Css //
.tabs-wrapper {
    display: flex;
    flex-direction: row;
    overflow: auto;
    overflow-y: hidden;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
    padding: 0px;
    -webkit-overflow-scrolling: touch;

    @media (min-width: 768px) {
        padding: 0 40px;
    }

    ::-webkit-scrollbar {
        display: none; // Safari and Chrome
    }

    &:focus-visible {
        outline: none;
    }

    .custom-tab {
        padding: 8px 16px;
        width: 210px;
        min-width: 200px;
        font-size: $font-size-14;
        font-weight: $font-weight-500;
        letter-spacing: 0;
        color: $black-color;
        cursor: pointer;
        justify-content: start;
        display: flex;
        align-items: center;
        border-radius: 8px;
        &:hover {
            color: $black-color;
            background: $light-hover-color;

            .tab-icon {
                svg {
                    path {
                        fill: $black-color;
                        opacity: unset;
                    }
                }
            }
        }

        .tab-icon {
            margin-right: 6px;
            color: $black-color;

            svg {
                path {
                    fill: $black-color;
                    opacity: 0.9;
                }
            }
        }

        &.active {
            position: relative;
            font-weight: $font-weight-600;
            color: $black-color;
            background: var(--primary-color);
            border: 0;

            &::after {
                content: '';
                position: absolute;
                border-radius: 2px;
                left: 0;
                width: 100%;
                bottom: 0;
            }

            .tab-icon {
                svg {
                    path {
                        fill: $black-color;
                        opacity: unset;
                    }
                }
            }
        }
    }
}
