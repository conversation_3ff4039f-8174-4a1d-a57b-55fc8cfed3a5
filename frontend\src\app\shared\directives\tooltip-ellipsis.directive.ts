import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';

@Directive({
    selector: '[appTooltipEllipsis]',
    standalone: true
})
export class TooltipEllipsisDirective {
    @Input('appTooltipEllipsis') tooltipText: string | null = null;
    @Input() toolTipContainer: string | null = null;

    constructor(private el: ElementRef, private tooltip: NgbTooltip) {}

    @HostListener('mouseenter') onMouseEnter() {
        if (this.toolTipContainer) {
            this.tooltip.container = `${this.toolTipContainer}`;
        }

        if (this.isOverflow()) {
            this.tooltip.ngbTooltip = this.tooltipText;
            this.tooltip.open();
        } else {
            this.tooltip.close();
        }
    }

    @HostListener('mouseleave') onMouseLeave() {
        this.tooltip.close();
    }

    private isOverflow(): boolean {
        const element = this.el.nativeElement;
        return element.scrollHeight > element.clientHeight;
    }
}
