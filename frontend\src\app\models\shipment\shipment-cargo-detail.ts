import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

export class ShipmentCargoDetail extends BaseModel {
    tenantId!: number;
    slug!: string;
    description!: string;
    cargoType!: string;
    weight!: number;
    volume!: number;
    length!: number;
    freight: number = 0;
    gst: number = 0;
    total: number = 0;
    weightInPounds!: number;
    fuelCharges: number = 0;
    height!: number;
    freightRate!: number;
    weightType!: string;
    width!: number;
    shipment!: string;
    quantity: number = 1;
    rateType: string = 'WEIGHT';

    constructor(data?: Partial<ShipmentCargoDetail>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): ShipmentCargoDetail {
        return new ShipmentCargoDetail(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        return this;
    }
}
