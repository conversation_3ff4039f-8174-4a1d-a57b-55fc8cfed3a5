<div aria-live="polite" aria-atomic="true" class="position-relative">
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1200">
        @for (toast of toasts; track toast.type) {
        <ng-container>
            @if (toast.type) {
            <!-- if we want to show header on toaster then we add that header -->

            <ngb-toast [autohide]="true" [ngClass]="{
                        'bg-success text-light': toast.type === 'success',
                        'bg-danger': toast.type === 'danger',
                        'text-light': toast.type === 'danger' || toast.type === 'success',
                        'bg-info': toast.type === 'info',
                        'bg-warning': toast.type === 'warning'
                      }">
                {{toast.message}}
            </ngb-toast>
            }
        </ng-container>
        } @empty {
        }
    </div>