import { BehaviorSubject } from 'rxjs';

export class CommonEventService {
    performSearch = new BehaviorSubject<any>(null);
    performClick = new BehaviorSubject<any>(null);

    search = this.performSearch.asObservable();
    clickevent = this.performClick.asObservable();

    showSearch(data: any, callback1: Function, callback2: Function) {
        this.performSearch.next({ data: data, callback1: callback1, callback2: callback2 });
    }

    showImageOnClick(data: any) {
        this.performClick.next({ data: data });
    }

    clearSearch() {
        this.performSearch.next(null);
    }
}
