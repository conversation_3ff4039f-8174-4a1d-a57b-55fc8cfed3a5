import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class LocalStorageService {
    private storageChangeSubject = new Subject<void>();

    constructor() {
        window.addEventListener('storage', this.handleStorageChange);
    }

    ngOnDestroy(): void {
        window.removeEventListener('storage', this.handleStorageChange);
    }

    private handleStorageChange = (event: StorageEvent): void => {
        this.storageChangeSubject.next();
    };

    clearAll() {
        this.remove('tempAuthKey');
        this.remove('tempVerifyKey');
    }

    onStorageChange() {
        return this.storageChangeSubject.asObservable();
    }

    set(key: string, value: any) {
        localStorage.setItem(key, value);
    }

    get(key: string): any {
        return localStorage.getItem(key);
    }

    remove(key: string): any {
        return localStorage.removeItem(key);
    }

    setBool(key: string, value: boolean) {
        localStorage.setItem(key, String(value));
    }

    getBool(key: string): boolean {
        return localStorage.getItem(key) === 'true';
    }

    setObject(key: string, value: object) {
        localStorage.setItem(key, JSON.stringify(value));
    }
}
