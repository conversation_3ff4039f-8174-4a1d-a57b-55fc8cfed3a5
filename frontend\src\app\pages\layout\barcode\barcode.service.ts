import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class BarcodeService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/barcode', '/api/barcodes');
    }

    getBarCodes(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/barcodes/generate', data);
    }

    downloadBarcodePDfFile(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/barcodes/pdf/download', filterParam);
    }

    markBarcodesPrinted(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/barcode/mark/printed', filterParam);
    }
}
