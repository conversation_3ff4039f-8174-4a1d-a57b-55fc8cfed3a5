// Angular Core & Common Modules
import { CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-Party Libraries
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Application Configuration & Constants
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { Constant } from '../../../../config/constants';

// Services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

// Utilities
import { CommonUtil } from '../../../../shared/common.util';

// Models
import { RestResponse } from '../../../../models/common/auth.model';
import { Shipment } from '../../../../models/shipment/shipment';

// Reusable / Shared Components
import { TabMenuComponent } from '../../../../shared/tab-menu/tab-menu/tab-menu.component';

// Feature Components (Shipment)
import { ShipmentBasicInfoComponent } from './shipment-basic-info/shipment-basic-info.component';
import { CargoDetailsComponent } from './shipment-cargo-details/shipment-cargo-details.component';
import { ShipmentCustomerCalculationsComponent } from './shipment-customer-calculations/shipment-customer-calculations.component';
import { ShipmentDetailComponent } from './shipment-detail/shipment-detail.component';
import { ShipmentDocumentsComponent } from './shipment-documents/shipment-documents.component';
import { ShipmentPickupDeliveryComponent } from './shipment-pickup-delivery/shipment-pickup-delivery.component';
import { ShipmentPodDeliveryComponent } from './shipment-pod-delivery/shipment-pod-delivery.component';
import { ShipmentSpecialRequestComponent } from './shipment-special-request/shipment-special-request.component';

// Manager
import { BreadcrumbService } from '../../../../shared/services/breadcrumb.service';
import { ShipmentManager } from '../shipment.manager';

@Component({
    selector: 'app-shipment-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TranslateModule,
        NgbDatepickerModule,
        NgSelectModule,
        TabMenuComponent,
        ShipmentBasicInfoComponent,
        ShipmentPickupDeliveryComponent,
        CargoDetailsComponent,
        ShipmentSpecialRequestComponent,
        ShipmentCustomerCalculationsComponent,
        ShipmentPodDeliveryComponent,
        ShipmentDocumentsComponent,
        ShipmentDetailComponent,
    ],
    templateUrl: './shipment-edit.component.html',
    styleUrls: ['./shipment-edit.component.scss'],
})
export class ShipmentEditComponent extends BaseEditComponent implements OnInit {
    shipment!: Shipment;

    selectedStep = 1;
    customerStatus = Constant.CUSTOMER_STATUS;
    stepperList = this.shipmentManager.stepperList;
    shipmentStatusOptions = Constant.SHIPMENT_STATUS_OPTIONS;

    intialRateSheetValue!: string | null;
    userRole!: string;
    disabled!: boolean;

    isOnFetchDropDownCompleted: boolean = true;

    constructor(
        public authService: AuthService,
        public commonUtil: CommonUtil,
        protected override route: ActivatedRoute,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
        protected location: Location,
        protected shipmentManager: ShipmentManager,
        protected breadcrumbService: BreadcrumbService
    ) {
        super(shipmentManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.userRole = this.authService.getRoles()[0];
        this.initializeShipment();

        const { step, firstLoad } = this.getNavigationContext();

        this.selectedStep = step <= this.stepperList.length ? (firstLoad ? 1 : step) : 1;
        this.shipment.step = this.selectedStep;

        this.onStepSelection();
    }

    ngOnDestroy() {
        this.breadcrumbService.clearNumber();
    }

    override onFetchCompleted() {
        this.shipment = Shipment.fromResponse(this.record);
        this.setRecord(this.shipment);
        this.setPickupDefaults();
        if (this.shipment.status === 'DELIVERED') {
            this.shipmentStatusOptions = Constant.SHIPMENT_STATUS_AFTER_DELIVERY;
        }
        if (!this.isOnFetchDropDownCompleted) {
            this.loadingService.show();
        }

        this.disabled =
            (this.userRole == 'ROLE_OFFICE_ADMIN' && this.shipment.status === 'COMPLETED') ||
            (this.userRole == 'ROLE_SUPER_ADMIN' &&
                this.shipment.status === 'COMPLETED' &&
                this.shipment.paymentStatus == 'INVOICED');

        if (this.shipment?.refID) {
            this.breadcrumbService.setNumber(this.shipment?.refID);
        }
    }

    override onSaveSuccess(data: any): void {
        this.router.navigate(['/dashboard/shipments']);
    }

    onSelectionCallBack(newSelection: number): void {
        const lastStoredStep = history.state.step ?? 1;
        if (lastStoredStep <= 2) {
            if (newSelection <= lastStoredStep) {
                this.onClickValidation = false;
                this.selectedStep = newSelection;
                this.init();
                this.onStepSelection();
                return;
            }
        } else {
            this.selectedStep = newSelection;
            this.init();
            this.onStepSelection();
            return;
        }

        this.onClickValidation = true;
    }

    onStepSelection(): void {
        this.stepperList.forEach((step: any) => {
            step.selected = step.stepNumber === this.selectedStep;
        });
    }

    tabMenuUpdate(step: number, isFormType: boolean): void {
        if (step > this.selectedStep) {
            if (isFormType) {
                this.saveShipmentStep(step);
            } else {
                this.selectedStep = step;
                this.init();
                this.onStepSelection();
            }
        } else {
            this.onSelectionCallBack(step);
        }
    }

    async saveShipmentStep(step?: number): Promise<void> {
        if (this.shipment.step <= this.stepperList.length) {
            if (step) {
                this.shipment.step = Math.max(step, this.shipment.step);
            } else if (this.selectedStep === history.state.step) {
                this.shipment.step += 1;
            }
        }

        this.shipment.startTime = this.shipment.startTime ?? null;
        this.shipment.endTime = this.shipment.endTime ?? null;

        const forRequestData = JSON.parse(JSON.stringify(this.shipment));

        this.loadingService.show();
        const method = !this.shipment?.id ? 'save' : 'update';
        this.shipmentManager[method](forRequestData, method == 'save').subscribe({
            next: (response: RestResponse) => {
                const isSave = method === 'save';
                if (step) {
                    if (isSave) {
                        this.shipment = Shipment.fromResponse(response.data);
                    } else {
                        this.shipment.id = response.data;
                    }

                    this.setRecord(this.shipment);
                    this.setPickupDefaults();

                    this.request = {
                        isNewRecord: !this.shipment.id,
                        recordId: this.shipment.id || 0,
                    };

                    if (this.shipment.step <= this.stepperList.length) {
                        isSave
                            ? this.updateUrlWithNewId(this.shipment.id, this.shipment.step)
                            : this.updateUrlWithNewState(this.shipment.id, this.shipment.step);
                    }
                    this.loadingService.hide();
                    this.tabMenuUpdate(step, false);
                } else {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                    this.onSaveSuccess(response.data);
                }
            },
            error: (error: { message: string }) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    onfetchDropdownDataCompleted(data: any) {
        this.isOnFetchDropDownCompleted = data;
    }

    private setPickupDefaults(): void {
        const userDetail = this.shipment?.customerUserDetail;
        const customer = userDetail?.customerDetail;

        this.shipment.pickupAddressDetail = this.shipment?.pickupAddressDetail?.id
            ? this.shipment.pickupAddressDetail
            : userDetail?.addressDetail;

        this.shipment.pickupCompanyName ??= customer?.companyName;
        this.shipment.pickupContactPersonName ??= customer?.keyContact;
        this.shipment.pickupContactCountryCode ??= customer?.keyContactCountryCode;
        this.shipment.pickupContactPersonPhone ??= customer?.keyContactPhone;

        this.intialRateSheetValue = this.shipment.rateSheet;
    }

    private updateUrlWithNewState(id: string, step: number) {
        const url = `/dashboard/shipment/edit/${id}`;
        const newState = { step, firstLoad: true };
        this.location.replaceState(url, '', newState);
    }

    private updateUrlWithNewId(newId: string, step: number) {
        this.router.navigateByUrl('/').then(() => {
            this.router.navigate(['/dashboard/shipment/edit', newId], {
                state: { step: step, firstLoad: false },
            });
        });
    }

    private initializeShipment(): void {
        this.shipment = new Shipment();

        this.shipment.startTime = this.shipment.startTime ?? '';
        this.shipment.endTime = this.shipment.endTime ?? '';

        this.setRecord(this.shipment);

        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;
        this.init();
    }

    private getNavigationContext(): { step: number; firstLoad: boolean } {
        let step = 1;
        let firstLoad = false;

        const encoded = this.route.snapshot.queryParamMap.get('q');
        if (encoded) {
            try {
                const decoded = JSON.parse(atob(encoded));
                step = decoded.step ?? step;
                firstLoad = decoded.firstLoad ?? firstLoad;
                history.replaceState({ ...history.state, step, firstLoad }, '');
            } catch (e) {
                console.warn('Failed to decode q param', e);
            }
        }

        if (history.state?.step) {
            step = history.state.step;
            firstLoad = !!history.state.firstLoad;
        }

        return { step, firstLoad };
    }
}
