// Angular core
import { Component, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';

// Angular common and forms
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Angular router
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// App constants and models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Shipment } from '../../../../../models/shipment/shipment';

// Shared services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared components
import { FileUploaderComponent } from '../../../../../shared/common-component/file-uploader/file-uploader.component';
import { NgCustomSignaturePadComponent } from '../../../../../shared/common-component/ng-custom-signature-pad/ng-custom-signature-pad.component';

@Component({
    selector: 'app-shipment-pod-delivery',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        TranslateModule,
        NgSelectModule,
        FileUploaderComponent,
        NgCustomSignaturePadComponent,
    ],
    templateUrl: './shipment-pod-delivery.component.html',
    styleUrl: './shipment-pod-delivery.component.scss',
})
export class ShipmentPodDeliveryComponent {
    @ViewChild('NgCustomSignaturePadComponent', { static: false })
    ngCustomSignaturePadComponent!: NgCustomSignaturePadComponent;

    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected typeAheadService: TypeAheadService,
        protected toastService: ToastService,
    ) { }

    uploaderVisibility: boolean = true;
    signaturePadVisibility: boolean = true;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['shipment']?.currentValue?.id) {
            this.uploaderVisibility = false;
            this.signaturePadVisibility = false;
            setTimeout(() => {
                this.uploaderVisibility = true;
                this.signaturePadVisibility = true;
            });
        }
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        const isImagesAdded = this.shipment.podImages?.some((image) => !image.isDeleted);

        if (this.shipment.podAdded && !this.shipment.podSignatures) {
            this.toastService.error('Please provide a signature.');
            this.onClickValidation = true;
            return;
        }

        if (this.shipment.podAdded && !isImagesAdded) {
            this.toastService.error('Please provide at least one image.');
            this.onClickValidation = true;
            return;
        }

        this.saveButtonClicked.emit();
    }

    onBack() {
        this.onNextOrBackClick.emit(7);
    }

    onSignatureUpdated($event: any) {
        this.shipment.podSignatures = $event;
    }

    onRadioButtonChange(data: any) {
        if (!data) {
            this.shipment.podNotes = null;
            this.ngCustomSignaturePadComponent.clearSignature();
            this.shipment.podImages.forEach((image) => (image.isDeleted = true));
        } else {
            this.shipment.status = 'DELIVERED';
        }
    }
}
