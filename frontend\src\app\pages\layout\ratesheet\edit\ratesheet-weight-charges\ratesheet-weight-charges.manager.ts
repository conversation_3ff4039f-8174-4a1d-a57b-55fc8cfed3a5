// Angular imports
import { Injectable } from '@angular/core';
import { BaseManager } from '../../../../../config/base.manager';
import { LoadingService } from '../../../../../services/loading.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { RateSheetWeightChargesService } from './ratesheet-weight-charges.service';

@Injectable({
    providedIn: 'root',
})
export class RateSheetSpecialRequestManager extends BaseManager {
    constructor(
        protected rateSheetWeightChargesService: RateSheetWeightChargesService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(rateSheetWeightChargesService, loadingService, toastService);
    }
}
