// Angular Imports and  Services
import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from '../../shared/services/local-storage.service';

@Component({
    selector: 'app-forbidden',
    standalone: true,
    imports: [],
    templateUrl: './forbidden.component.html',
    styleUrl: './forbidden.component.scss',
})
export class ForbiddenComponent implements OnInit {
    constructor(private readonly localStorageService: LocalStorageService) {}
    ngOnInit(): void {
        this.localStorageService.clearAll();
    }
}
