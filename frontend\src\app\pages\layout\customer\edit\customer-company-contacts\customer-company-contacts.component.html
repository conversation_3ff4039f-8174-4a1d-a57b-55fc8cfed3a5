<div class="site-page-container mt-3">
    <div class="site-card">
        <form #customerForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <div class="label-wrap-box">
                            <span>{{"CUSTOMER.keyContactInfo"| translate}}</span>
                        </div>

                        <!-- Key Contact Name  -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="keyContact" #KeyContact="ngModel"
                                    [(ngModel)]="customer.customerDetail.keyContact"
                                    placeholder="{{'CUSTOMER.keyContactName' | translate }}"
                                    [ngClass]="{'is-invalid': !KeyContact.valid && onClickValidation }" />
                                <label for="keyContact">{{"CUSTOMER.keyContactName" | translate}}</label>
                                <app-validation-message [field]="KeyContact" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Key Contact Phone Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2 mb-14">
                            <app-dial-code-input [(countryCode)]="customer.countryCode"
                                [(number)]="customer.customerDetail.keyContactPhone"
                                [onClickValidation]="onClickValidation" fieldName="keyContactPhone"
                                nameCode="keyContactCountryCode"
                                [labelName]="'CUSTOMER.keyContactPhone' | translate"></app-dial-code-input>
                        </div>

                        <!-- Key Contact Email -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="keyContactEmail"
                                    #KeyContactEmail="ngModel" [(ngModel)]="customer.customerDetail.keyContactEmail"
                                    placeholder="{{ 'CUSTOMER.keyContactEmail' | translate }}" [ngClass]="{
                                        'is-invalid': !KeyContactEmail.valid && onClickValidation
                                    }" appEmailValidator [allowEmpty]="true" />
                                <label for="keyContactEmail">{{ "CUSTOMER.keyContactEmail" | translate
                                    }}</label>
                                <app-validation-message [field]="KeyContactEmail"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Key Contact Position -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="keyContactPosition"
                                    #KeyContactPosition="ngModel"
                                    [(ngModel)]="customer.customerDetail.keyContactPosition"
                                    placeholder="{{'CUSTOMER.keyContactPosition' | translate }}"
                                    [ngClass]="{'is-invalid': !KeyContactPosition.valid && onClickValidation }" />
                                <label for="keyContactPosition">{{"CUSTOMER.keyContactPosition" | translate}}</label>
                                <app-validation-message [field]="KeyContactPosition"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <div class="label-wrap-box mt-2">
                            <span>{{"CUSTOMER.keyContactInfo"| translate}}</span>
                        </div>

                        <!-- Account Name  -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="accountsContact"
                                    #AccountsContact="ngModel" [(ngModel)]="customer.customerDetail.accountsContact"
                                    placeholder="{{'CUSTOMER.accountsContact' | translate}}"
                                    [ngClass]="{'is-invalid': !AccountsContact.valid && onClickValidation }" />
                                <label for="accountsContact">{{"CUSTOMER.accountsContact" | translate}}</label>
                                <app-validation-message [field]="AccountsContact"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Account Phone Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2 mb-14">
                            <app-dial-code-input [(countryCode)]="customer.customerDetail.companyCountryCode"
                                [(number)]="customer.customerDetail.accountsPhone"
                                [onClickValidation]="onClickValidation" fieldName="accountsPhone"
                                nameCode="accountsCountryCode"
                                [labelName]="'CUSTOMER.accountsPhone' | translate"></app-dial-code-input>
                        </div>

                        <!--Account Email -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="accountsEmail" #AccountsEmail="ngModel"
                                    [(ngModel)]="customer.customerDetail.accountsEmail"
                                    placeholder="{{ 'CUSTOMER.accountsEmail' | translate }}" [ngClass]="{
                                        'is-invalid': !AccountsEmail.valid && onClickValidation
                                    }" appEmailValidator [allowEmpty]="true" />
                                <label for="accountsEmail">{{ "CUSTOMER.accountsEmail" | translate
                                    }}</label>
                                <app-validation-message [field]="AccountsEmail" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Account Position -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="accountsPosition"
                                    #AccountsPosition="ngModel" [(ngModel)]="customer.customerDetail.accountsPosition"
                                    placeholder="{{'CUSTOMER.accountsPosition' | translate }}"
                                    [ngClass]="{'is-invalid': !AccountsPosition.valid && onClickValidation }" />
                                <label for="accountsPosition">{{"CUSTOMER.accountsPosition" | translate}}</label>
                                <app-validation-message [field]="AccountsPosition"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button" (click)="onBack()">
                                {{ "COMMON.BACK" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>