.notification-list {
    margin-top: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.sticky-header {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 1;
    padding: 8px 12px;
    font-weight: $font-weight-500;
    border-bottom: 1px solid #eee;
    color: #666;
}

.notification-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.avatar.placeholder {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.content {
    flex: 1;
}

.title {
    font-size: $font-size-16;
    font-weight: $font-weight-500;
    margin: 0;
}

.time {
    font-size: 12px;
    color: #888;
}

.dot {
    width: 8px;
    height: 8px;
    background: #ffc107;
    border-radius: 50%;
    margin-left: 8px;
}

.view-all {
    margin-top: 12px;
    width: 100%;
    background: #f8f9fa;
    border: none;
    padding: 8px 0;
    border-radius: 6px;
    color: #007bff;
    font-weight: $font-weight-500;
    cursor: pointer;
}