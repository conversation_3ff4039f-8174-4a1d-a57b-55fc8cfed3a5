@import '../../variables.scss';

.column-break {
    word-break: break-all;
    white-space: normal;
}

.primary-color {
    color: var(--primary-color);
}

.admin-page-container {
    height: 100vh;

    //-------------Sidebar Css -------------------//
    .sidebar-container {
        -webkit-box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);
        box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);
        background-color: $white-color;
        -webkit-transition: all 0.3s ease;
        transition: all 0.3s ease;
        width: 250px;
        height: 100%;
        max-height: 100%;
        position: fixed;
        top: 0;
        left: -250px;
        z-index: 999;

        .sidebar-brand {
            text-align: center;
            padding: 15px 0 15px 0;
            min-height: 60px;

            @media (min-width: 992px) {
                min-height: unset;
            }

            .logo-light-mode {
                max-width: 180px;
                display: none;

                @media (min-width: 992px) {
                    display: inline-block;
                }
            }
        }

        .sidebar-content {
            overflow-y: auto;
            position: relative;
            height: 100%;

            .sidebar-menu {
                .sidebar-dropdown {
                    .sidebar-link {
                        display: flex;
                        align-items: center;
                        padding: 10px 10px 10px 15px;
                        color: $body-color;
                        font-size: 13px;
                        transition: ease 0.3s ease;
                        font-weight: $font-weight-500;
                        text-transform: capitalize;

                        .svg-icon {
                            width: 34px;
                            height: 34px;
                            display: inline-flex;
                            border: 1px solid #ddd;
                            -webkit-box-align: center;
                            -ms-flex-align: center;
                            align-items: center;
                            justify-content: center;
                            text-align: center;
                            border-radius: 8px;
                            margin-right: 10px;

                            i {
                                font-size: 18px;
                            }
                        }
                    }

                    &.active {
                        .sidebar-link {
                            background-color: var(--primary-color);
                            color: $white-color;
                            font-weight: $font-weight-600;

                            .svg-icon {
                                border: 1px solid #adc1ff;
                            }
                        }
                    }

                    &:hover {
                        .sidebar-link {
                            background-color: var(--primary-color);
                            color: $white-color;

                            .svg-icon {
                                border: 1px solid #adc1ff;
                            }
                        }
                    }
                }
            }
        }

        //---	Responsive  ----//
        @media (max-width: 425px) {
            width: 250px;
        }

        @media (min-width: 1025px) {
            z-index: 1000;
        }

        @media (max-width: 1024px) {
            left: 0;
        }
    }

    //-------------Sidebar Css -------------------//
    //----------- Content-body Css ---------------- //
    .page-content {
        display: inline-block;
        width: 100%;
        min-height: calc(100% - 60px);
        padding-left: 0;
        overflow-x: hidden;
        -webkit-transition: all 0.3s ease;
        transition: all 0.3s ease;
        background-color: $white-color;

        .top-header {
            position: fixed;
            background-color: $white-color;
            right: 0;
            top: 0;
            z-index: 999;
            left: 0;
            -webkit-transition: all 0.3s;
            transition: all 0.3s;
            padding: 15px 10px 10px 10px;

            .header-container {
                .breadcrumb-container {
                    .breadcrumb-bar {
                        .page-title {
                            font-size: 20px;
                            font-weight: $font-weight-600;
                            line-height: 18px;
                        }

                        .breadcrumb-link {
                            .breadcrumb-item {
                                font-size: 12px;

                                .breadcrumb-item-link {
                                    color: $body-color;

                                    &:hover {
                                        color: var(--primary-color) !important;
                                    }
                                }

                                &::before {
                                    padding-right: 6px;
                                }
                            }
                        }
                    }
                }

                .logo-content {
                    display: block;

                    .small-logo {
                        display: block;

                        @media (min-width: 768px) {
                            display: none;
                        }
                    }

                    .big-logo {
                        display: none;

                        @media (min-width: 768px) {
                            display: block;
                        }
                    }

                    @media (min-width: 991px) {
                        display: none;
                    }
                }

                .toggle-menu-icon {
                    cursor: $cursor-pointer;
                    margin-right: 15px;

                    .menu-toggled {
                        width: 30px;
                    }
                }
            }
        }

        .page-layout-container {
            position: relative;
            top: 70px;
        }
    }
}

// --------- Page sidebar Content Trigger -------- //
.admin-page-container.toggled {
    .top-header {
        left: 250px;
    }

    .sidebar-container {
        left: 0;
    }

    @media (max-width: 1024px) {
        .top-header {
            left: 0;
        }

        .sidebar-container {
            left: -250px;
        }
    }

    @media (min-width: 1024px) {
        .page-content {
            padding-left: 250px;
        }
    }
}

// Mobile-view //

// -----------Search & filter Buttons Css -----------------//
.search-container {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 10px;

    .search-input-container {
        position: relative;
        width: 100%;

        .search-icon {
            position: absolute;
            top: 0;
            max-width: 20px;
            display: flex;
            justify-content: center;
            height: 100%;
            right: 16px;
            cursor: $cursor-pointer;
        }
    }
}

// Table-layout css //
.site-table-container {

    .table.dataTable,
    .site-table-layout {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 4px !important;

        .table-head {
            background-color: var(--button-pre-active-color);

            tr {
                th {
                    vertical-align: middle;
                    background-color: transparent;
                    text-align: left;
                    font-size: 12px;
                    letter-spacing: 1.12px;
                    color: $black-color;
                    text-transform: uppercase;
                    font-weight: $font-weight-600;
                    border-bottom: none;
                    min-width: 100px;
                    padding: 0.8rem 30px 0.8rem 0.5rem;
                    white-space: nowrap;

                    &:first-child {
                        border-top-left-radius: 5px;
                        border-bottom-left-radius: 5px;
                        min-width: unset;
                    }

                    &:last-child {
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;
                        min-width: unset;
                    }

                    &.th-profile {
                        width: 20px;
                    }

                    &.dt-orderable-asc,
                    &.dt-orderable-desc,
                    &.dt-ordering-asc,
                    &.dt-ordering-desc {
                        span.dt-column-order {

                            &::before,
                            &::after {
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }

        .table-hover>tbody>tr:hover>* {
            background-color: transparent !important;
            color: inherit !important;
        }

        tbody {
            tr {
                background-color: $white-color;

                .dt-empty {
                    height: 40px;
                    text-align: center !important;
                }

                &:first-child>* {
                    border-top: 1px solid #2e5bff14 !important;
                }

                td {
                    font-size: 13px;
                    vertical-align: middle;
                    text-align: left;
                    font-weight: 400;
                    border-top: 1px solid #2e5bff14 !important;
                    border-bottom: 1px solid #2e5bff14 !important;
                    min-width: 100px;
                    padding: 0.5rem 0.5rem !important;
                    background-color: transparent;

                    a {
                        color: $body-color;
                    }

                    strong {
                        font-weight: $font-weight-600;
                    }

                    .action-icon {
                        .action-link {
                            margin-right: 10px;
                            cursor: $cursor-pointer;

                            img {
                                max-width: 24px;
                                height: 24px;
                            }
                        }
                    }

                    &:first-child {
                        border-left: 1px solid #2e5bff14;
                        border-top-left-radius: 5px;
                        border-bottom-left-radius: 5px;
                    }

                    &:last-child {
                        border-right: 1px solid #2e5bff14;
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;
                        white-space: nowrap;
                    }
                }

                &:hover {
                    color: inherit !important;
                }
            }
        }
    }
}

.table-responsive {

    .dt-layout-end,
    .dt-end {
        .dt-paging {
            white-space: nowrap !important;

            .dt-paging-button {
                margin: 6px 6px 0 0;
                background: $white-color !important;
                color: $black-color !important;
                font-weight: $font-weight-500;
                border-radius: 10px;

                &:hover,
                &:active {
                    color: $white-color !important;
                    background: var(--primary-color) !important;
                    border: 1px solid transparent !important;
                    box-shadow: none !important;
                }

                &.current {
                    background: var(--primary-color) !important;
                    color: $white-color !important;
                    border: none !important;

                    &:hover,
                    &:active {
                        color: $white-color !important;
                        border: none !important;
                        box-shadow: none !important;
                    }
                }

                &.disabled {
                    background: $white-color !important;
                    color: $black-color !important;

                    &:hover,
                    &:active {
                        color: $white-color !important;
                        background: #e2e2e2 !important;
                        border: 1px solid transparent !important;
                        box-shadow: none !important;
                    }
                }
            }
        }
    }
}

.table-hover>tbody>tr:hover>* {
    --bs-table-hover-bg: transparent !important;
    --bs-table-hover-color: inherit !important;
}