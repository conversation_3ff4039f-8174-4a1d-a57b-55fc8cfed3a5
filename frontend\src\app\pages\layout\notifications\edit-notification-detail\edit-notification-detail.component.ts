// 1. Angular Framework Imports
import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';

// 2. Third-Party Library Imports
import { NgbAccordionModule, NgbPopover, NgbPopoverModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import moment from 'moment';
import { catchError, filter, interval, of, startWith, Subscription, switchMap, tap } from 'rxjs';

// 3. Application Core/Shared Imports
import { BaseListServerSideComponent } from '../../../../config/base.list.server.side.component';
import { FilterParam } from '../../../../models/common/filter-param';
import { LoadingService } from '../../../../services/loading.service';
import { CommonService } from '../../../../shared/services/common.service';
import { NotificationService } from '../../../../shared/services/notification.service';
import { ToastService } from '../../../../shared/services/toast.service';
import { TypeAheadService } from '../../../../shared/services/typeahead-search.service';

// 4. Local/Feature-Specific Imports
import { CommonUtil } from '../../../../shared/common.util';
import { RippleEffectDirective } from '../../../../shared/directives/ripple-effect.directive';
import { TooltipEllipsisDirective } from '../../../../shared/directives/tooltip-ellipsis.directive';
import { RateSheetManager } from '../../ratesheet/rate-sheet.manager';
import { NotificationsManager } from '../notifications.manager';

@Component({
    selector: 'app-edit-notification-detail',
    standalone: true,
    imports: [
        CommonModule,
        RippleEffectDirective,
        NgbPopover,
        NgbPopoverModule,
        NgbAccordionModule,
        NgbTooltipModule,
        TooltipEllipsisDirective,
        RouterLink,
        TranslateModule,
    ],
    templateUrl: './edit-notification-detail.component.html',
    styleUrl: './edit-notification-detail.component.scss',
})
export class EditNotificationDetailComponent extends BaseListServerSideComponent implements OnInit {
    notificationCount$ = this.notificationsManager.notificationCount$;
    @ViewChild('popoverRef') popoverRef!: NgbPopover;

    private pollingSubscription?: Subscription;
    private routerSubscription?: Subscription;

    notificationCount: number = 0;
    isNotificationLoading = false;

    notifications: any[] = [];
    todayNotifications: any[] = [];
    groupedOlderNotifications: { label: string; items: any[] }[] = [];;

    groupedNotifications: { label: string; items: any[] }[] = [];

    currentPage = 1;
    pageSize = 20;
    isLoadingMore = false;
    hasMore = true;

    constructor(
        protected rateSheetManager: RateSheetManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
        protected notificationsManager: NotificationsManager,
        protected typeAheadService: TypeAheadService,
        protected notificationService: NotificationService,
    ) {
        super(rateSheetManager, commonService, toastService, loadingService, router);
    }

    ngOnInit(): void {
        this.pollingSubscription = interval(60000)
            .pipe(
                startWith(0),
                switchMap(() => {
                    const countParam = new FilterParam();
                    countParam.filtering.currentDate = CommonUtil.setTodayDate();
                    return this.notificationsManager.fetchNotificationCount(countParam);
                })
            )
            .subscribe();

        if (this.todayNotifications.length) {
            this.groupedNotifications.push({
                label: 'Today',
                items: this.todayNotifications,
            });
        }

        // ✅ 2. Close popover on every route change
        this.routerSubscription = this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe(() => {
                if (this.popoverRef?.isOpen()) {
                    this.popoverRef.close();
                }
            });
    }

    override ngOnDestroy(): void {
        this.pollingSubscription?.unsubscribe();
        this.routerSubscription?.unsubscribe();
    }

    onNotificationPanelOpen() {
        this.currentPage = 1;
        this.notifications = [];
        this.groupedNotifications = [];
        this.hasMore = true;
        this.fetchNotifications();
    }

    openNotificationListing() {
        if (this.popoverRef?.isOpen()) {
            this.popoverRef.close();
        }

        this.router.navigate(['/dashboard/notifications']);
    }

    onScroll(event: any) {
        const target = event.target;

        if (target.scrollTop + target.clientHeight >= target.scrollHeight - 50) {
            this.fetchNotifications();
        }
    }

    redirectToRoute(record: any) {
        if (!record.isSeen) {
            this.viewNotification(record.id);
            this.getNotificationCount$().subscribe();
        }

        const routeMappings: any = {
            CUSTOMER: '/dashboard/customers',
            QUOTATION: '/dashboard/quotations',
            RATESHEET: '/dashboard/rate-sheets',
            SHIPMENT: '/dashboard/shipments',
            BARCODE: '/dashboard/barcodes',
        };

        const route = routeMappings[record.entityName] ?? '/dashboard/notifications';
        this.reDirectBasedOnStatus(route);
    }

    async redirectToUser(record: any) {

        if (!(record.entityIsDeleted === true && this.router.url === '/dashboard/notifications')) {
            if (!record.isRead) {
                await this.markAsRead(record.id);
            }
        }

        if (record.entityIsDeleted === true) {
            if (this.router.url === '/dashboard/notifications') {
                if (this.popoverRef?.isOpen()) {
                    this.popoverRef.close();
                }

                this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
                    this.router.navigate(['/dashboard/notifications']);
                });
            } else {
                this.router.navigate(['/dashboard/notifications']);
            }
            return;
        }

        const routeMappings: any = {
            CUSTOMER: 'dashboard/customer',
            QUOTATION: 'dashboard/quotation',
            RATESHEET: 'dashboard/ratesheet',
            SHIPMENT: 'dashboard/shipment',
            BARCODE: 'dashboard/barcodes'
        };

        const route = routeMappings[record.entityName] ?? '/dashboard/notifications';

        if (this.popoverRef?.isOpen()) {
            this.popoverRef.close();
        }

        this.reDirectBasedOnStatus(route, record?.routeId);
    }

    private fetchNotifications() {
        if (this.isLoadingMore || !this.hasMore) return;

        this.isLoadingMore = true;
        this.isNotificationLoading = this.currentPage === 1;

        const notificationParam = new FilterParam();
        notificationParam.filtering.currentDate = CommonUtil.setTodayDate();
        notificationParam.filtering.isRead = false;
        notificationParam.pagination.offset = this.currentPage;
        notificationParam.pagination.next = this.pageSize;

        this.notificationsManager
            .getNotifications(notificationParam)
            .pipe(
                tap((response) => {
                    const newData = response?.data ?? [];

                    if (newData.length < this.pageSize) {
                        this.hasMore = false; // no more data
                    }

                    const transformed = this.transformNotifications(newData);
                    this.notifications = [...this.notifications, ...transformed];

                    this.groupedNotifications = this.getGroupedNotifications(this.notifications);
                    this.currentPage++;
                }),
                catchError(() => {
                    this.hasMore = false;
                    return of(null);
                }),
                tap(() => {
                    this.isLoadingMore = false;
                    this.isNotificationLoading = false;
                }),
            )
            .subscribe();
    }

    // private methods
    private getNotificationCount$() {
        const countParam = new FilterParam();
        countParam.filtering.currentDate = CommonUtil.setTodayDate();

        return this.notificationsManager.fetchNotificationCount(countParam).pipe(
            tap((response) => {
                this.notificationCount = response.data?.totalCount ?? 0;
            }),
            catchError((error) => {
                // Handle error properly if needed
                console.error('Failed to fetch notification count', error);
                return of(null);
            }),
        );
    }

    private getTimeAgo(dateString: string): string {
        if (!dateString) return 'Invalid date'; // Handle empty/null input

        const today = moment().startOf('day');
        const created = moment(dateString).startOf('day');

        if (!created.isValid()) return 'Invalid date'; // Handle invalid date strings

        const diffDays = today.diff(created, 'days');

        switch (true) {
            case diffDays === 0:
                return 'Today';
            case diffDays === 1:
                return 'Yesterday';
            case diffDays < 0:
                return 'Future date'; // Handle future dates (edge case)
            case diffDays < 7:
                return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
            case diffDays < 30: {
                const diffWeeks = Math.floor(diffDays / 7);
                return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
            }
            default: {
                const diffMonths = today.diff(created, 'months');
                return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
            }
        }
    }

    private getNotificationIcon(entityName: string): string {
        switch (entityName?.toUpperCase()) {
            case 'SHIPMENT':
                return 'bi bi-truck color-red';
            case 'QUOTATION':
                return 'bi bi-file-earmark-text color-blue';
            case 'RATESHEET':
                return 'bi bi-upc color-green';
            case 'CUSTOMER':
                return 'bi bi-person color-yellow';
            default:
                return 'bi bi-bell';
        }
    }

    private markAsRead(itemId: string): Promise<void> {
        return new Promise((resolve, reject) => {
            this.notificationsManager
                .notificationAction([{ id: itemId }])
                .subscribe({
                    next: () => resolve(),
                    error: (err) => reject(new Error(err))
                });
        });
    }

    private reDirectBasedOnStatus(baseRoute: string, id?: string) {
        const targetRoute = id ? `/${baseRoute}/edit/${id}` : `/${baseRoute}s`;

        if (this.router.url === targetRoute) return;

        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate([targetRoute]);
        });
    }

    private transformNotifications(apiData: any[]) {
        const today = moment().startOf('day');

        return apiData.map((item) => {
            const createdDate = moment(item.createdOn).startOf('day');
            const diffDays = today.diff(createdDate, 'days');
            const iconClass = this.getNotificationIcon(item.entityName);

            return {
                title: item.title,
                time: this.getTimeAgo(item.createdOn),
                isNew: !item.isRead,
                message: item.message,
                id: item.id,
                iconClass: iconClass,
                routeId: item.entityPkId,
                entityName: item.entityName,
                status: item.status,
                dateDiff: diffDays,
                entityIsDeleted: item.entityIsDeleted
            };
        });
    }

    private getGroupedNotifications(allItems: any[]) {
        const grouped: { [key: string]: any[] } = {
            Today: [],
        };

        allItems.forEach((item) => {
            if (item.dateDiff === 0) {
                grouped['Today'].push(item);
            } else if (item.dateDiff === 1) {
                grouped['Yesterday'] = grouped['Yesterday'] || [];
                grouped['Yesterday'].push(item);
            } else {
                const label = `${item.dateDiff} days ago`;
                grouped[label] = grouped[label] || [];
                grouped[label].push(item);
            }
        });

        return Object.entries(grouped).map(([label, items]) => ({ label, items }));
    }

    private viewNotification(id: string): void {
        this.notificationsManager.notificationAction(id).subscribe({
            next: () => {
                this.getNotificationCount$().subscribe();
            },
            error: () => {
                this.loadingService.hide();
            },
        });
    }
}
