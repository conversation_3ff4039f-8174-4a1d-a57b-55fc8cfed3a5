import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';
import { Address } from '../common/address';
import { Attachment } from '../common/attachment';
import { CustomerDetail } from './customer-detail';
import { Employees } from './employees';
export class Customer extends BaseModel {
    tenantId!: number;
    slug!: string;
    status!: string;
    registerCompanyName!: string;
    postalAddress!: string;
    addressDetail!: Address;
    employeeDetail!: Employees;
    user!: string;
    alternateEmail!: string;
    accountsPayableEmail!: string;
    website!: string;
    abn!: string;
    fullName!: string;
    companyName!: string;
    email!: string;
    phoneNumber!: string;
    countryCode: string = '+1-CA';
    firstName!: string;
    lastName!: string;
    customerDetail!: CustomerDetail;
    companyPhone!: string;
    keyContact!: string;
    keyContactPosition!: string;
    keyContactPhone!: string;
    keyContactEmail!: string;
    executiveContact!: string;
    executivePosition!: string;
    executivePhone!: string;
    executiveEmail!: string;
    accountsContact!: string;
    accountsPosition!: string;
    accountsPhone!: string;
    accountsEmail!: string;
    operationsContact!: string;
    operationsPosition!: string;
    tradeRefCompanyName!: string;
    tradeRefContact!: string;
    tradeRefPhone!: string;
    tradeRefEmail!: string;
    documents!: Attachment[];
    profileImage!: Attachment[];
    operationsEmail!: string;
    operationsPhone!: string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.documents = new Array<Attachment>();
        this.profileImage = new Array<Attachment>();
        this.customerDetail = new CustomerDetail();
        this.addressDetail = new Address();
        this.addressDetail.type = 'ADDRESS';
    }

    static fromResponse(data: any): Customer {
        const customer = { ...data };
        return customer;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.registerCompanyName = this.trimMe(this.registerCompanyName);
        this.status = this.trimMe(this.status);
        this.postalAddress = this.trimMe(this.postalAddress);
        this.alternateEmail = this.trimMe(this.alternateEmail);
        this.accountsPayableEmail = this.trimMe(this.accountsPayableEmail);
        this.website = this.trimMe(this.website);
        this.abn = this.trimMe(this.abn);
        this.companyName = this.trimMe(this.companyName);
        this.companyPhone = this.trimMe(this.companyPhone);
        this.keyContact = this.trimMe(this.keyContact);
        this.keyContactPosition = this.trimMe(this.keyContactPosition);
        this.keyContactPhone = this.trimMe(this.keyContactPhone);
        this.keyContactEmail = this.trimMe(this.keyContactEmail);
        this.executiveContact = this.trimMe(this.executiveContact);
        this.executivePosition = this.trimMe(this.executivePosition);
        this.executivePhone = this.trimMe(this.executivePhone);
        this.executiveEmail = this.trimMe(this.executiveEmail);
        this.accountsContact = this.trimMe(this.accountsContact);
        this.accountsPosition = this.trimMe(this.accountsPosition);
        this.accountsPhone = this.trimMe(this.accountsPhone);
        this.accountsEmail = this.trimMe(this.accountsEmail);
        this.operationsContact = this.trimMe(this.operationsContact);
        this.operationsPosition = this.trimMe(this.operationsPosition);
        this.tradeRefCompanyName = this.trimMe(this.tradeRefCompanyName);
        this.tradeRefContact = this.trimMe(this.tradeRefContact);
        this.tradeRefPhone = this.trimMe(this.tradeRefPhone);
        this.tradeRefEmail = this.trimMe(this.tradeRefEmail);
        this.operationsEmail = this.trimMe(this.operationsEmail);
        this.operationsPhone = this.trimMe(this.operationsPhone);
        return this;
    }
}
