// Angular Core & Common Modules
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-Party Libraries
import { TranslateModule } from '@ngx-translate/core';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Shipment } from '../../../../../models/shipment/shipment';

// Services
import { RateSheet } from '../../../../../models/rate-sheet';
import { LoadingService } from '../../../../../services/loading.service';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';
import { CurrencyFormatterDirective } from '../../../../../shared/directives/custom-currency.directive';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { ShipmentManager } from '../../shipment.manager';

@Component({
    selector: 'app-shipment-special-request',
    standalone: true,
    imports: [
        TranslateModule,
        CommonModule,
        FormsModule,
        RouterModule,
        CurrencyFormatterDirective,
        ValidationMessageComponent,
    ],
    templateUrl: './shipment-special-request.component.html',
    styleUrl: './shipment-special-request.component.scss',
})
export class ShipmentSpecialRequestComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() rateSheetId!: string | null;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    specialRequestCharges!: RateSheet;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected cdRef: ChangeDetectorRef,
        protected shipmentManager: ShipmentManager,
    ) { }

    ngOnInit() {
        this.specialRequestCharges = new RateSheet();
        this.fetchSpecialPrice();
        this.cdRef.detectChanges();
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.onNextClick.emit(6);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.saveButtonClicked.emit();
    }

    onBack() {
        this.onNextOrBackClick.emit(4);
    }

    onRadioButtonChange(
        data: any,
        field:
            | 'oversizeRate'
            | 'rushRequestRate'
            | 'enclosedRate'
            | 'fragileRate'
            | 'perishableRate'
            | 'dangerousGoodsRate',
    ) {
        if (!data) {
            this.shipment[field] = null;
        } else {
            this.shipment[field] = this.specialRequestCharges[field];
        }
    }

    fetchSpecialPrice() {
        this.filterParam.filtering.shipmentId = this.route.snapshot.paramMap.get('id') as string;

        this.shipmentManager.fetchSpecialPrice(this.filterParam).subscribe({
            next: (specialRequestCharges: any) => {
                if (specialRequestCharges) {
                    this.specialRequestCharges = specialRequestCharges;
                }
            },
        });
    }

    updateSwitchLabel(): void {
        const label = document.getElementById('switchStatus');
        const toggle = document.getElementById('toggleSwitch') as HTMLInputElement | null;

        if (label && toggle) {
            label.textContent = toggle.checked ? 'Yes' : 'No';
        }
    }
}
