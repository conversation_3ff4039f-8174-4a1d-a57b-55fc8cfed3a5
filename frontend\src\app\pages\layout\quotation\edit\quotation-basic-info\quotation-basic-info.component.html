<div class="site-page-container mt-3">
    <div class="site-card">
        <form #quotationForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <!-- start of section for the basic information -->
                        <ng-container title="Basic Information">
                            <div class="label-wrap-box">
                                <span>{{"COMMON.BasicDetails" | translate}}</span>
                            </div>

                            <!-- Reference ID -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="text" name="referenceId"
                                        placeholder="Reference/Load ID" #ReferenceId="ngModel"
                                        [(ngModel)]="quotation.refID" disabled>
                                    <label for="referenceId">{{"BARCODE.refID" | translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Name -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="text" name="contactName" placeholder=""
                                        #contactPersonName="ngModel" [(ngModel)]="quotation.contactPersonName">
                                    <label for="contactName">{{"DASHBOARD.Contact" | translate}} {{"USERS.Name" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Email -->
                            <div class="col-md-6  px-0 ps-md-0 pe-md-2">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="email" name="contactEmail" #contactEmail="ngModel"
                                        [(ngModel)]="quotation.contactPersonEmail" placeholder="">
                                    <label for="contactEmail">{{"DASHBOARD.Contact" | translate}} {{"EMAIL" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Phone -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14">
                                    <app-dial-code-input [(countryCode)]="quotation.contactPersonCountryCode"
                                        [(number)]="quotation.contactPersonPhone" nameCode="CountryCode"
                                        [onClickValidation]="onClickValidation" fieldName="phoneNumber"
                                        [labelName]="'Quotation.contactPersonPhone' | translate"></app-dial-code-input>
                                </div>
                            </div>

                            <!-- Customer -->
                            <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="fullName" bindValue="id" [items]="customers"
                                        [(ngModel)]="quotation.customer" #Customer="ngModel" name="customer"
                                        [required]="!quotation.isQuickQuote"
                                        [ngClass]="{'is-invalid': !Customer.valid && onClickValidation}"
                                        [clearable]="false" [typeahead]="searchCustomerSubject"
                                        [loading]="loadingCustomerNgSelect">
                                    </ng-select>
                                    <app-validation-message [field]="Customer" [onClickValidation]="onClickValidation">
                                    </app-validation-message>

                                    <label for="Customer" class="ng-select-label">{{"CUSTOMER.objName" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- RateSheet -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindValue="id" bindLabel="name" [items]="rateSheets"
                                        [(ngModel)]="quotation.rateSheet" #RateSheet="ngModel" name="rateSheet"
                                        [ngClass]="{'is-invalid': !RateSheet.valid && onClickValidation}"
                                        [clearable]="false" [typeahead]="searchRateSheetSubject"
                                        [loading]="loadingRateSheetNgSelect" (change)="onRateSheetChange($event)">
                                    </ng-select>
                                    <app-validation-message [field]="RateSheet" [onClickValidation]="onClickValidation">
                                    </app-validation-message>

                                    <label for="RateSheet" class="ng-select-label">{{"RATE_SHEET.objName#" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindValue="id" bindLabel="name" [items]="quotationStatus"
                                        [(ngModel)]="quotation.status" #QuotationStatus="ngModel" name="quotationStatus"
                                        required="required"
                                        [ngClass]="{'is-invalid': !QuotationStatus.valid && onClickValidation}"
                                        [clearable]="false">
                                    </ng-select>

                                    <app-validation-message [field]="QuotationStatus"
                                        [onClickValidation]="onClickValidation">
                                    </app-validation-message>

                                    <label for="QuotationStatus" class="ng-select-label">{{"COMMON.STATUS" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Company Name -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14" *ngIf="quotation.isQuickQuote">
                                    <input class="form-control" type="text" name="companyName" placeholder=""
                                        #CompanyName="ngModel" [(ngModel)]="quotation.companyName">
                                    <app-validation-message [field]="CompanyName"
                                        [onClickValidation]="onClickValidation">
                                    </app-validation-message>
                                    <label for="companyName">{{"CUSTOMER.companyName" | translate}} </label>
                                </div>
                            </div>

                            <!-- summary -->
                            <div class="col-md-12 mb-14 px-0 p-md-0">
                                <div class="form-floating textarea-custom">
                                    <textarea class="form-control" placeholder="{{'shipmentSummary' | translate}}"
                                        rows="4" id="shipmentSummary" name="Description" #QuotationSummary="ngModel"
                                        [(ngModel)]="quotation.summary"></textarea>
                                    <label for="description pb-2">{{"Quotation.summary"| translate}}</label>
                                </div>
                            </div>
                        </ng-container>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(quotationForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(quotationForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>