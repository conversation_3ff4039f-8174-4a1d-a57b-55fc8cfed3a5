import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class VehicleService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/vehicle', '/api/vehicles');
    }

    fetchDriverLocationsDetails(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/DriverLocations', filterParam);
    }
}
