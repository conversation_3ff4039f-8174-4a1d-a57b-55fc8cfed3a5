import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class LoadingService {
    private renderer: Renderer2;
    private loadingElement: HTMLElement | null;

    constructor(private rendererFactory: RendererFactory2) {
        this.renderer = this.rendererFactory.createRenderer(null, null);
        this.loadingElement = document.getElementById('loading');
    }

    show() {
        if (this.loadingElement) {
            this.renderer.setStyle(this.loadingElement, 'display', 'flex');
        } else {
            console.error('Loading element not found');
        }

        this.toggleBodyOverflow(true);
    }

    hide() {
        if (this.loadingElement) {
            this.renderer.setStyle(this.loadingElement, 'display', 'none');
        } else {
            console.error('Loading element not found');
        }

        this.toggleBodyOverflow(false);
    }

    // Method to toggle body overflow based on loading state
    private toggleBodyOverflow(isHidden: boolean) {
        const body = document.body;
        const overflowValue = isHidden ? 'hidden' : 'auto';
        this.renderer.setStyle(body, 'overflow', overflowValue);
    }
}
