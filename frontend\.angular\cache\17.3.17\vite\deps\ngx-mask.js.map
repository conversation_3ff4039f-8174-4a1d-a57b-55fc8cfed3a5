{"version": 3, "sources": ["../../../../../node_modules/ngx-mask/fesm2022/ngx-mask.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, inject, Injectable, ElementRef, Renderer2, makeEnvironmentProviders, Directive, Input, Output, HostListener, Pipe } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n  suffix: '',\n  prefix: '',\n  thousandSeparator: ' ',\n  decimalMarker: ['.', ','],\n  clearIfNotMatch: false,\n  showTemplate: false,\n  showMaskTyped: false,\n  placeHolderCharacter: '_',\n  dropSpecialCharacters: true,\n  hiddenInput: undefined,\n  shownMaskExpression: '',\n  separatorLimit: '',\n  allowNegativeNumbers: false,\n  validation: true,\n  specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n  leadZeroDateTime: false,\n  apm: false,\n  leadZero: false,\n  keepCharacterPositions: false,\n  triggerOnMaskChange: false,\n  inputTransformFn: value => value,\n  outputTransformFn: value => value,\n  maskFilled: new EventEmitter(),\n  patterns: {\n    '0': {\n      pattern: new RegExp('\\\\d')\n    },\n    '9': {\n      pattern: new RegExp('\\\\d'),\n      optional: true\n    },\n    X: {\n      pattern: new RegExp('\\\\d'),\n      symbol: '*'\n    },\n    A: {\n      pattern: new RegExp('[a-zA-Z0-9]')\n    },\n    S: {\n      pattern: new RegExp('[a-zA-Z]')\n    },\n    U: {\n      pattern: new RegExp('[A-Z]')\n    },\n    L: {\n      pattern: new RegExp('[a-z]')\n    },\n    d: {\n      pattern: new RegExp('\\\\d')\n    },\n    m: {\n      pattern: new RegExp('\\\\d')\n    },\n    M: {\n      pattern: new RegExp('\\\\d')\n    },\n    H: {\n      pattern: new RegExp('\\\\d')\n    },\n    h: {\n      pattern: new RegExp('\\\\d')\n    },\n    s: {\n      pattern: new RegExp('\\\\d')\n    }\n  }\n};\nconst timeMasks = [\"Hh:m0:s0\" /* MaskExpression.HOURS_MINUTES_SECONDS */, \"Hh:m0\" /* MaskExpression.HOURS_MINUTES */, \"m0:s0\" /* MaskExpression.MINUTES_SECONDS */];\nconst withoutValidation = [\"percent\" /* MaskExpression.PERCENT */, \"Hh\" /* MaskExpression.HOURS_HOUR */, \"s0\" /* MaskExpression.SECONDS */, \"m0\" /* MaskExpression.MINUTES */, \"separator\" /* MaskExpression.SEPARATOR */, \"d0/M0/0000\" /* MaskExpression.DAYS_MONTHS_YEARS */, \"d0/M0\" /* MaskExpression.DAYS_MONTHS */, \"d0\" /* MaskExpression.DAYS */, \"M0\" /* MaskExpression.MONTHS */];\nclass NgxMaskApplierService {\n  constructor() {\n    this._config = inject(NGX_MASK_CONFIG);\n    this.dropSpecialCharacters = this._config.dropSpecialCharacters;\n    this.hiddenInput = this._config.hiddenInput;\n    this.clearIfNotMatch = this._config.clearIfNotMatch;\n    this.specialCharacters = this._config.specialCharacters;\n    this.patterns = this._config.patterns;\n    this.prefix = this._config.prefix;\n    this.suffix = this._config.suffix;\n    this.thousandSeparator = this._config.thousandSeparator;\n    this.decimalMarker = this._config.decimalMarker;\n    this.showMaskTyped = this._config.showMaskTyped;\n    this.placeHolderCharacter = this._config.placeHolderCharacter;\n    this.validation = this._config.validation;\n    this.separatorLimit = this._config.separatorLimit;\n    this.allowNegativeNumbers = this._config.allowNegativeNumbers;\n    this.leadZeroDateTime = this._config.leadZeroDateTime;\n    this.leadZero = this._config.leadZero;\n    this.apm = this._config.apm;\n    this.inputTransformFn = this._config.inputTransformFn;\n    this.outputTransformFn = this._config.outputTransformFn;\n    this.keepCharacterPositions = this._config.keepCharacterPositions;\n    this._shift = new Set();\n    this.plusOnePosition = false;\n    this.maskExpression = '';\n    this.actualValue = '';\n    this.showKeepCharacterExp = '';\n    this.shownMaskExpression = '';\n    this.deletedSpecialCharacter = false;\n    this._formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n      let x = [];\n      let decimalChar = '';\n      if (Array.isArray(decimalChars)) {\n        const regExp = new RegExp(decimalChars.map(v => '[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v).join('|'));\n        x = str.split(regExp);\n        decimalChar = str.match(regExp)?.[0] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n      } else {\n        x = str.split(decimalChars);\n        decimalChar = decimalChars;\n      }\n      const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : \"\" /* MaskExpression.EMPTY_STRING */;\n      let res = x[0] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n      const separatorLimit = this.separatorLimit.replace(/\\s/g, \"\" /* MaskExpression.EMPTY_STRING */);\n      if (separatorLimit && +separatorLimit) {\n        if (res[0] === \"-\" /* MaskExpression.MINUS */) {\n          res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n        } else {\n          res = res.slice(0, separatorLimit.length);\n        }\n      }\n      const rgx = /(\\d+)(\\d{3})/;\n      while (thousandSeparatorChar && rgx.test(res)) {\n        res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n      }\n      if (precision === undefined) {\n        return res + decimals;\n      } else if (precision === 0) {\n        return res;\n      }\n      return res + decimals.substring(0, precision + 1);\n    };\n    this.percentage = str => {\n      const sanitizedStr = str.replace(',', '.');\n      const value = Number(this.allowNegativeNumbers && str.includes(\"-\" /* MaskExpression.MINUS */) ? sanitizedStr.slice(1, str.length) : sanitizedStr);\n      return !isNaN(value) && value >= 0 && value <= 100;\n    };\n    this.getPrecision = maskExpression => {\n      const x = maskExpression.split(\".\" /* MaskExpression.DOT */);\n      if (x.length > 1) {\n        return Number(x[x.length - 1]);\n      }\n      return Infinity;\n    };\n    this.checkAndRemoveSuffix = inputValue => {\n      for (let i = this.suffix?.length - 1; i >= 0; i--) {\n        const substr = this.suffix.substring(i, this.suffix?.length);\n        if (inputValue.includes(substr) && i !== this.suffix?.length - 1 && (i - 1 < 0 || !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n          return inputValue.replace(substr, \"\" /* MaskExpression.EMPTY_STRING */);\n        }\n      }\n      return inputValue;\n    };\n    this.checkInputPrecision = (inputValue, precision, decimalMarker) => {\n      if (precision < Infinity) {\n        // TODO need think about decimalMarker\n        if (Array.isArray(decimalMarker)) {\n          const marker = decimalMarker.find(dm => dm !== this.thousandSeparator);\n          decimalMarker = marker ? marker : decimalMarker[0];\n        }\n        const precisionRegEx = new RegExp(this._charToRegExpExpression(decimalMarker) + `\\\\d{${precision}}.*$`);\n        const precisionMatch = inputValue.match(precisionRegEx);\n        const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n        if (precisionMatchLength - 1 > precision) {\n          const diff = precisionMatchLength - 1 - precision;\n          inputValue = inputValue.substring(0, inputValue.length - diff);\n        }\n        if (precision === 0 && this._compareOrIncludes(inputValue[inputValue.length - 1], decimalMarker, this.thousandSeparator)) {\n          inputValue = inputValue.substring(0, inputValue.length - 1);\n        }\n      }\n      return inputValue;\n    };\n  }\n  applyMaskWithPattern(inputValue, maskAndPattern) {\n    const [mask, customPattern] = maskAndPattern;\n    this.customPattern = customPattern;\n    return this.applyMask(inputValue, mask);\n  }\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    if (!maskExpression || typeof inputValue !== 'string') {\n      return \"\" /* MaskExpression.EMPTY_STRING */;\n    }\n    let cursor = 0;\n    let result = '';\n    let multi = false;\n    let backspaceShift = false;\n    let shift = 1;\n    let stepBack = false;\n    if (inputValue.slice(0, this.prefix.length) === this.prefix) {\n      inputValue = inputValue.slice(this.prefix.length, inputValue.length);\n    }\n    if (!!this.suffix && inputValue?.length > 0) {\n      inputValue = this.checkAndRemoveSuffix(inputValue);\n    }\n    if (inputValue === '(' && this.prefix) {\n      inputValue = '';\n    }\n    const inputArray = inputValue.toString().split(\"\" /* MaskExpression.EMPTY_STRING */);\n    if (this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\" /* MaskExpression.MINUS */) {\n      result += inputValue.slice(cursor, cursor + 1);\n    }\n    if (maskExpression === \"IP\" /* MaskExpression.IP */) {\n      const valuesIP = inputValue.split(\".\" /* MaskExpression.DOT */);\n      this.ipError = this._validIP(valuesIP);\n      maskExpression = '***************';\n    }\n    const arr = [];\n    for (let i = 0; i < inputValue.length; i++) {\n      if (inputValue[i]?.match('\\\\d')) {\n        arr.push(inputValue[i] ?? \"\" /* MaskExpression.EMPTY_STRING */);\n      }\n    }\n    if (maskExpression === \"CPF_CNPJ\" /* MaskExpression.CPF_CNPJ */) {\n      this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n      if (arr.length > 11) {\n        maskExpression = '00.000.000/0000-00';\n      } else {\n        maskExpression = '000.000.000-00';\n      }\n    }\n    if (maskExpression.startsWith(\"percent\" /* MaskExpression.PERCENT */)) {\n      if (inputValue.match('[a-z]|[A-Z]') ||\n      // eslint-disable-next-line no-useless-escape\n      inputValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced) {\n        inputValue = this._stripToDecimal(inputValue);\n        const precision = this.getPrecision(maskExpression);\n        inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n      }\n      const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\" /* MaskExpression.DOT */;\n      if (inputValue.indexOf(decimalMarker) > 0 && !this.percentage(inputValue.substring(0, inputValue.indexOf(decimalMarker)))) {\n        let base = inputValue.substring(0, inputValue.indexOf(decimalMarker) - 1);\n        if (this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\" /* MaskExpression.MINUS */ && !backspaced) {\n          base = inputValue.substring(0, inputValue.indexOf(decimalMarker));\n        }\n        inputValue = `${base}${inputValue.substring(inputValue.indexOf(decimalMarker), inputValue.length)}`;\n      }\n      let value = '';\n      this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\" /* MaskExpression.MINUS */ ? value = `${\"-\" /* MaskExpression.MINUS */}${inputValue.slice(cursor + 1, cursor + inputValue.length)}` : value = inputValue;\n      if (this.percentage(value)) {\n        result = this._splitPercentZero(inputValue);\n      } else {\n        result = this._splitPercentZero(inputValue.substring(0, inputValue.length - 1));\n      }\n    } else if (maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */)) {\n      if (inputValue.match('[wа-яА-Я]') || inputValue.match('[ЁёА-я]') || inputValue.match('[a-z]|[A-Z]') || inputValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) || inputValue.match('[^A-Za-z0-9,]')) {\n        inputValue = this._stripToDecimal(inputValue);\n      }\n      const precision = this.getPrecision(maskExpression);\n      const decimalMarker = Array.isArray(this.decimalMarker) ? \".\" /* MaskExpression.DOT */ : this.decimalMarker;\n      if (precision === 0) {\n        inputValue = this.allowNegativeNumbers ? inputValue.length > 2 && inputValue[0] === \"-\" /* MaskExpression.MINUS */ && inputValue[1] === \"0\" /* MaskExpression.NUMBER_ZERO */ && inputValue[2] !== this.thousandSeparator && inputValue[2] !== \",\" /* MaskExpression.COMMA */ && inputValue[2] !== \".\" /* MaskExpression.DOT */ ? '-' + inputValue.slice(2, inputValue.length) : inputValue[0] === \"0\" /* MaskExpression.NUMBER_ZERO */ && inputValue.length > 1 && inputValue[1] !== this.thousandSeparator && inputValue[1] !== \",\" /* MaskExpression.COMMA */ && inputValue[1] !== \".\" /* MaskExpression.DOT */ ? inputValue.slice(1, inputValue.length) : inputValue : inputValue.length > 1 && inputValue[0] === \"0\" /* MaskExpression.NUMBER_ZERO */ && inputValue[1] !== this.thousandSeparator && inputValue[1] !== \",\" /* MaskExpression.COMMA */ && inputValue[1] !== \".\" /* MaskExpression.DOT */ ? inputValue.slice(1, inputValue.length) : inputValue;\n      } else {\n        if (inputValue[0] === decimalMarker && inputValue.length > 1) {\n          inputValue = \"0\" /* MaskExpression.NUMBER_ZERO */ + inputValue.slice(0, inputValue.length + 1);\n          this.plusOnePosition = true;\n        }\n        if (inputValue[0] === \"0\" /* MaskExpression.NUMBER_ZERO */ && inputValue[1] !== decimalMarker && inputValue[1] !== this.thousandSeparator) {\n          inputValue = inputValue.length > 1 ? inputValue.slice(0, 1) + decimalMarker + inputValue.slice(1, inputValue.length + 1) : inputValue;\n          this.plusOnePosition = true;\n        }\n        if (this.allowNegativeNumbers && inputValue[0] === \"-\" /* MaskExpression.MINUS */ && (inputValue[1] === decimalMarker || inputValue[1] === \"0\" /* MaskExpression.NUMBER_ZERO */)) {\n          inputValue = inputValue[1] === decimalMarker && inputValue.length > 2 ? inputValue.slice(0, 1) + \"0\" /* MaskExpression.NUMBER_ZERO */ + inputValue.slice(1, inputValue.length) : inputValue[1] === \"0\" /* MaskExpression.NUMBER_ZERO */ && inputValue.length > 2 && inputValue[2] !== decimalMarker ? inputValue.slice(0, 2) + decimalMarker + inputValue.slice(2, inputValue.length) : inputValue;\n          this.plusOnePosition = true;\n        }\n      }\n      if (backspaced) {\n        const inputValueAfterZero = inputValue.slice(this._findFirstNonZeroDigitIndex(inputValue), inputValue.length);\n        const positionOfZeroOrDecimalMarker = inputValue[position] === \"0\" /* MaskExpression.NUMBER_ZERO */ || inputValue[position] === decimalMarker;\n        const zeroIndexNumberZero = inputValue[0] === \"0\" /* MaskExpression.NUMBER_ZERO */;\n        const zeroIndexMinus = inputValue[0] === \"-\" /* MaskExpression.MINUS */;\n        const zeroIndexThousand = inputValue[0] === this.thousandSeparator;\n        const firstIndexDecimalMarker = inputValue[1] === decimalMarker;\n        const firstIndexNumberZero = inputValue[1] === \"0\" /* MaskExpression.NUMBER_ZERO */;\n        const secondIndexDecimalMarker = inputValue[2] === decimalMarker;\n        if (zeroIndexNumberZero && firstIndexDecimalMarker && positionOfZeroOrDecimalMarker && position < 2) {\n          inputValue = inputValueAfterZero;\n        }\n        if (zeroIndexMinus && firstIndexNumberZero && secondIndexDecimalMarker && positionOfZeroOrDecimalMarker && position < 3) {\n          inputValue = \"-\" /* MaskExpression.MINUS */ + inputValueAfterZero;\n        }\n        if (inputValueAfterZero !== \"-\" /* MaskExpression.MINUS */ && (position === 0 && (zeroIndexNumberZero || zeroIndexThousand) || this.allowNegativeNumbers && position === 1 && zeroIndexMinus && !firstIndexNumberZero)) {\n          inputValue = zeroIndexMinus ? \"-\" /* MaskExpression.MINUS */ + inputValueAfterZero : inputValueAfterZero;\n        }\n      }\n      // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n      //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n      const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n      let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, '');\n      //.replace(decimalMarkerEscaped, '');\n      if (Array.isArray(this.decimalMarker)) {\n        for (const marker of this.decimalMarker) {\n          invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), \"\" /* MaskExpression.EMPTY_STRING */);\n        }\n      } else {\n        invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n      }\n      const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n      if (inputValue.match(invalidCharRegexp)) {\n        inputValue = inputValue.substring(0, inputValue.length - 1);\n      }\n      inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n      const strForSep = inputValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n      result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n      const commaShift = result.indexOf(\",\" /* MaskExpression.COMMA */) - inputValue.indexOf(\",\" /* MaskExpression.COMMA */);\n      const shiftStep = result.length - inputValue.length;\n      if (result[position - 1] === this.thousandSeparator && this.prefix && backspaced) {\n        position = position - 1;\n      } else if (shiftStep > 0 && result[position] !== this.thousandSeparator) {\n        backspaceShift = true;\n        let _shift = 0;\n        do {\n          this._shift.add(position + _shift);\n          _shift++;\n        } while (_shift < shiftStep);\n      } else if (result[position - 1] === this.decimalMarker || shiftStep === -4 || shiftStep === -3 || result[position] === this.thousandSeparator) {\n        this._shift.clear();\n        this._shift.add(position - 1);\n      } else if (commaShift !== 0 && position > 0 && !(result.indexOf(\",\" /* MaskExpression.COMMA */) >= position && position > 3) || !(result.indexOf(\".\" /* MaskExpression.DOT */) >= position && position > 3) && shiftStep <= 0) {\n        this._shift.clear();\n        backspaceShift = true;\n        shift = shiftStep;\n        position += shiftStep;\n        this._shift.add(position);\n      } else {\n        this._shift.clear();\n      }\n    } else {\n      for (let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? \"\" /* MaskExpression.EMPTY_STRING */) {\n        if (cursor === maskExpression.length) {\n          break;\n        }\n        const symbolStarInPattern = \"*\" /* MaskExpression.SYMBOL_STAR */ in this.patterns;\n        if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */) && maskExpression[cursor + 1] === \"?\" /* MaskExpression.SYMBOL_QUESTION */) {\n          result += inputSymbol;\n          cursor += 2;\n        } else if (maskExpression[cursor + 1] === \"*\" /* MaskExpression.SYMBOL_STAR */ && multi && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\" /* MaskExpression.EMPTY_STRING */)) {\n          result += inputSymbol;\n          cursor += 3;\n          multi = false;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */) && maskExpression[cursor + 1] === \"*\" /* MaskExpression.SYMBOL_STAR */ && !symbolStarInPattern) {\n          result += inputSymbol;\n          multi = true;\n        } else if (maskExpression[cursor + 1] === \"?\" /* MaskExpression.SYMBOL_QUESTION */ && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\" /* MaskExpression.EMPTY_STRING */)) {\n          result += inputSymbol;\n          cursor += 3;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */)) {\n          if (maskExpression[cursor] === \"H\" /* MaskExpression.HOURS */) {\n            if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === \"h\" /* MaskExpression.HOUR */) {\n            if (this.apm ? result.length === 1 && Number(result) > 1 || result === '1' && Number(inputSymbol) > 2 || inputValue.slice(cursor - 1, cursor).length === 1 && Number(inputValue.slice(cursor - 1, cursor)) > 2 || inputValue.slice(cursor - 1, cursor) === '1' && Number(inputSymbol) > 2 : result === '2' && Number(inputSymbol) > 3 || (result.slice(cursor - 2, cursor) === '2' || result.slice(cursor - 3, cursor) === '2' || result.slice(cursor - 4, cursor) === '2' || result.slice(cursor - 1, cursor) === '2') && Number(inputSymbol) > 3 && cursor > 10) {\n              position = position + 1;\n              cursor += 1;\n              i--;\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === \"m\" /* MaskExpression.MINUTE */ || maskExpression[cursor] === \"s\" /* MaskExpression.SECOND */) {\n            if (Number(inputSymbol) > 5) {\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          const daysCount = 31;\n          const inputValueCursor = inputValue[cursor];\n          const inputValueCursorPlusOne = inputValue[cursor + 1];\n          const inputValueCursorPlusTwo = inputValue[cursor + 2];\n          const inputValueCursorMinusOne = inputValue[cursor - 1];\n          const inputValueCursorMinusTwo = inputValue[cursor - 2];\n          const inputValueSliceMinusThreeMinusOne = inputValue.slice(cursor - 3, cursor - 1);\n          const inputValueSliceMinusOnePlusOne = inputValue.slice(cursor - 1, cursor + 1);\n          const inputValueSliceCursorPlusTwo = inputValue.slice(cursor, cursor + 2);\n          const inputValueSliceMinusTwoCursor = inputValue.slice(cursor - 2, cursor);\n          if (maskExpression[cursor] === \"d\" /* MaskExpression.DAY */) {\n            const maskStartWithMonth = maskExpression.slice(0, 2) === \"M0\" /* MaskExpression.MONTHS */;\n            const startWithMonthInput = maskExpression.slice(0, 2) === \"M0\" /* MaskExpression.MONTHS */ && this.specialCharacters.includes(inputValueCursorMinusTwo);\n            if (Number(inputSymbol) > 3 && this.leadZeroDateTime || !maskStartWithMonth && (Number(inputValueSliceCursorPlusTwo) > daysCount || Number(inputValueSliceMinusOnePlusOne) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne)) || (startWithMonthInput ? Number(inputValueSliceMinusOnePlusOne) > daysCount || !this.specialCharacters.includes(inputValueCursor) && this.specialCharacters.includes(inputValueCursorPlusTwo) || this.specialCharacters.includes(inputValueCursor) : Number(inputValueSliceCursorPlusTwo) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne))) {\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === \"M\" /* MaskExpression.MONTH */) {\n            const monthsCount = 12;\n            // mask without day\n            const withoutDays = cursor === 0 && (Number(inputSymbol) > 2 || Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne) && !backspaced);\n            // day<10 && month<12 for input\n            const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n            const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) && maskExpression.includes('d0') && (this.specialCharacters.includes(inputValueCursorMinusTwo) && Number(inputValueSliceMinusOnePlusOne) > monthsCount && !this.specialCharacters.includes(inputValueCursor) || this.specialCharacters.includes(inputValueCursor));\n            //  month<12 && day<10 for input\n            const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && this.specialCharacters.includes(inputValueCursorMinusOne) && (Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne));\n            // cursor === 5 && without days\n            const day2monthInputDot = Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5 || this.specialCharacters.includes(inputValueCursorPlusOne) && cursor === 5;\n            // // day<10 && month<12 for paste whole data\n            const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) && Number(inputValueSliceMinusTwoCursor) > monthsCount && maskExpression.includes('d0');\n            // 10<day<31 && month<12 for paste whole data\n            const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueCursorMinusOne) && Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n            if (Number(inputSymbol) > 1 && this.leadZeroDateTime || withoutDays || day1monthInput || day2monthPaste || day1monthPaste || day2monthInput || day2monthInputDot && !this.leadZeroDateTime) {\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.includes(inputSymbol) && maskExpression[cursor] === inputSymbol) {\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */) !== -1) {\n          result += maskExpression[cursor];\n          cursor++;\n          this._shiftStep(maskExpression, cursor, inputArray.length);\n          i--;\n        } else if (maskExpression[cursor] === \"9\" /* MaskExpression.NUMBER_NINE */ && this.showMaskTyped) {\n          this._shiftStep(maskExpression, cursor, inputArray.length);\n        } else if (this.patterns[maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */] && this.patterns[maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */]?.optional) {\n          if (!!inputArray[cursor] && maskExpression !== '***************' && maskExpression !== '000.000.000-00' && maskExpression !== '00.000.000/0000-00' && !maskExpression.match(/^9+\\.0+$/) && !this.patterns[maskExpression[cursor] ?? \"\" /* MaskExpression.EMPTY_STRING */]?.optional) {\n            result += inputArray[cursor];\n          }\n          if (maskExpression.includes(\"9\" /* MaskExpression.NUMBER_NINE */ + \"*\" /* MaskExpression.SYMBOL_STAR */) && maskExpression.includes(\"0\" /* MaskExpression.NUMBER_ZERO */ + \"*\" /* MaskExpression.SYMBOL_STAR */)) {\n            cursor++;\n          }\n          cursor++;\n          i--;\n        } else if (this.maskExpression[cursor + 1] === \"*\" /* MaskExpression.SYMBOL_STAR */ && this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\" /* MaskExpression.EMPTY_STRING */) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.maskExpression[cursor + 1] === \"?\" /* MaskExpression.SYMBOL_QUESTION */ && this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\" /* MaskExpression.EMPTY_STRING */) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.showMaskTyped && this.specialCharacters.indexOf(inputSymbol) < 0 && inputSymbol !== this.placeHolderCharacter && this.placeHolderCharacter.length === 1) {\n          stepBack = true;\n        }\n      }\n    }\n    if (result.length + 1 === maskExpression.length && this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? \"\" /* MaskExpression.EMPTY_STRING */) !== -1) {\n      result += maskExpression[maskExpression.length - 1];\n    }\n    let newPosition = position + 1;\n    while (this._shift.has(newPosition)) {\n      shift++;\n      newPosition++;\n    }\n    let actualShift = justPasted && !maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) ? cursor : this._shift.has(position) ? shift : 0;\n    if (stepBack) {\n      actualShift--;\n    }\n    cb(actualShift, backspaceShift);\n    if (shift < 0) {\n      this._shift.clear();\n    }\n    let onlySpecial = false;\n    if (backspaced) {\n      onlySpecial = inputArray.every(char => this.specialCharacters.includes(char));\n    }\n    let res = `${this.prefix}${onlySpecial ? \"\" /* MaskExpression.EMPTY_STRING */ : result}${this.showMaskTyped ? '' : this.suffix}`;\n    if (result.length === 0) {\n      res = !this.dropSpecialCharacters ? `${this.prefix}${result}` : `${result}`;\n    }\n    const isSpecialCharacterMaskFirstSymbol = inputValue.length === 1 && this.specialCharacters.includes(maskExpression[0]) && inputValue !== maskExpression[0];\n    if (!this._checkSymbolMask(inputValue, maskExpression[1]) && isSpecialCharacterMaskFirstSymbol) {\n      return '';\n    }\n    if (result.includes(\"-\" /* MaskExpression.MINUS */) && this.prefix && this.allowNegativeNumbers) {\n      if (backspaced && result === \"-\" /* MaskExpression.MINUS */) {\n        return '';\n      }\n      res = `${\"-\" /* MaskExpression.MINUS */}${this.prefix}${result.split(\"-\" /* MaskExpression.MINUS */).join(\"\" /* MaskExpression.EMPTY_STRING */)}${this.suffix}`;\n    }\n    return res;\n  }\n  _findDropSpecialChar(inputSymbol) {\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      return this.dropSpecialCharacters.find(val => val === inputSymbol);\n    }\n    return this._findSpecialChar(inputSymbol);\n  }\n  _findSpecialChar(inputSymbol) {\n    return this.specialCharacters.find(val => val === inputSymbol);\n  }\n  _checkSymbolMask(inputSymbol, maskSymbol) {\n    this.patterns = this.customPattern ? this.customPattern : this.patterns;\n    return (this.patterns[maskSymbol]?.pattern && this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ?? false;\n  }\n  _stripToDecimal(str) {\n    return str.split(\"\" /* MaskExpression.EMPTY_STRING */).filter((i, idx) => {\n      const isDecimalMarker = typeof this.decimalMarker === 'string' ? i === this.decimalMarker :\n      // TODO (inepipenko) use utility type\n      this.decimalMarker.includes(i);\n      return i.match('^-?\\\\d') || i === this.thousandSeparator || isDecimalMarker || i === \"-\" /* MaskExpression.MINUS */ && idx === 0 && this.allowNegativeNumbers;\n    }).join(\"\" /* MaskExpression.EMPTY_STRING */);\n  }\n  _charToRegExpExpression(char) {\n    // if (Array.isArray(char)) {\n    // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n    // }\n    if (char) {\n      const charsToEscape = '[\\\\^$.|?*+()';\n      return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n    }\n    return char;\n  }\n  _shiftStep(maskExpression, cursor, inputLength) {\n    const shiftStep = /[*?]/g.test(maskExpression.slice(0, cursor)) ? inputLength : cursor;\n    this._shift.add(shiftStep + this.prefix.length || 0);\n  }\n  _compareOrIncludes(value, comparedValue, excludedValue) {\n    return Array.isArray(comparedValue) ? comparedValue.filter(v => v !== excludedValue).includes(value) : value === comparedValue;\n  }\n  _validIP(valuesIP) {\n    return !(valuesIP.length === 4 && !valuesIP.some((value, index) => {\n      if (valuesIP.length !== index + 1) {\n        return value === \"\" /* MaskExpression.EMPTY_STRING */ || Number(value) > 255;\n      }\n      return value === \"\" /* MaskExpression.EMPTY_STRING */ || Number(value.substring(0, 3)) > 255;\n    }));\n  }\n  _splitPercentZero(value) {\n    if (value === \"-\" /* MaskExpression.MINUS */ && this.allowNegativeNumbers) {\n      return value;\n    }\n    const decimalIndex = typeof this.decimalMarker === 'string' ? value.indexOf(this.decimalMarker) : value.indexOf(\".\" /* MaskExpression.DOT */);\n    const emptyOrMinus = this.allowNegativeNumbers && value.includes(\"-\" /* MaskExpression.MINUS */) ? '-' : '';\n    if (decimalIndex === -1) {\n      const parsedValue = parseInt(emptyOrMinus ? value.slice(1, value.length) : value, 10);\n      return isNaN(parsedValue) ? \"\" /* MaskExpression.EMPTY_STRING */ : `${emptyOrMinus}${parsedValue}`;\n    } else {\n      const integerPart = parseInt(value.replace('-', '').substring(0, decimalIndex), 10);\n      const decimalPart = value.substring(decimalIndex + 1);\n      const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n      const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\" /* MaskExpression.DOT */;\n      return integerString === \"\" /* MaskExpression.EMPTY_STRING */ ? \"\" /* MaskExpression.EMPTY_STRING */ : `${emptyOrMinus}${integerString}${decimal}${decimalPart}`;\n    }\n  }\n  _findFirstNonZeroDigitIndex(inputString) {\n    for (let i = 0; i < inputString.length; i++) {\n      const char = inputString[i];\n      if (char && char >= '1' && char <= '9') {\n        return i;\n      }\n    }\n    return -1;\n  }\n  static {\n    this.ɵfac = function NgxMaskApplierService_Factory(t) {\n      return new (t || NgxMaskApplierService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgxMaskApplierService,\n      factory: NgxMaskApplierService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskApplierService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NgxMaskService extends NgxMaskApplierService {\n  constructor() {\n    super(...arguments);\n    this.isNumberValue = false;\n    this.maskIsShown = '';\n    this.selStart = null;\n    this.selEnd = null;\n    /**\n     * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n     * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n     */\n    this.writingValue = false;\n    this.maskChanged = false;\n    this._maskExpressionArray = [];\n    this.triggerOnMaskChange = false;\n    this._previousValue = '';\n    this._currentValue = '';\n    this._emitValue = false;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.onChange = _ => {};\n    this._elementRef = inject(ElementRef, {\n      optional: true\n    });\n    this.document = inject(DOCUMENT);\n    this._config = inject(NGX_MASK_CONFIG);\n    this._renderer = inject(Renderer2, {\n      optional: true\n    });\n  }\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    if (!maskExpression) {\n      return inputValue !== this.actualValue ? this.actualValue : inputValue;\n    }\n    this.maskIsShown = this.showMaskTyped ? this.showMaskInInput() : \"\" /* MaskExpression.EMPTY_STRING */;\n    if (this.maskExpression === \"IP\" /* MaskExpression.IP */ && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || \"#\" /* MaskExpression.HASH */);\n    }\n    if (this.maskExpression === \"CPF_CNPJ\" /* MaskExpression.CPF_CNPJ */ && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || \"#\" /* MaskExpression.HASH */);\n    }\n    if (!inputValue && this.showMaskTyped) {\n      this.formControlResult(this.prefix);\n      return `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    const getSymbol = !!inputValue && typeof this.selStart === 'number' ? inputValue[this.selStart] ?? \"\" /* MaskExpression.EMPTY_STRING */ : \"\" /* MaskExpression.EMPTY_STRING */;\n    let newInputValue = '';\n    if (this.hiddenInput !== undefined && !this.writingValue) {\n      let actualResult = inputValue && inputValue.length === 1 ? inputValue.split(\"\" /* MaskExpression.EMPTY_STRING */) : this.actualValue.split(\"\" /* MaskExpression.EMPTY_STRING */);\n      // eslint-disable  @typescript-eslint/no-unused-expressions\n      if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n        this.selStart = Number(this.selStart);\n        this.selEnd = Number(this.selEnd);\n      } else {\n        inputValue !== \"\" /* MaskExpression.EMPTY_STRING */ && actualResult.length ? typeof this.selStart === 'number' && typeof this.selEnd === 'number' ? inputValue.length > actualResult.length ? actualResult.splice(this.selStart, 0, getSymbol) : inputValue.length < actualResult.length ? actualResult.length - inputValue.length === 1 ? backspaced ? actualResult.splice(this.selStart - 1, 1) : actualResult.splice(inputValue.length - 1, 1) : actualResult.splice(this.selStart, this.selEnd - this.selStart) : null : null : actualResult = [];\n      }\n      if (this.showMaskTyped) {\n        if (!this.hiddenInput) {\n          inputValue = this.removeMask(inputValue);\n        }\n      }\n      // eslint-enable  @typescript-eslint/no-unused-expressions\n      newInputValue = this.actualValue.length && actualResult.length <= inputValue.length ? this.shiftTypedSymbols(actualResult.join(\"\" /* MaskExpression.EMPTY_STRING */)) : inputValue;\n    }\n    if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n      newInputValue = inputValue;\n    }\n    if (backspaced && this.specialCharacters.indexOf(this.maskExpression[position] ?? \"\" /* MaskExpression.EMPTY_STRING */) !== -1 && this.showMaskTyped && !this.prefix) {\n      newInputValue = this._currentValue;\n    }\n    if (this.deletedSpecialCharacter && position) {\n      if (this.specialCharacters.includes(this.actualValue.slice(position, position + 1))) {\n        position = position + 1;\n      } else if (maskExpression.slice(position - 1, position + 1) !== \"M0\" /* MaskExpression.MONTHS */) {\n        position = position - 2;\n      }\n      this.deletedSpecialCharacter = false;\n    }\n    if (this.showMaskTyped && this.placeHolderCharacter.length === 1 && !this.leadZeroDateTime) {\n      inputValue = this.removeMask(inputValue);\n    }\n    if (this.maskChanged) {\n      newInputValue = inputValue;\n    } else {\n      newInputValue = Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n    }\n    if (this.showMaskTyped && this.keepCharacterPositions && this.actualValue && !justPasted && !this.writingValue) {\n      const value = this.dropSpecialCharacters ? this.removeMask(this.actualValue) : this.actualValue;\n      this.formControlResult(value);\n      return this.actualValue ? this.actualValue : `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    const result = super.applyMask(newInputValue, maskExpression, position, justPasted, backspaced, cb);\n    this.actualValue = this.getActualValue(result);\n    // handle some separator implications:\n    // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n    if (this.thousandSeparator === \".\" /* MaskExpression.DOT */ && this.decimalMarker === \".\" /* MaskExpression.DOT */) {\n      this.decimalMarker = \",\" /* MaskExpression.COMMA */;\n    }\n    // b) remove decimal marker from list of special characters to mask\n    if (this.maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && this.dropSpecialCharacters === true) {\n      this.specialCharacters = this.specialCharacters.filter(item => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n      );\n    }\n    if (result || result === '') {\n      this._previousValue = this._currentValue;\n      this._currentValue = result;\n      this._emitValue = this._previousValue !== this._currentValue || this.maskChanged || this._previousValue === this._currentValue && justPasted;\n    }\n    this._emitValue ? this.writingValue && this.triggerOnMaskChange ? requestAnimationFrame(() => this.formControlResult(result)) : this.formControlResult(result) : '';\n    if (!this.showMaskTyped || this.showMaskTyped && this.hiddenInput) {\n      if (this.hiddenInput) {\n        if (backspaced) {\n          return this.hideInput(result, this.maskExpression);\n        }\n        return `${this.hideInput(result, this.maskExpression)}${this.maskIsShown.slice(result.length)}`;\n      }\n      return result;\n    }\n    const resLen = result.length;\n    const prefNmask = `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    if (this.maskExpression.includes(\"H\" /* MaskExpression.HOURS */)) {\n      const countSkipedSymbol = this._numberSkipedSymbols(result);\n      return `${result}${prefNmask.slice(resLen + countSkipedSymbol)}`;\n    } else if (this.maskExpression === \"IP\" /* MaskExpression.IP */ || this.maskExpression === \"CPF_CNPJ\" /* MaskExpression.CPF_CNPJ */) {\n      return `${result}${prefNmask}`;\n    }\n    return `${result}${prefNmask.slice(resLen)}`;\n  }\n  // get the number of characters that were shifted\n  _numberSkipedSymbols(value) {\n    const regex = /(^|\\D)(\\d\\D)/g;\n    let match = regex.exec(value);\n    let countSkipedSymbol = 0;\n    while (match != null) {\n      countSkipedSymbol += 1;\n      match = regex.exec(value);\n    }\n    return countSkipedSymbol;\n  }\n  applyValueChanges(position, justPasted, backspaced,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n    if (formElement === this._getActiveElement()) {\n      return;\n    }\n    this.clearIfNotMatchFn();\n  }\n  hideInput(inputValue, maskExpression) {\n    return inputValue.split(\"\" /* MaskExpression.EMPTY_STRING */).map((curr, index) => {\n      if (this.patterns && this.patterns[maskExpression[index] ?? \"\" /* MaskExpression.EMPTY_STRING */] && this.patterns[maskExpression[index] ?? \"\" /* MaskExpression.EMPTY_STRING */]?.symbol) {\n        return this.patterns[maskExpression[index] ?? \"\" /* MaskExpression.EMPTY_STRING */]?.symbol;\n      }\n      return curr;\n    }).join(\"\" /* MaskExpression.EMPTY_STRING */);\n  }\n  // this function is not necessary, it checks result against maskExpression\n  getActualValue(res) {\n    const compare = res.split(\"\" /* MaskExpression.EMPTY_STRING */).filter((symbol, i) => {\n      const maskChar = this.maskExpression[i] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n      return this._checkSymbolMask(symbol, maskChar) || this.specialCharacters.includes(maskChar) && symbol === maskChar;\n    });\n    if (compare.join(\"\" /* MaskExpression.EMPTY_STRING */) === res) {\n      return compare.join(\"\" /* MaskExpression.EMPTY_STRING */);\n    }\n    return res;\n  }\n  shiftTypedSymbols(inputValue) {\n    let symbolToReplace = '';\n    const newInputValue = inputValue && inputValue.split(\"\" /* MaskExpression.EMPTY_STRING */).map((currSymbol, index) => {\n      if (this.specialCharacters.includes(inputValue[index + 1] ?? \"\" /* MaskExpression.EMPTY_STRING */) && inputValue[index + 1] !== this.maskExpression[index + 1]) {\n        symbolToReplace = currSymbol;\n        return inputValue[index + 1];\n      }\n      if (symbolToReplace.length) {\n        const replaceSymbol = symbolToReplace;\n        symbolToReplace = \"\" /* MaskExpression.EMPTY_STRING */;\n        return replaceSymbol;\n      }\n      return currSymbol;\n    }) || [];\n    return newInputValue.join(\"\" /* MaskExpression.EMPTY_STRING */);\n  }\n  /**\n   * Convert number value to string\n   * 3.1415 -> '3.1415'\n   * 1e-7 -> '0.0000001'\n   */\n  numberToString(value) {\n    if (!value && value !== 0 || this.maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && (this.leadZero || !this.dropSpecialCharacters) || this.maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && this.separatorLimit.length > 14 && String(value).length > 14) {\n      return String(value);\n    }\n    return Number(value).toLocaleString('fullwide', {\n      useGrouping: false,\n      maximumFractionDigits: 20\n    }).replace(`/${\"-\" /* MaskExpression.MINUS */}/`, \"-\" /* MaskExpression.MINUS */);\n  }\n  showMaskInInput(inputVal) {\n    if (this.showMaskTyped && !!this.shownMaskExpression) {\n      if (this.maskExpression.length !== this.shownMaskExpression.length) {\n        throw new Error('Mask expression must match mask placeholder length');\n      } else {\n        return this.shownMaskExpression;\n      }\n    } else if (this.showMaskTyped) {\n      if (inputVal) {\n        if (this.maskExpression === \"IP\" /* MaskExpression.IP */) {\n          return this._checkForIp(inputVal);\n        }\n        if (this.maskExpression === \"CPF_CNPJ\" /* MaskExpression.CPF_CNPJ */) {\n          return this._checkForCpfCnpj(inputVal);\n        }\n      }\n      if (this.placeHolderCharacter.length === this.maskExpression.length) {\n        return this.placeHolderCharacter;\n      }\n      return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n    }\n    return '';\n  }\n  clearIfNotMatchFn() {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    if (this.clearIfNotMatch && this.prefix.length + this.maskExpression.length + this.suffix.length !== formElement.value.replace(this.placeHolderCharacter, \"\" /* MaskExpression.EMPTY_STRING */).length) {\n      this.formElementProperty = ['value', \"\" /* MaskExpression.EMPTY_STRING */];\n      this.applyMask('', this.maskExpression);\n    }\n  }\n  set formElementProperty([name, value]) {\n    if (!this._renderer || !this._elementRef) {\n      return;\n    }\n    //[TODO]: andriikamaldinov1 find better solution\n    Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n  }\n  checkDropSpecialCharAmount(mask) {\n    const chars = mask.split(\"\" /* MaskExpression.EMPTY_STRING */).filter(item => this._findDropSpecialChar(item));\n    return chars.length;\n  }\n  removeMask(inputValue) {\n    return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n  }\n  _checkForIp(inputVal) {\n    if (inputVal === \"#\" /* MaskExpression.HASH */) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    const arr = [];\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return this.placeHolderCharacter;\n    }\n    if (arr.length > 9 && arr.length <= 12) {\n      return '';\n    }\n    return '';\n  }\n  _checkForCpfCnpj(inputVal) {\n    const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    if (inputVal === \"#\" /* MaskExpression.HASH */) {\n      return cpf;\n    }\n    const arr = [];\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return cpf.slice(arr.length, cpf.length);\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return cpf.slice(arr.length + 1, cpf.length);\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return cpf.slice(arr.length + 2, cpf.length);\n    }\n    if (arr.length > 9 && arr.length < 11) {\n      return cpf.slice(arr.length + 3, cpf.length);\n    }\n    if (arr.length === 11) {\n      return '';\n    }\n    if (arr.length === 12) {\n      if (inputVal.length === 17) {\n        return cnpj.slice(16, cnpj.length);\n      }\n      return cnpj.slice(15, cnpj.length);\n    }\n    if (arr.length > 12 && arr.length <= 14) {\n      return cnpj.slice(arr.length + 4, cnpj.length);\n    }\n    return '';\n  }\n  /**\n   * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n   */\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  /**\n   * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n   * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n   * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n   * model value too. Therefore, we don't trigger onChange in this scenario.\n   * @param inputValue the current form input value\n   */\n  formControlResult(inputValue) {\n    if (this.writingValue || !this.triggerOnMaskChange && this.maskChanged) {\n      this.triggerOnMaskChange && this.maskChanged ? this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue)))))) : '';\n      this.maskChanged = false;\n      return;\n    }\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n    } else if (this.dropSpecialCharacters || !this.dropSpecialCharacters && this.prefix === inputValue) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n    } else {\n      this.onChange(this.outputTransformFn(this._toNumber(inputValue)));\n    }\n  }\n  _toNumber(value) {\n    if (!this.isNumberValue || value === \"\" /* MaskExpression.EMPTY_STRING */) {\n      return value;\n    }\n    if (this.maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && (this.leadZero || !this.dropSpecialCharacters)) {\n      return value;\n    }\n    if (String(value).length > 16 && this.separatorLimit.length > 14) {\n      return String(value);\n    }\n    const num = Number(value);\n    if (this.maskExpression.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && Number.isNaN(num)) {\n      const val = String(value).replace(',', '.');\n      return Number(val);\n    }\n    return Number.isNaN(num) ? value : num;\n  }\n  _removeMask(value, specialCharactersForRemove) {\n    if (this.maskExpression.startsWith(\"percent\" /* MaskExpression.PERCENT */) && value.includes(\".\" /* MaskExpression.DOT */)) {\n      return value;\n    }\n    return value ? value.replace(this._regExpForRemove(specialCharactersForRemove), \"\" /* MaskExpression.EMPTY_STRING */) : value;\n  }\n  _removePrefix(value) {\n    if (!this.prefix) {\n      return value;\n    }\n    return value ? value.replace(this.prefix, \"\" /* MaskExpression.EMPTY_STRING */) : value;\n  }\n  _removeSuffix(value) {\n    if (!this.suffix) {\n      return value;\n    }\n    return value ? value.replace(this.suffix, \"\" /* MaskExpression.EMPTY_STRING */) : value;\n  }\n  _retrieveSeparatorValue(result) {\n    let specialCharacters = Array.isArray(this.dropSpecialCharacters) ? this.specialCharacters.filter(v => {\n      return this.dropSpecialCharacters.includes(v);\n    }) : this.specialCharacters;\n    if (!this.deletedSpecialCharacter && this._checkPatternForSpace() && result.includes(\" \" /* MaskExpression.WHITE_SPACE */) && this.maskExpression.includes(\"*\" /* MaskExpression.SYMBOL_STAR */)) {\n      specialCharacters = specialCharacters.filter(char => char !== \" \" /* MaskExpression.WHITE_SPACE */);\n    }\n    return this._removeMask(result, specialCharacters);\n  }\n  _regExpForRemove(specialCharactersForRemove) {\n    return new RegExp(specialCharactersForRemove.map(item => `\\\\${item}`).join('|'), 'gi');\n  }\n  _replaceDecimalMarkerToDot(value) {\n    const markers = Array.isArray(this.decimalMarker) ? this.decimalMarker : [this.decimalMarker];\n    return value.replace(this._regExpForRemove(markers), \".\" /* MaskExpression.DOT */);\n  }\n  _checkSymbols(result) {\n    if (result === \"\" /* MaskExpression.EMPTY_STRING */) {\n      return result;\n    }\n    if (this.maskExpression.startsWith(\"percent\" /* MaskExpression.PERCENT */) && this.decimalMarker === \",\" /* MaskExpression.COMMA */) {\n      result = result.replace(\",\" /* MaskExpression.COMMA */, \".\" /* MaskExpression.DOT */);\n    }\n    const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n    const separatorValue = this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(result));\n    if (!this.isNumberValue) {\n      return separatorValue;\n    }\n    if (separatorPrecision) {\n      if (result === this.decimalMarker) {\n        return null;\n      }\n      if (this.separatorLimit.length > 14) {\n        return String(separatorValue);\n      }\n      return this._checkPrecision(this.maskExpression, separatorValue);\n    } else {\n      return separatorValue;\n    }\n  }\n  _checkPatternForSpace() {\n    for (const key in this.patterns) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n        const patternString = this.patterns[key]?.pattern.toString();\n        const pattern = this.patterns[key]?.pattern;\n        if (patternString?.includes(\" \" /* MaskExpression.WHITE_SPACE */) && pattern?.test(this.maskExpression)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  // TODO should think about helpers or separting decimal precision to own property\n  _retrieveSeparatorPrecision(maskExpretion) {\n    const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n    return matcher ? Number(matcher[1]) : null;\n  }\n  _checkPrecision(separatorExpression, separatorValue) {\n    const separatorPrecision = separatorExpression.slice(10, 11);\n    if (separatorExpression.indexOf('2') > 0 || this.leadZero && Number(separatorPrecision) > 0) {\n      if (this.decimalMarker === \",\" /* MaskExpression.COMMA */ && this.leadZero) {\n        separatorValue = separatorValue.replace(',', '.');\n      }\n      return this.leadZero ? Number(separatorValue).toFixed(Number(separatorPrecision)) : Number(separatorValue).toFixed(2);\n    }\n    return this.numberToString(separatorValue);\n  }\n  _repeatPatternSymbols(maskExp) {\n    return maskExp.match(/{[0-9]+}/) && maskExp.split(\"\" /* MaskExpression.EMPTY_STRING */).reduce((accum, currVal, index) => {\n      this._start = currVal === \"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */ ? index : this._start;\n      if (currVal !== \"}\" /* MaskExpression.CURLY_BRACKETS_RIGHT */) {\n        return this._findSpecialChar(currVal) ? accum + currVal : accum;\n      }\n      this._end = index;\n      const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n      const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n      if (maskExp.slice(0, this._start).length > 1 && maskExp.includes(\"S\" /* MaskExpression.LETTER_S */)) {\n        const symbols = maskExp.slice(0, this._start - 1);\n        return symbols.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */) ? accum + replaceWith : symbols + accum + replaceWith;\n      } else {\n        return accum + replaceWith;\n      }\n    }, '') || maskExp;\n  }\n  currentLocaleDecimalMarker() {\n    return 1.1.toLocaleString().substring(1, 2);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵNgxMaskService_BaseFactory;\n      return function NgxMaskService_Factory(t) {\n        return (ɵNgxMaskService_BaseFactory || (ɵNgxMaskService_BaseFactory = i0.ɵɵgetInheritedFactory(NgxMaskService)))(t || NgxMaskService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgxMaskService,\n      factory: NgxMaskService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @internal\n */\nfunction _configFactory() {\n  const initConfig = inject(INITIAL_CONFIG);\n  const configValue = inject(NEW_CONFIG);\n  return configValue instanceof Function ? {\n    ...initConfig,\n    ...configValue()\n  } : {\n    ...initConfig,\n    ...configValue\n  };\n}\nfunction provideNgxMask(configValue) {\n  return [{\n    provide: NEW_CONFIG,\n    useValue: configValue\n  }, {\n    provide: INITIAL_CONFIG,\n    useValue: initialConfig\n  }, {\n    provide: NGX_MASK_CONFIG,\n    useFactory: _configFactory\n  }, NgxMaskService];\n}\nfunction provideEnvironmentNgxMask(configValue) {\n  return makeEnvironmentProviders(provideNgxMask(configValue));\n}\nclass NgxMaskDirective {\n  constructor() {\n    this.maskExpression = '';\n    this.specialCharacters = [];\n    this.patterns = {};\n    this.prefix = '';\n    this.suffix = '';\n    this.thousandSeparator = ' ';\n    this.decimalMarker = '.';\n    this.dropSpecialCharacters = null;\n    this.hiddenInput = null;\n    this.showMaskTyped = null;\n    this.placeHolderCharacter = null;\n    this.shownMaskExpression = null;\n    this.showTemplate = null;\n    this.clearIfNotMatch = null;\n    this.validation = null;\n    this.separatorLimit = null;\n    this.allowNegativeNumbers = null;\n    this.leadZeroDateTime = null;\n    this.leadZero = null;\n    this.triggerOnMaskChange = null;\n    this.apm = null;\n    this.inputTransformFn = null;\n    this.outputTransformFn = null;\n    this.keepCharacterPositions = null;\n    this.maskFilled = new EventEmitter();\n    this._maskValue = '';\n    this._position = null;\n    this._maskExpressionArray = [];\n    this._allowFewMaskChangeMask = false;\n    this._justPasted = false;\n    this._isFocused = false;\n    /**For IME composition event */\n    this._isComposing = false;\n    this.document = inject(DOCUMENT);\n    this._maskService = inject(NgxMaskService, {\n      self: true\n    });\n    this._config = inject(NGX_MASK_CONFIG);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.onChange = _ => {};\n    this.onTouch = () => {};\n  }\n  ngOnChanges(changes) {\n    const {\n      maskExpression,\n      specialCharacters,\n      patterns,\n      prefix,\n      suffix,\n      thousandSeparator,\n      decimalMarker,\n      dropSpecialCharacters,\n      hiddenInput,\n      showMaskTyped,\n      placeHolderCharacter,\n      shownMaskExpression,\n      showTemplate,\n      clearIfNotMatch,\n      validation,\n      separatorLimit,\n      allowNegativeNumbers,\n      leadZeroDateTime,\n      leadZero,\n      triggerOnMaskChange,\n      apm,\n      inputTransformFn,\n      outputTransformFn,\n      keepCharacterPositions\n    } = changes;\n    if (maskExpression) {\n      if (maskExpression.currentValue !== maskExpression.previousValue && !maskExpression.firstChange) {\n        this._maskService.maskChanged = true;\n      }\n      if (maskExpression.currentValue && maskExpression.currentValue.split(\"||\" /* MaskExpression.OR */).length > 1) {\n        this._maskExpressionArray = maskExpression.currentValue.split(\"||\" /* MaskExpression.OR */).sort((a, b) => {\n          return a.length - b.length;\n        });\n        this._setMask();\n      } else {\n        this._maskExpressionArray = [];\n        this._maskValue = maskExpression.currentValue || \"\" /* MaskExpression.EMPTY_STRING */;\n        this._maskService.maskExpression = this._maskValue;\n      }\n    }\n    if (specialCharacters) {\n      if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n        return;\n      } else {\n        this._maskService.specialCharacters = specialCharacters.currentValue || [];\n      }\n    }\n    if (allowNegativeNumbers) {\n      this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n      if (this._maskService.allowNegativeNumbers) {\n        this._maskService.specialCharacters = this._maskService.specialCharacters.filter(c => c !== \"-\" /* MaskExpression.MINUS */);\n      }\n    }\n    // Only overwrite the mask available patterns if a pattern has actually been passed in\n    if (patterns && patterns.currentValue) {\n      this._maskService.patterns = patterns.currentValue;\n    }\n    if (apm && apm.currentValue) {\n      this._maskService.apm = apm.currentValue;\n    }\n    if (prefix) {\n      this._maskService.prefix = prefix.currentValue;\n    }\n    if (suffix) {\n      this._maskService.suffix = suffix.currentValue;\n    }\n    if (thousandSeparator) {\n      this._maskService.thousandSeparator = thousandSeparator.currentValue;\n    }\n    if (decimalMarker) {\n      this._maskService.decimalMarker = decimalMarker.currentValue;\n    }\n    if (dropSpecialCharacters) {\n      this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n    }\n    if (hiddenInput) {\n      this._maskService.hiddenInput = hiddenInput.currentValue;\n    }\n    if (showMaskTyped) {\n      this._maskService.showMaskTyped = showMaskTyped.currentValue;\n      if (showMaskTyped.previousValue === false && showMaskTyped.currentValue === true && this._isFocused) {\n        requestAnimationFrame(() => {\n          this._maskService._elementRef?.nativeElement.click();\n        });\n      }\n    }\n    if (placeHolderCharacter) {\n      this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n    }\n    if (shownMaskExpression) {\n      this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n    }\n    if (showTemplate) {\n      this._maskService.showTemplate = showTemplate.currentValue;\n    }\n    if (clearIfNotMatch) {\n      this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n    }\n    if (validation) {\n      this._maskService.validation = validation.currentValue;\n    }\n    if (separatorLimit) {\n      this._maskService.separatorLimit = separatorLimit.currentValue;\n    }\n    if (leadZeroDateTime) {\n      this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n    }\n    if (leadZero) {\n      this._maskService.leadZero = leadZero.currentValue;\n    }\n    if (triggerOnMaskChange) {\n      this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n    }\n    if (inputTransformFn) {\n      this._maskService.inputTransformFn = inputTransformFn.currentValue;\n    }\n    if (outputTransformFn) {\n      this._maskService.outputTransformFn = outputTransformFn.currentValue;\n    }\n    if (keepCharacterPositions) {\n      this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n    }\n    this._applyMask();\n  }\n  validate({\n    value\n  }) {\n    if (!this._maskService.validation || !this._maskValue) {\n      return null;\n    }\n    if (this._maskService.ipError) {\n      return this._createValidationError(value);\n    }\n    if (this._maskService.cpfCnpjError) {\n      return this._createValidationError(value);\n    }\n    if (this._maskValue.startsWith(\"separator\" /* MaskExpression.SEPARATOR */)) {\n      return null;\n    }\n    if (withoutValidation.includes(this._maskValue)) {\n      return null;\n    }\n    if (this._maskService.clearIfNotMatch) {\n      return null;\n    }\n    if (timeMasks.includes(this._maskValue)) {\n      return this._validateTime(value);\n    }\n    if (value && value.toString().length >= 1) {\n      let counterOfOpt = 0;\n      if (this._maskValue.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */) && this._maskValue.includes(\"}\" /* MaskExpression.CURLY_BRACKETS_RIGHT */)) {\n        const lengthInsideCurlyBrackets = this._maskValue.slice(this._maskValue.indexOf(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */) + 1, this._maskValue.indexOf(\"}\" /* MaskExpression.CURLY_BRACKETS_RIGHT */));\n        return lengthInsideCurlyBrackets === String(value.length) ? null : this._createValidationError(value);\n      }\n      if (this._maskValue.startsWith(\"percent\" /* MaskExpression.PERCENT */)) {\n        return null;\n      }\n      for (const key in this._maskService.patterns) {\n        if (this._maskService.patterns[key]?.optional) {\n          if (this._maskValue.indexOf(key) !== this._maskValue.lastIndexOf(key)) {\n            const opt = this._maskValue.split(\"\" /* MaskExpression.EMPTY_STRING */).filter(i => i === key).join(\"\" /* MaskExpression.EMPTY_STRING */);\n            counterOfOpt += opt.length;\n          } else if (this._maskValue.indexOf(key) !== -1) {\n            counterOfOpt++;\n          }\n          if (this._maskValue.indexOf(key) !== -1 && value.toString().length >= this._maskValue.indexOf(key)) {\n            return null;\n          }\n          if (counterOfOpt === this._maskValue.length) {\n            return null;\n          }\n        }\n      }\n      if (this._maskValue.indexOf(\"*\" /* MaskExpression.SYMBOL_STAR */) > 1 && value.toString().length < this._maskValue.indexOf(\"*\" /* MaskExpression.SYMBOL_STAR */) || this._maskValue.indexOf(\"?\" /* MaskExpression.SYMBOL_QUESTION */) > 1 && value.toString().length < this._maskValue.indexOf(\"?\" /* MaskExpression.SYMBOL_QUESTION */)) {\n        return this._createValidationError(value);\n      }\n      if (this._maskValue.indexOf(\"*\" /* MaskExpression.SYMBOL_STAR */) === -1 || this._maskValue.indexOf(\"?\" /* MaskExpression.SYMBOL_QUESTION */) === -1) {\n        value = typeof value === 'number' ? String(value) : value;\n        const array = this._maskValue.split('*');\n        const length = this._maskService.dropSpecialCharacters ? this._maskValue.length - this._maskService.checkDropSpecialCharAmount(this._maskValue) - counterOfOpt : this.prefix ? this._maskValue.length + this.prefix.length - counterOfOpt : this._maskValue.length - counterOfOpt;\n        if (array.length === 1) {\n          if (value.toString().length < length) {\n            return this._createValidationError(value);\n          }\n        }\n        if (array.length > 1) {\n          const lastIndexArray = array[array.length - 1];\n          if (lastIndexArray && this._maskService.specialCharacters.includes(lastIndexArray[0]) && String(value).includes(lastIndexArray[0] ?? '') && !this.dropSpecialCharacters) {\n            const special = value.split(lastIndexArray[0]);\n            return special[special.length - 1].length === lastIndexArray.length - 1 ? null : this._createValidationError(value);\n          } else if ((lastIndexArray && !this._maskService.specialCharacters.includes(lastIndexArray[0]) || !lastIndexArray || this._maskService.dropSpecialCharacters) && value.length >= length - 1) {\n            return null;\n          } else {\n            return this._createValidationError(value);\n          }\n        }\n      }\n      if (this._maskValue.indexOf(\"*\" /* MaskExpression.SYMBOL_STAR */) === 1 || this._maskValue.indexOf(\"?\" /* MaskExpression.SYMBOL_QUESTION */) === 1) {\n        return null;\n      }\n    }\n    if (value) {\n      this.maskFilled.emit();\n      return null;\n    }\n    return null;\n  }\n  onPaste() {\n    this._justPasted = true;\n  }\n  onFocus() {\n    this._isFocused = true;\n  }\n  onModelChange(value) {\n    // on form reset we need to update the actualValue\n    if ((value === \"\" /* MaskExpression.EMPTY_STRING */ || value === null || value === undefined) && this._maskService.actualValue) {\n      this._maskService.actualValue = this._maskService.getActualValue(\"\" /* MaskExpression.EMPTY_STRING */);\n    }\n  }\n  onInput(e) {\n    // If IME is composing text, we wait for the composed text.\n    if (this._isComposing) return;\n    const el = e.target;\n    const transformedValue = this._maskService.inputTransformFn(el.value);\n    if (el.type !== 'number') {\n      if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n        el.value = transformedValue.toString();\n        this._inputValue = el.value;\n        this._setMask();\n        if (!this._maskValue) {\n          this.onChange(el.value);\n          return;\n        }\n        let position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n        if (this.showMaskTyped && this.keepCharacterPositions && this._maskService.placeHolderCharacter.length === 1) {\n          const inputSymbol = el.value.slice(position - 1, position);\n          const prefixLength = this.prefix.length;\n          const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ?? \"\" /* MaskExpression.EMPTY_STRING */);\n          const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ?? \"\" /* MaskExpression.EMPTY_STRING */);\n          const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n          const selStart = Number(this._maskService.selStart) - prefixLength;\n          const selEnd = Number(this._maskService.selEnd) - prefixLength;\n          if (this._code === \"Backspace\" /* MaskExpression.BACKSPACE */) {\n            if (!selectRangeBackspace) {\n              if (this._maskService.selStart === prefixLength) {\n                this._maskService.actualValue = `${this.prefix}${this._maskService.maskIsShown.slice(0, selEnd)}${this._inputValue.split(this.prefix).join('')}`;\n              } else if (this._maskService.selStart === this._maskService.maskIsShown.length + prefixLength) {\n                this._maskService.actualValue = `${this._inputValue}${this._maskService.maskIsShown.slice(selStart, selEnd)}`;\n              } else {\n                this._maskService.actualValue = `${this.prefix}${this._inputValue.split(this.prefix).join('').slice(0, selStart)}${this._maskService.maskIsShown.slice(selStart, selEnd)}${this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength)}${this.suffix}`;\n              }\n            } else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - this.prefix.length, position + 1 - this.prefix.length)) && selectRangeBackspace) {\n              if (selStart === 1 && this.prefix) {\n                this._maskService.actualValue = `${this.prefix}${this._maskService.placeHolderCharacter}${el.value.split(this.prefix).join('').split(this.suffix).join('')}${this.suffix}`;\n                position = position - 1;\n              } else {\n                const part1 = el.value.substring(0, position);\n                const part2 = el.value.substring(position);\n                this._maskService.actualValue = `${part1}${this._maskService.placeHolderCharacter}${part2}`;\n              }\n            }\n          }\n          if (this._code !== \"Backspace\" /* MaskExpression.BACKSPACE */) {\n            if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n              position = Number(el.selectionStart) - 1;\n            } else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) && checkSpecialCharacter && !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n              this._maskService.actualValue = `${el.value.slice(0, position - 1)}${el.value.slice(position, position + 1)}${inputSymbol}${el.value.slice(position + 2)}`;\n              position = position + 1;\n            } else if (checkSymbols) {\n              if (el.value.length === 1 && position === 1) {\n                this._maskService.actualValue = `${this.prefix}${inputSymbol}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${this.suffix}`;\n              } else {\n                this._maskService.actualValue = `${el.value.slice(0, position - 1)}${inputSymbol}${el.value.slice(position + 1).split(this.suffix).join('')}${this.suffix}`;\n              }\n            } else if (this.prefix && el.value.length === 1 && position - prefixLength === 1 && this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ?? \"\" /* MaskExpression.EMPTY_STRING */)) {\n              this._maskService.actualValue = `${this.prefix}${el.value}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${this.suffix}`;\n            }\n          }\n        }\n        let caretShift = 0;\n        let backspaceShift = false;\n        if (this._code === \"Delete\" /* MaskExpression.DELETE */ && \"separator\" /* MaskExpression.SEPARATOR */) {\n          this._maskService.deletedSpecialCharacter = true;\n        }\n        if (this._inputValue.length >= this._maskService.maskExpression.length - 1 && this._code !== \"Backspace\" /* MaskExpression.BACKSPACE */ && this._maskService.maskExpression === \"d0/M0/0000\" /* MaskExpression.DAYS_MONTHS_YEARS */ && position < 10) {\n          const inputSymbol = this._inputValue.slice(position - 1, position);\n          el.value = this._inputValue.slice(0, position - 1) + inputSymbol + this._inputValue.slice(position + 1);\n        }\n        if (this._maskService.maskExpression === \"d0/M0/0000\" /* MaskExpression.DAYS_MONTHS_YEARS */ && this.leadZeroDateTime) {\n          if (position < 3 && Number(el.value) > 31 && Number(el.value) < 40 || position === 5 && Number(el.value.slice(3, 5)) > 12) {\n            position = position + 2;\n          }\n        }\n        if (this._maskService.maskExpression === \"Hh:m0:s0\" /* MaskExpression.HOURS_MINUTES_SECONDS */ && this.apm) {\n          if (this._justPasted && el.value.slice(0, 2) === \"00\" /* MaskExpression.DOUBLE_ZERO */) {\n            el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n          }\n          el.value = el.value === \"00\" /* MaskExpression.DOUBLE_ZERO */ ? \"0\" /* MaskExpression.NUMBER_ZERO */ : el.value;\n        }\n        this._maskService.applyValueChanges(position, this._justPasted, this._code === \"Backspace\" /* MaskExpression.BACKSPACE */ || this._code === \"Delete\" /* MaskExpression.DELETE */, (shift, _backspaceShift) => {\n          this._justPasted = false;\n          caretShift = shift;\n          backspaceShift = _backspaceShift;\n        });\n        // only set the selection if the element is active\n        if (this._getActiveElement() !== el) {\n          return;\n        }\n        if (this._maskService.plusOnePosition) {\n          position = position + 1;\n          this._maskService.plusOnePosition = false;\n        }\n        // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n        if (this._maskExpressionArray.length) {\n          if (this._code === \"Backspace\" /* MaskExpression.BACKSPACE */) {\n            const specialChartMinusOne = this.specialCharacters.includes(this._maskService.actualValue.slice(position - 1, position));\n            const specialChartPlusOne = this.specialCharacters.includes(this._maskService.actualValue.slice(position, position + 1));\n            if (this._allowFewMaskChangeMask && !specialChartPlusOne) {\n              position = el.selectionStart + 1;\n              this._allowFewMaskChangeMask = false;\n            } else {\n              position = specialChartMinusOne ? position - 1 : position;\n            }\n          } else {\n            position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n          }\n        }\n        this._position = this._position === 1 && this._inputValue.length === 1 ? null : this._position;\n        let positionToApply = this._position ? this._inputValue.length + position + caretShift : position + (this._code === \"Backspace\" /* MaskExpression.BACKSPACE */ && !backspaceShift ? 0 : caretShift);\n        if (positionToApply > this._getActualInputLength()) {\n          positionToApply = el.value === this._maskService.decimalMarker && el.value.length === 1 ? this._getActualInputLength() + 1 : this._getActualInputLength();\n        }\n        if (positionToApply < 0) {\n          positionToApply = 0;\n        }\n        el.setSelectionRange(positionToApply, positionToApply);\n        this._position = null;\n      } else {\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n      }\n    } else {\n      if (!this._maskValue) {\n        this.onChange(el.value);\n        return;\n      }\n      this._maskService.applyValueChanges(el.value.length, this._justPasted, this._code === \"Backspace\" /* MaskExpression.BACKSPACE */ || this._code === \"Delete\" /* MaskExpression.DELETE */);\n    }\n  }\n  // IME starts\n  onCompositionStart() {\n    this._isComposing = true;\n  }\n  // IME completes\n  onCompositionEnd(e) {\n    this._isComposing = false;\n    this._justPasted = true;\n    this.onInput(e);\n  }\n  onBlur(e) {\n    if (this._maskValue) {\n      const el = e.target;\n      if (this.leadZero && el.value.length > 0 && typeof this.decimalMarker === 'string') {\n        const maskExpression = this._maskService.maskExpression;\n        const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n        if (precision > 0) {\n          el.value = this.suffix ? el.value.split(this.suffix).join('') : el.value;\n          const decimalPart = el.value.split(this.decimalMarker)[1];\n          el.value = el.value.includes(this.decimalMarker) ? el.value + \"0\" /* MaskExpression.NUMBER_ZERO */.repeat(precision - decimalPart.length) + this.suffix : el.value + this.decimalMarker + \"0\" /* MaskExpression.NUMBER_ZERO */.repeat(precision) + this.suffix;\n          this._maskService.actualValue = el.value;\n        }\n      }\n      this._maskService.clearIfNotMatchFn();\n    }\n    this._isFocused = false;\n    this.onTouch();\n  }\n  onClick(e) {\n    if (!this._maskValue) {\n      return;\n    }\n    const el = e.target;\n    const posStart = 0;\n    const posEnd = 0;\n    if (el !== null && el.selectionStart !== null && el.selectionStart === el.selectionEnd && el.selectionStart > this._maskService.prefix.length &&\n    // eslint-disable-next-line\n    e.keyCode !== 38) {\n      if (this._maskService.showMaskTyped && !this.keepCharacterPositions) {\n        // We are showing the mask in the input\n        this._maskService.maskIsShown = this._maskService.showMaskInInput();\n        if (el.setSelectionRange && this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n          // the input ONLY contains the mask, so position the cursor at the start\n          el.focus();\n          el.setSelectionRange(posStart, posEnd);\n        } else {\n          // the input contains some characters already\n          if (el.selectionStart > this._maskService.actualValue.length) {\n            // if the user clicked beyond our value's length, position the cursor at the end of our value\n            el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n          }\n        }\n      }\n    }\n    const nextValue = el && (el.value === this._maskService.prefix ? this._maskService.prefix + this._maskService.maskIsShown : el.value);\n    /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n    if (el && el.value !== nextValue) {\n      el.value = nextValue;\n    }\n    /** fix of cursor position with prefix when mouse click occur */\n    if (el && el.type !== 'number' && (el.selectionStart || el.selectionEnd) <= this._maskService.prefix.length) {\n      el.selectionStart = this._maskService.prefix.length;\n      return;\n    }\n    /** select only inserted text */\n    if (el && el.selectionEnd > this._getActualInputLength()) {\n      el.selectionEnd = this._getActualInputLength();\n    }\n  }\n  onKeyDown(e) {\n    if (!this._maskValue) {\n      return;\n    }\n    if (this._isComposing) {\n      // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n      if (e.key === 'Enter') this.onCompositionEnd(e);\n      return;\n    }\n    this._code = e.code ? e.code : e.key;\n    const el = e.target;\n    this._inputValue = el.value;\n    this._setMask();\n    if (el.type !== 'number') {\n      if (e.key === \"ArrowUp\" /* MaskExpression.ARROW_UP */) {\n        e.preventDefault();\n      }\n      if (e.key === \"ArrowLeft\" /* MaskExpression.ARROW_LEFT */ || e.key === \"Backspace\" /* MaskExpression.BACKSPACE */ || e.key === \"Delete\" /* MaskExpression.DELETE */) {\n        if (e.key === \"Backspace\" /* MaskExpression.BACKSPACE */ && el.value.length === 0) {\n          el.selectionStart = el.selectionEnd;\n        }\n        if (e.key === \"Backspace\" /* MaskExpression.BACKSPACE */ && el.selectionStart !== 0) {\n          // If specialChars is false, (shouldn't ever happen) then set to the defaults\n          this.specialCharacters = this.specialCharacters?.length ? this.specialCharacters : this._config.specialCharacters;\n          if (this.prefix.length > 1 && el.selectionStart <= this.prefix.length) {\n            el.setSelectionRange(this.prefix.length, el.selectionEnd);\n          } else {\n            if (this._inputValue.length !== el.selectionStart && el.selectionStart !== 1) {\n              while (this.specialCharacters.includes((this._inputValue[el.selectionStart - 1] ?? \"\" /* MaskExpression.EMPTY_STRING */).toString()) && (this.prefix.length >= 1 && el.selectionStart > this.prefix.length || this.prefix.length === 0)) {\n                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n              }\n            }\n          }\n        }\n        this.checkSelectionOnDeletion(el);\n        if (this._maskService.prefix.length && el.selectionStart <= this._maskService.prefix.length && el.selectionEnd <= this._maskService.prefix.length) {\n          e.preventDefault();\n        }\n        const cursorStart = el.selectionStart;\n        if (e.key === \"Backspace\" /* MaskExpression.BACKSPACE */ && !el.readOnly && cursorStart === 0 && el.selectionEnd === el.value.length && el.value.length !== 0) {\n          this._position = this._maskService.prefix ? this._maskService.prefix.length : 0;\n          this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position);\n        }\n      }\n      if (!!this.suffix && this.suffix.length > 1 && this._inputValue.length - this.suffix.length < el.selectionStart) {\n        el.setSelectionRange(this._inputValue.length - this.suffix.length, this._inputValue.length);\n      } else if (e.code === 'KeyA' && e.ctrlKey || e.code === 'KeyA' && e.metaKey // Cmd + A (Mac)\n      ) {\n        el.setSelectionRange(0, this._getActualInputLength());\n        e.preventDefault();\n      }\n      this._maskService.selStart = el.selectionStart;\n      this._maskService.selEnd = el.selectionEnd;\n    }\n  }\n  /** It writes the value in the input */\n  async writeValue(controlValue) {\n    if (typeof controlValue === 'object' && controlValue !== null && 'value' in controlValue) {\n      if ('disable' in controlValue) {\n        this.setDisabledState(Boolean(controlValue.disable));\n      }\n      controlValue = controlValue.value;\n    }\n    if (controlValue !== null) {\n      controlValue = this.inputTransformFn ? this.inputTransformFn(controlValue) : controlValue;\n    }\n    if (typeof controlValue === 'string' || typeof controlValue === 'number' || controlValue === null || controlValue === undefined) {\n      if (controlValue === null || controlValue === undefined || controlValue === '') {\n        this._maskService._currentValue = '';\n        this._maskService._previousValue = '';\n      }\n      let inputValue = controlValue;\n      if (typeof inputValue === 'number' || this._maskValue.startsWith(\"separator\" /* MaskExpression.SEPARATOR */)) {\n        inputValue = String(inputValue);\n        const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n        if (!Array.isArray(this._maskService.decimalMarker)) {\n          inputValue = this._maskService.decimalMarker !== localeDecimalMarker ? inputValue.replace(localeDecimalMarker, this._maskService.decimalMarker) : inputValue;\n        }\n        if (this._maskService.leadZero && inputValue && this.maskExpression && this.dropSpecialCharacters !== false) {\n          inputValue = this._maskService._checkPrecision(this._maskService.maskExpression, inputValue);\n        }\n        if (this.decimalMarker === \",\" /* MaskExpression.COMMA */ || Array.isArray(this._maskService.decimalMarker) && this.thousandSeparator === \".\" /* MaskExpression.DOT */) {\n          inputValue = inputValue.toString().replace(\".\" /* MaskExpression.DOT */, \",\" /* MaskExpression.COMMA */);\n        }\n        if (this.maskExpression?.startsWith(\"separator\" /* MaskExpression.SEPARATOR */) && this.leadZero) {\n          requestAnimationFrame(() => {\n            this._maskService.applyMask(inputValue?.toString() ?? '', this._maskService.maskExpression);\n          });\n        }\n        this._maskService.isNumberValue = true;\n      }\n      if (typeof inputValue !== 'string') {\n        inputValue = '';\n      }\n      this._inputValue = inputValue;\n      this._setMask();\n      if (inputValue && this._maskService.maskExpression || this._maskService.maskExpression && (this._maskService.prefix || this._maskService.showMaskTyped)) {\n        // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n        typeof this.inputTransformFn !== 'function' ? this._maskService.writingValue = true : '';\n        this._maskService.formElementProperty = ['value', this._maskService.applyMask(inputValue, this._maskService.maskExpression)];\n        // Let the service know we've finished writing value\n        typeof this.inputTransformFn !== 'function' ? this._maskService.writingValue = false : '';\n      } else {\n        this._maskService.formElementProperty = ['value', inputValue];\n      }\n      this._inputValue = inputValue;\n    } else {\n      console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof controlValue);\n    }\n  }\n  registerOnChange(fn) {\n    this._maskService.onChange = this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  checkSelectionOnDeletion(el) {\n    el.selectionStart = Math.min(Math.max(this.prefix.length, el.selectionStart), this._inputValue.length - this.suffix.length);\n    el.selectionEnd = Math.min(Math.max(this.prefix.length, el.selectionEnd), this._inputValue.length - this.suffix.length);\n  }\n  /** It disables the input element */\n  setDisabledState(isDisabled) {\n    this._maskService.formElementProperty = ['disabled', isDisabled];\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  _applyMask() {\n    this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue || '');\n    this._maskService.formElementProperty = ['value', this._maskService.applyMask(this._inputValue, this._maskService.maskExpression)];\n  }\n  _validateTime(value) {\n    const rowMaskLen = this._maskValue.split(\"\" /* MaskExpression.EMPTY_STRING */).filter(s => s !== ':').length;\n    if (!value) {\n      return null; // Don't validate empty values to allow for optional form control\n    }\n    if (+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen || value.length <= rowMaskLen - 2) {\n      return this._createValidationError(value);\n    }\n    return null;\n  }\n  _getActualInputLength() {\n    return this._maskService.actualValue.length || this._maskService.actualValue.length + this._maskService.prefix.length;\n  }\n  _createValidationError(actualValue) {\n    return {\n      mask: {\n        requiredMask: this._maskValue,\n        actualValue\n      }\n    };\n  }\n  _setMask() {\n    this._maskExpressionArray.some(mask => {\n      const specialChart = mask.split(\"\" /* MaskExpression.EMPTY_STRING */).some(char => this._maskService.specialCharacters.includes(char));\n      if (specialChart && this._inputValue && this._areAllCharactersInEachStringSame(this._maskExpressionArray) || mask.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */)) {\n        const test = this._maskService.removeMask(this._inputValue)?.length <= this._maskService.removeMask(mask)?.length;\n        if (test) {\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = mask.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */) ? this._maskService._repeatPatternSymbols(mask) : mask;\n          return test;\n        } else {\n          if (this._code === \"Backspace\" /* MaskExpression.BACKSPACE */) {\n            this._allowFewMaskChangeMask = true;\n          }\n          const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = expression.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */) ? this._maskService._repeatPatternSymbols(expression) : expression;\n        }\n      } else {\n        const check = this._maskService.removeMask(this._inputValue)?.split(\"\" /* MaskExpression.EMPTY_STRING */).every((character, index) => {\n          const indexMask = mask.charAt(index);\n          return this._maskService._checkSymbolMask(character, indexMask);\n        });\n        if (check || this._justPasted) {\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = mask;\n          return check;\n        }\n      }\n    });\n  }\n  _areAllCharactersInEachStringSame(array) {\n    const specialCharacters = this._maskService.specialCharacters;\n    function removeSpecialCharacters(str) {\n      const regex = new RegExp(`[${specialCharacters.map(ch => `\\\\${ch}`).join('')}]`, 'g');\n      return str.replace(regex, '');\n    }\n    const processedArr = array.map(removeSpecialCharacters);\n    return processedArr.every(str => {\n      const uniqueCharacters = new Set(str);\n      return uniqueCharacters.size === 1;\n    });\n  }\n  static {\n    this.ɵfac = function NgxMaskDirective_Factory(t) {\n      return new (t || NgxMaskDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgxMaskDirective,\n      selectors: [[\"input\", \"mask\", \"\"], [\"textarea\", \"mask\", \"\"]],\n      hostBindings: function NgxMaskDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"paste\", function NgxMaskDirective_paste_HostBindingHandler() {\n            return ctx.onPaste();\n          })(\"focus\", function NgxMaskDirective_focus_HostBindingHandler($event) {\n            return ctx.onFocus($event);\n          })(\"ngModelChange\", function NgxMaskDirective_ngModelChange_HostBindingHandler($event) {\n            return ctx.onModelChange($event);\n          })(\"input\", function NgxMaskDirective_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          })(\"compositionstart\", function NgxMaskDirective_compositionstart_HostBindingHandler($event) {\n            return ctx.onCompositionStart($event);\n          })(\"compositionend\", function NgxMaskDirective_compositionend_HostBindingHandler($event) {\n            return ctx.onCompositionEnd($event);\n          })(\"blur\", function NgxMaskDirective_blur_HostBindingHandler($event) {\n            return ctx.onBlur($event);\n          })(\"click\", function NgxMaskDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown\", function NgxMaskDirective_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n      },\n      inputs: {\n        maskExpression: [i0.ɵɵInputFlags.None, \"mask\", \"maskExpression\"],\n        specialCharacters: \"specialCharacters\",\n        patterns: \"patterns\",\n        prefix: \"prefix\",\n        suffix: \"suffix\",\n        thousandSeparator: \"thousandSeparator\",\n        decimalMarker: \"decimalMarker\",\n        dropSpecialCharacters: \"dropSpecialCharacters\",\n        hiddenInput: \"hiddenInput\",\n        showMaskTyped: \"showMaskTyped\",\n        placeHolderCharacter: \"placeHolderCharacter\",\n        shownMaskExpression: \"shownMaskExpression\",\n        showTemplate: \"showTemplate\",\n        clearIfNotMatch: \"clearIfNotMatch\",\n        validation: \"validation\",\n        separatorLimit: \"separatorLimit\",\n        allowNegativeNumbers: \"allowNegativeNumbers\",\n        leadZeroDateTime: \"leadZeroDateTime\",\n        leadZero: \"leadZero\",\n        triggerOnMaskChange: \"triggerOnMaskChange\",\n        apm: \"apm\",\n        inputTransformFn: \"inputTransformFn\",\n        outputTransformFn: \"outputTransformFn\",\n        keepCharacterPositions: \"keepCharacterPositions\"\n      },\n      outputs: {\n        maskFilled: \"maskFilled\"\n      },\n      exportAs: [\"mask\", \"ngxMask\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, NgxMaskService]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[mask], textarea[mask]',\n      standalone: true,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, NgxMaskService],\n      exportAs: 'mask,ngxMask'\n    }]\n  }], null, {\n    maskExpression: [{\n      type: Input,\n      args: ['mask']\n    }],\n    specialCharacters: [{\n      type: Input\n    }],\n    patterns: [{\n      type: Input\n    }],\n    prefix: [{\n      type: Input\n    }],\n    suffix: [{\n      type: Input\n    }],\n    thousandSeparator: [{\n      type: Input\n    }],\n    decimalMarker: [{\n      type: Input\n    }],\n    dropSpecialCharacters: [{\n      type: Input\n    }],\n    hiddenInput: [{\n      type: Input\n    }],\n    showMaskTyped: [{\n      type: Input\n    }],\n    placeHolderCharacter: [{\n      type: Input\n    }],\n    shownMaskExpression: [{\n      type: Input\n    }],\n    showTemplate: [{\n      type: Input\n    }],\n    clearIfNotMatch: [{\n      type: Input\n    }],\n    validation: [{\n      type: Input\n    }],\n    separatorLimit: [{\n      type: Input\n    }],\n    allowNegativeNumbers: [{\n      type: Input\n    }],\n    leadZeroDateTime: [{\n      type: Input\n    }],\n    leadZero: [{\n      type: Input\n    }],\n    triggerOnMaskChange: [{\n      type: Input\n    }],\n    apm: [{\n      type: Input\n    }],\n    inputTransformFn: [{\n      type: Input\n    }],\n    outputTransformFn: [{\n      type: Input\n    }],\n    keepCharacterPositions: [{\n      type: Input\n    }],\n    maskFilled: [{\n      type: Output\n    }],\n    onPaste: [{\n      type: HostListener,\n      args: ['paste']\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus', ['$event']]\n    }],\n    onModelChange: [{\n      type: HostListener,\n      args: ['ngModelChange', ['$event']]\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }],\n    onCompositionStart: [{\n      type: HostListener,\n      args: ['compositionstart', ['$event']]\n    }],\n    onCompositionEnd: [{\n      type: HostListener,\n      args: ['compositionend', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur', ['$event']]\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass NgxMaskPipe {\n  constructor() {\n    this.defaultOptions = inject(NGX_MASK_CONFIG);\n    this._maskService = inject(NgxMaskService);\n    this._maskExpressionArray = [];\n    this.mask = '';\n  }\n  transform(value, mask, {\n    patterns,\n    ...config\n  } = {}) {\n    const currentConfig = {\n      maskExpression: mask,\n      ...this.defaultOptions,\n      ...config,\n      patterns: {\n        ...this._maskService.patterns,\n        ...patterns\n      }\n    };\n    Object.entries(currentConfig).forEach(([key, value]) => {\n      //eslint-disable-next-line  @typescript-eslint/no-explicit-any\n      this._maskService[key] = value;\n    });\n    if (mask.includes('||')) {\n      if (mask.split('||').length > 1) {\n        this._maskExpressionArray = mask.split('||').sort((a, b) => {\n          return a.length - b.length;\n        });\n        this._setMask(value);\n        return this._maskService.applyMask(`${value}`, this.mask);\n      } else {\n        this._maskExpressionArray = [];\n        return this._maskService.applyMask(`${value}`, this.mask);\n      }\n    }\n    if (mask.includes(\"{\" /* MaskExpression.CURLY_BRACKETS_LEFT */)) {\n      return this._maskService.applyMask(`${value}`, this._maskService._repeatPatternSymbols(mask));\n    }\n    if (mask.startsWith(\"separator\" /* MaskExpression.SEPARATOR */)) {\n      if (config.decimalMarker) {\n        this._maskService.decimalMarker = config.decimalMarker;\n      }\n      if (config.thousandSeparator) {\n        this._maskService.thousandSeparator = config.thousandSeparator;\n      }\n      if (config.leadZero) {\n        this._maskService.leadZero = config.leadZero;\n      }\n      value = String(value);\n      const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n      if (!Array.isArray(this._maskService.decimalMarker)) {\n        value = this._maskService.decimalMarker !== localeDecimalMarker ? value.replace(localeDecimalMarker, this._maskService.decimalMarker) : value;\n      }\n      if (this._maskService.leadZero && value && this._maskService.dropSpecialCharacters !== false) {\n        value = this._maskService._checkPrecision(mask, value);\n      }\n      if (this._maskService.decimalMarker === \",\" /* MaskExpression.COMMA */) {\n        value = value.toString().replace(\".\" /* MaskExpression.DOT */, \",\" /* MaskExpression.COMMA */);\n      }\n      this._maskService.isNumberValue = true;\n    }\n    if (value === null || value === undefined) {\n      return this._maskService.applyMask('', mask);\n    }\n    return this._maskService.applyMask(`${value}`, mask);\n  }\n  _setMask(value) {\n    if (this._maskExpressionArray.length > 0) {\n      this._maskExpressionArray.some(mask => {\n        const test = this._maskService.removeMask(value)?.length <= this._maskService.removeMask(mask)?.length;\n        if (value && test) {\n          this.mask = mask;\n          return test;\n        } else {\n          const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ?? \"\" /* MaskExpression.EMPTY_STRING */;\n          this.mask = expression;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function NgxMaskPipe_Factory(t) {\n      return new (t || NgxMaskPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"mask\",\n      type: NgxMaskPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'mask',\n      pure: true,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskPipe, NgxMaskService, initialConfig, provideEnvironmentNgxMask, provideNgxMask, timeMasks, withoutValidation };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,aAAa,IAAI,eAAe,qBAAqB;AAC3D,IAAM,iBAAiB,IAAI,eAAe,yBAAyB;AACnE,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,mBAAmB;AAAA,EACnB,eAAe,CAAC,KAAK,GAAG;AAAA,EACxB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,YAAY;AAAA,EACZ,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACxF,kBAAkB;AAAA,EAClB,KAAK;AAAA,EACL,UAAU;AAAA,EACV,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,kBAAkB,WAAS;AAAA,EAC3B,mBAAmB,WAAS;AAAA,EAC5B,YAAY,IAAI,aAAa;AAAA,EAC7B,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACH,SAAS,IAAI,OAAO,KAAK;AAAA,MACzB,UAAU;AAAA,IACZ;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,MACzB,QAAQ;AAAA,IACV;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,aAAa;AAAA,IACnC;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,UAAU;AAAA,IAChC;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,OAAO;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,OAAO;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAM,YAAY;AAAA,EAAC;AAAA,EAAuD;AAAA,EAA4C;AAAA;AAA4C;AAClK,IAAM,oBAAoB;AAAA,EAAC;AAAA,EAAwC;AAAA,EAAsC;AAAA,EAAmC;AAAA,EAAmC;AAAA,EAA4C;AAAA,EAAqD;AAAA,EAA0C;AAAA,EAAgC;AAAA;AAAgC;AAC1X,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,UAAU,OAAO,eAAe;AACrC,SAAK,wBAAwB,KAAK,QAAQ;AAC1C,SAAK,cAAc,KAAK,QAAQ;AAChC,SAAK,kBAAkB,KAAK,QAAQ;AACpC,SAAK,oBAAoB,KAAK,QAAQ;AACtC,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,KAAK,QAAQ;AAC3B,SAAK,SAAS,KAAK,QAAQ;AAC3B,SAAK,oBAAoB,KAAK,QAAQ;AACtC,SAAK,gBAAgB,KAAK,QAAQ;AAClC,SAAK,gBAAgB,KAAK,QAAQ;AAClC,SAAK,uBAAuB,KAAK,QAAQ;AACzC,SAAK,aAAa,KAAK,QAAQ;AAC/B,SAAK,iBAAiB,KAAK,QAAQ;AACnC,SAAK,uBAAuB,KAAK,QAAQ;AACzC,SAAK,mBAAmB,KAAK,QAAQ;AACrC,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,MAAM,KAAK,QAAQ;AACxB,SAAK,mBAAmB,KAAK,QAAQ;AACrC,SAAK,oBAAoB,KAAK,QAAQ;AACtC,SAAK,yBAAyB,KAAK,QAAQ;AAC3C,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,0BAA0B;AAC/B,SAAK,wBAAwB,CAAC,KAAK,uBAAuB,cAAc,cAAc;AACpF,UAAI,IAAI,CAAC;AACT,UAAI,cAAc;AAClB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,cAAM,SAAS,IAAI,OAAO,aAAa,IAAI,OAAK,eAAe,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC;AACxG,YAAI,IAAI,MAAM,MAAM;AACpB,sBAAc,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK;AAAA,MAC1C,OAAO;AACL,YAAI,IAAI,MAAM,YAAY;AAC1B,sBAAc;AAAA,MAChB;AACA,YAAM,WAAW,EAAE,SAAS,IAAI,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC,KAAK;AAC1D,UAAI,MAAM,EAAE,CAAC,KAAK;AAClB,YAAM,iBAAiB,KAAK,eAAe;AAAA,QAAQ;AAAA,QAAO;AAAA;AAAA,MAAoC;AAC9F,UAAI,kBAAkB,CAAC,gBAAgB;AACrC,YAAI,IAAI,CAAC,MAAM,KAAgC;AAC7C,gBAAM,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,EAAE,MAAM,GAAG,eAAe,MAAM,CAAC;AAAA,QACpE,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG,eAAe,MAAM;AAAA,QAC1C;AAAA,MACF;AACA,YAAM,MAAM;AACZ,aAAO,yBAAyB,IAAI,KAAK,GAAG,GAAG;AAC7C,cAAM,IAAI,QAAQ,KAAK,OAAO,wBAAwB,IAAI;AAAA,MAC5D;AACA,UAAI,cAAc,QAAW;AAC3B,eAAO,MAAM;AAAA,MACf,WAAW,cAAc,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,aAAO,MAAM,SAAS,UAAU,GAAG,YAAY,CAAC;AAAA,IAClD;AACA,SAAK,aAAa,SAAO;AACvB,YAAM,eAAe,IAAI,QAAQ,KAAK,GAAG;AACzC,YAAM,QAAQ,OAAO,KAAK,wBAAwB,IAAI;AAAA,QAAS;AAAA;AAAA,MAA8B,IAAI,aAAa,MAAM,GAAG,IAAI,MAAM,IAAI,YAAY;AACjJ,aAAO,CAAC,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS;AAAA,IACjD;AACA,SAAK,eAAe,oBAAkB;AACpC,YAAM,IAAI,eAAe;AAAA,QAAM;AAAA;AAAA,MAA4B;AAC3D,UAAI,EAAE,SAAS,GAAG;AAChB,eAAO,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;AAAA,MAC/B;AACA,aAAO;AAAA,IACT;AACA,SAAK,uBAAuB,gBAAc;AACxC,eAAS,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,cAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,QAAQ,MAAM;AAC3D,YAAI,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK,QAAQ,SAAS,MAAM,IAAI,IAAI,KAAK,CAAC,WAAW,SAAS,KAAK,OAAO,UAAU,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI;AAC1J,iBAAO,WAAW;AAAA,YAAQ;AAAA,YAAQ;AAAA;AAAA,UAAoC;AAAA,QACxE;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,sBAAsB,CAAC,YAAY,WAAW,kBAAkB;AACnE,UAAI,YAAY,UAAU;AAExB,YAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,gBAAM,SAAS,cAAc,KAAK,QAAM,OAAO,KAAK,iBAAiB;AACrE,0BAAgB,SAAS,SAAS,cAAc,CAAC;AAAA,QACnD;AACA,cAAM,iBAAiB,IAAI,OAAO,KAAK,wBAAwB,aAAa,IAAI,OAAO,SAAS,MAAM;AACtG,cAAM,iBAAiB,WAAW,MAAM,cAAc;AACtD,cAAM,wBAAwB,kBAAkB,eAAe,CAAC,GAAG,WAAW;AAC9E,YAAI,uBAAuB,IAAI,WAAW;AACxC,gBAAM,OAAO,uBAAuB,IAAI;AACxC,uBAAa,WAAW,UAAU,GAAG,WAAW,SAAS,IAAI;AAAA,QAC/D;AACA,YAAI,cAAc,KAAK,KAAK,mBAAmB,WAAW,WAAW,SAAS,CAAC,GAAG,eAAe,KAAK,iBAAiB,GAAG;AACxH,uBAAa,WAAW,UAAU,GAAG,WAAW,SAAS,CAAC;AAAA,QAC5D;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,qBAAqB,YAAY,gBAAgB;AAC/C,UAAM,CAAC,MAAM,aAAa,IAAI;AAC9B,SAAK,gBAAgB;AACrB,WAAO,KAAK,UAAU,YAAY,IAAI;AAAA,EACxC;AAAA,EACA,UAAU,YAAY,gBAAgB,WAAW,GAAG,aAAa,OAAO,aAAa,OAErF,KAAK,MAAM;AAAA,EAAC,GAAG;AACb,QAAI,CAAC,kBAAkB,OAAO,eAAe,UAAU;AACrD,aAAO;AAAA,IACT;AACA,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,WAAW;AACf,QAAI,WAAW,MAAM,GAAG,KAAK,OAAO,MAAM,MAAM,KAAK,QAAQ;AAC3D,mBAAa,WAAW,MAAM,KAAK,OAAO,QAAQ,WAAW,MAAM;AAAA,IACrE;AACA,QAAI,CAAC,CAAC,KAAK,UAAU,YAAY,SAAS,GAAG;AAC3C,mBAAa,KAAK,qBAAqB,UAAU;AAAA,IACnD;AACA,QAAI,eAAe,OAAO,KAAK,QAAQ;AACrC,mBAAa;AAAA,IACf;AACA,UAAM,aAAa,WAAW,SAAS,EAAE;AAAA,MAAM;AAAA;AAAA,IAAoC;AACnF,QAAI,KAAK,wBAAwB,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,KAAgC;AACxG,gBAAU,WAAW,MAAM,QAAQ,SAAS,CAAC;AAAA,IAC/C;AACA,QAAI,mBAAmB,MAA8B;AACnD,YAAM,WAAW,WAAW;AAAA,QAAM;AAAA;AAAA,MAA4B;AAC9D,WAAK,UAAU,KAAK,SAAS,QAAQ;AACrC,uBAAiB;AAAA,IACnB;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAI,WAAW,CAAC,GAAG,MAAM,KAAK,GAAG;AAC/B,YAAI;AAAA,UAAK,WAAW,CAAC,KAAK;AAAA;AAAA,QAAoC;AAAA,MAChE;AAAA,IACF;AACA,QAAI,mBAAmB,YAA0C;AAC/D,WAAK,eAAe,IAAI,WAAW,MAAM,IAAI,WAAW;AACxD,UAAI,IAAI,SAAS,IAAI;AACnB,yBAAiB;AAAA,MACnB,OAAO;AACL,yBAAiB;AAAA,MACnB;AAAA,IACF;AACA,QAAI,eAAe;AAAA,MAAW;AAAA;AAAA,IAAsC,GAAG;AACrE,UAAI,WAAW,MAAM,aAAa;AAAA,MAElC,WAAW,MAAM,oCAAoC,KAAK,CAAC,YAAY;AACrE,qBAAa,KAAK,gBAAgB,UAAU;AAC5C,cAAM,YAAY,KAAK,aAAa,cAAc;AAClD,qBAAa,KAAK,oBAAoB,YAAY,WAAW,KAAK,aAAa;AAAA,MACjF;AACA,YAAM,gBAAgB,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB;AACpF,UAAI,WAAW,QAAQ,aAAa,IAAI,KAAK,CAAC,KAAK,WAAW,WAAW,UAAU,GAAG,WAAW,QAAQ,aAAa,CAAC,CAAC,GAAG;AACzH,YAAI,OAAO,WAAW,UAAU,GAAG,WAAW,QAAQ,aAAa,IAAI,CAAC;AACxE,YAAI,KAAK,wBAAwB,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,OAAkC,CAAC,YAAY;AACvH,iBAAO,WAAW,UAAU,GAAG,WAAW,QAAQ,aAAa,CAAC;AAAA,QAClE;AACA,qBAAa,GAAG,IAAI,GAAG,WAAW,UAAU,WAAW,QAAQ,aAAa,GAAG,WAAW,MAAM,CAAC;AAAA,MACnG;AACA,UAAI,QAAQ;AACZ,WAAK,wBAAwB,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,MAAiC,QAAQ,GAAG,GAA8B,GAAG,WAAW,MAAM,SAAS,GAAG,SAAS,WAAW,MAAM,CAAC,KAAK,QAAQ;AACxN,UAAI,KAAK,WAAW,KAAK,GAAG;AAC1B,iBAAS,KAAK,kBAAkB,UAAU;AAAA,MAC5C,OAAO;AACL,iBAAS,KAAK,kBAAkB,WAAW,UAAU,GAAG,WAAW,SAAS,CAAC,CAAC;AAAA,MAChF;AAAA,IACF,WAAW,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,GAAG;AAChF,UAAI,WAAW,MAAM,WAAW,KAAK,WAAW,MAAM,SAAS,KAAK,WAAW,MAAM,aAAa,KAAK,WAAW,MAAM,sCAAsC,KAAK,WAAW,MAAM,eAAe,GAAG;AACpM,qBAAa,KAAK,gBAAgB,UAAU;AAAA,MAC9C;AACA,YAAM,YAAY,KAAK,aAAa,cAAc;AAClD,YAAM,gBAAgB,MAAM,QAAQ,KAAK,aAAa,IAAI,MAA+B,KAAK;AAC9F,UAAI,cAAc,GAAG;AACnB,qBAAa,KAAK,uBAAuB,WAAW,SAAS,KAAK,WAAW,CAAC,MAAM,OAAkC,WAAW,CAAC,MAAM,OAAwC,WAAW,CAAC,MAAM,KAAK,qBAAqB,WAAW,CAAC,MAAM,OAAkC,WAAW,CAAC,MAAM,MAA+B,MAAM,WAAW,MAAM,GAAG,WAAW,MAAM,IAAI,WAAW,CAAC,MAAM,OAAwC,WAAW,SAAS,KAAK,WAAW,CAAC,MAAM,KAAK,qBAAqB,WAAW,CAAC,MAAM,OAAkC,WAAW,CAAC,MAAM,MAA+B,WAAW,MAAM,GAAG,WAAW,MAAM,IAAI,aAAa,WAAW,SAAS,KAAK,WAAW,CAAC,MAAM,OAAwC,WAAW,CAAC,MAAM,KAAK,qBAAqB,WAAW,CAAC,MAAM,OAAkC,WAAW,CAAC,MAAM,MAA+B,WAAW,MAAM,GAAG,WAAW,MAAM,IAAI;AAAA,MACz5B,OAAO;AACL,YAAI,WAAW,CAAC,MAAM,iBAAiB,WAAW,SAAS,GAAG;AAC5D,uBAAa,MAAuC,WAAW,MAAM,GAAG,WAAW,SAAS,CAAC;AAC7F,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,WAAW,CAAC,MAAM,OAAwC,WAAW,CAAC,MAAM,iBAAiB,WAAW,CAAC,MAAM,KAAK,mBAAmB;AACzI,uBAAa,WAAW,SAAS,IAAI,WAAW,MAAM,GAAG,CAAC,IAAI,gBAAgB,WAAW,MAAM,GAAG,WAAW,SAAS,CAAC,IAAI;AAC3H,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,KAAK,wBAAwB,WAAW,CAAC,MAAM,QAAmC,WAAW,CAAC,MAAM,iBAAiB,WAAW,CAAC,MAAM,MAAuC;AAChL,uBAAa,WAAW,CAAC,MAAM,iBAAiB,WAAW,SAAS,IAAI,WAAW,MAAM,GAAG,CAAC,IAAI,MAAuC,WAAW,MAAM,GAAG,WAAW,MAAM,IAAI,WAAW,CAAC,MAAM,OAAwC,WAAW,SAAS,KAAK,WAAW,CAAC,MAAM,gBAAgB,WAAW,MAAM,GAAG,CAAC,IAAI,gBAAgB,WAAW,MAAM,GAAG,WAAW,MAAM,IAAI;AACxX,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AACA,UAAI,YAAY;AACd,cAAM,sBAAsB,WAAW,MAAM,KAAK,4BAA4B,UAAU,GAAG,WAAW,MAAM;AAC5G,cAAM,gCAAgC,WAAW,QAAQ,MAAM,OAAwC,WAAW,QAAQ,MAAM;AAChI,cAAM,sBAAsB,WAAW,CAAC,MAAM;AAC9C,cAAM,iBAAiB,WAAW,CAAC,MAAM;AACzC,cAAM,oBAAoB,WAAW,CAAC,MAAM,KAAK;AACjD,cAAM,0BAA0B,WAAW,CAAC,MAAM;AAClD,cAAM,uBAAuB,WAAW,CAAC,MAAM;AAC/C,cAAM,2BAA2B,WAAW,CAAC,MAAM;AACnD,YAAI,uBAAuB,2BAA2B,iCAAiC,WAAW,GAAG;AACnG,uBAAa;AAAA,QACf;AACA,YAAI,kBAAkB,wBAAwB,4BAA4B,iCAAiC,WAAW,GAAG;AACvH,uBAAa,MAAiC;AAAA,QAChD;AACA,YAAI,wBAAwB,QAAmC,aAAa,MAAM,uBAAuB,sBAAsB,KAAK,wBAAwB,aAAa,KAAK,kBAAkB,CAAC,uBAAuB;AACtN,uBAAa,iBAAiB,MAAiC,sBAAsB;AAAA,QACvF;AAAA,MACF;AAGA,YAAM,+BAA+B,KAAK,wBAAwB,KAAK,iBAAiB;AACxF,UAAI,eAAe,2CAA2C,QAAQ,8BAA8B,EAAE;AAEtG,UAAI,MAAM,QAAQ,KAAK,aAAa,GAAG;AACrC,mBAAW,UAAU,KAAK,eAAe;AACvC,yBAAe,aAAa;AAAA,YAAQ,KAAK,wBAAwB,MAAM;AAAA,YAAG;AAAA;AAAA,UAAoC;AAAA,QAChH;AAAA,MACF,OAAO;AACL,uBAAe,aAAa,QAAQ,KAAK,wBAAwB,KAAK,aAAa,GAAG,EAAE;AAAA,MAC1F;AACA,YAAM,oBAAoB,IAAI,OAAO,MAAM,eAAe,GAAG;AAC7D,UAAI,WAAW,MAAM,iBAAiB,GAAG;AACvC,qBAAa,WAAW,UAAU,GAAG,WAAW,SAAS,CAAC;AAAA,MAC5D;AACA,mBAAa,KAAK,oBAAoB,YAAY,WAAW,KAAK,aAAa;AAC/E,YAAM,YAAY,WAAW,QAAQ,IAAI,OAAO,8BAA8B,GAAG,GAAG,EAAE;AACtF,eAAS,KAAK,sBAAsB,WAAW,KAAK,mBAAmB,KAAK,eAAe,SAAS;AACpG,YAAM,aAAa,OAAO;AAAA,QAAQ;AAAA;AAAA,MAA8B,IAAI,WAAW;AAAA,QAAQ;AAAA;AAAA,MAA8B;AACrH,YAAM,YAAY,OAAO,SAAS,WAAW;AAC7C,UAAI,OAAO,WAAW,CAAC,MAAM,KAAK,qBAAqB,KAAK,UAAU,YAAY;AAChF,mBAAW,WAAW;AAAA,MACxB,WAAW,YAAY,KAAK,OAAO,QAAQ,MAAM,KAAK,mBAAmB;AACvE,yBAAiB;AACjB,YAAI,SAAS;AACb,WAAG;AACD,eAAK,OAAO,IAAI,WAAW,MAAM;AACjC;AAAA,QACF,SAAS,SAAS;AAAA,MACpB,WAAW,OAAO,WAAW,CAAC,MAAM,KAAK,iBAAiB,cAAc,MAAM,cAAc,MAAM,OAAO,QAAQ,MAAM,KAAK,mBAAmB;AAC7I,aAAK,OAAO,MAAM;AAClB,aAAK,OAAO,IAAI,WAAW,CAAC;AAAA,MAC9B,WAAW,eAAe,KAAK,WAAW,KAAK,EAAE,OAAO;AAAA,QAAQ;AAAA;AAAA,MAA8B,KAAK,YAAY,WAAW,MAAM,EAAE,OAAO;AAAA,QAAQ;AAAA;AAAA,MAA4B,KAAK,YAAY,WAAW,MAAM,aAAa,GAAG;AAC7N,aAAK,OAAO,MAAM;AAClB,yBAAiB;AACjB,gBAAQ;AACR,oBAAY;AACZ,aAAK,OAAO,IAAI,QAAQ;AAAA,MAC1B,OAAO;AACL,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,cAAc,WAAW,CAAC,GAAG,IAAI,WAAW,QAAQ,KAAK,cAAc,WAAW,CAAC,KAAK,IAAsC;AAC5I,YAAI,WAAW,eAAe,QAAQ;AACpC;AAAA,QACF;AACA,cAAM,sBAAsB,OAAwC,KAAK;AACzE,YAAI,KAAK;AAAA,UAAiB;AAAA,UAAa,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,KAAK,eAAe,SAAS,CAAC,MAAM,KAA0C;AACjL,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,eAAe,SAAS,CAAC,MAAM,OAAwC,SAAS,KAAK;AAAA,UAAiB;AAAA,UAAa,eAAe,SAAS,CAAC,KAAK;AAAA;AAAA,QAAoC,GAAG;AACjM,oBAAU;AACV,oBAAU;AACV,kBAAQ;AAAA,QACV,WAAW,KAAK;AAAA,UAAiB;AAAA,UAAa,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,KAAK,eAAe,SAAS,CAAC,MAAM,OAAwC,CAAC,qBAAqB;AAC5M,oBAAU;AACV,kBAAQ;AAAA,QACV,WAAW,eAAe,SAAS,CAAC,MAAM,OAA4C,KAAK;AAAA,UAAiB;AAAA,UAAa,eAAe,SAAS,CAAC,KAAK;AAAA;AAAA,QAAoC,GAAG;AAC5L,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK;AAAA,UAAiB;AAAA,UAAa,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,GAAG;AAC7G,cAAI,eAAe,MAAM,MAAM,KAAgC;AAC7D,gBAAI,KAAK,MAAM,OAAO,WAAW,IAAI,IAAI,OAAO,WAAW,IAAI,GAAG;AAChE,yBAAW,CAAC,KAAK,mBAAmB,WAAW,IAAI;AACnD,wBAAU;AACV,mBAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AACzD;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,KAA+B;AAC5D,gBAAI,KAAK,MAAM,OAAO,WAAW,KAAK,OAAO,MAAM,IAAI,KAAK,WAAW,OAAO,OAAO,WAAW,IAAI,KAAK,WAAW,MAAM,SAAS,GAAG,MAAM,EAAE,WAAW,KAAK,OAAO,WAAW,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,KAAK,WAAW,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI,WAAW,OAAO,OAAO,WAAW,IAAI,MAAM,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,QAAQ,OAAO,WAAW,IAAI,KAAK,SAAS,IAAI;AACjiB,yBAAW,WAAW;AACtB,wBAAU;AACV;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,OAAmC,eAAe,MAAM,MAAM,KAAiC;AAC5H,gBAAI,OAAO,WAAW,IAAI,GAAG;AAC3B,yBAAW,CAAC,KAAK,mBAAmB,WAAW,IAAI;AACnD,wBAAU;AACV,mBAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AACzD;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,gBAAM,mBAAmB,WAAW,MAAM;AAC1C,gBAAM,0BAA0B,WAAW,SAAS,CAAC;AACrD,gBAAM,0BAA0B,WAAW,SAAS,CAAC;AACrD,gBAAM,2BAA2B,WAAW,SAAS,CAAC;AACtD,gBAAM,2BAA2B,WAAW,SAAS,CAAC;AACtD,gBAAM,oCAAoC,WAAW,MAAM,SAAS,GAAG,SAAS,CAAC;AACjF,gBAAM,iCAAiC,WAAW,MAAM,SAAS,GAAG,SAAS,CAAC;AAC9E,gBAAM,+BAA+B,WAAW,MAAM,QAAQ,SAAS,CAAC;AACxE,gBAAM,gCAAgC,WAAW,MAAM,SAAS,GAAG,MAAM;AACzE,cAAI,eAAe,MAAM,MAAM,KAA8B;AAC3D,kBAAM,qBAAqB,eAAe,MAAM,GAAG,CAAC,MAAM;AAC1D,kBAAM,sBAAsB,eAAe,MAAM,GAAG,CAAC,MAAM,QAAoC,KAAK,kBAAkB,SAAS,wBAAwB;AACvJ,gBAAI,OAAO,WAAW,IAAI,KAAK,KAAK,oBAAoB,CAAC,uBAAuB,OAAO,4BAA4B,IAAI,aAAa,OAAO,8BAA8B,IAAI,aAAa,KAAK,kBAAkB,SAAS,uBAAuB,OAAO,sBAAsB,OAAO,8BAA8B,IAAI,aAAa,CAAC,KAAK,kBAAkB,SAAS,gBAAgB,KAAK,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,KAAK,kBAAkB,SAAS,gBAAgB,IAAI,OAAO,4BAA4B,IAAI,aAAa,KAAK,kBAAkB,SAAS,uBAAuB,IAAI;AACvlB,yBAAW,CAAC,KAAK,mBAAmB,WAAW,IAAI;AACnD,wBAAU;AACV,mBAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AACzD;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,KAAgC;AAC7D,kBAAM,cAAc;AAEpB,kBAAM,cAAc,WAAW,MAAM,OAAO,WAAW,IAAI,KAAK,OAAO,4BAA4B,IAAI,eAAe,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,CAAC;AAEnL,kBAAM,eAAe,eAAe,MAAM,SAAS,GAAG,SAAS,CAAC;AAChE,kBAAM,iBAAiB,kCAAkC,SAAS,YAAY,KAAK,eAAe,SAAS,IAAI,MAAM,KAAK,kBAAkB,SAAS,wBAAwB,KAAK,OAAO,8BAA8B,IAAI,eAAe,CAAC,KAAK,kBAAkB,SAAS,gBAAgB,KAAK,KAAK,kBAAkB,SAAS,gBAAgB;AAEhV,kBAAM,iBAAiB,OAAO,iCAAiC,KAAK,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,KAAK,kBAAkB,SAAS,wBAAwB,MAAM,OAAO,4BAA4B,IAAI,eAAe,KAAK,kBAAkB,SAAS,uBAAuB;AAEnU,kBAAM,oBAAoB,OAAO,4BAA4B,IAAI,eAAe,WAAW,KAAK,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,WAAW;AAEvK,kBAAM,iBAAiB,OAAO,iCAAiC,IAAI,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,CAAC,KAAK,kBAAkB,SAAS,6BAA6B,KAAK,OAAO,6BAA6B,IAAI,eAAe,eAAe,SAAS,IAAI;AAE7S,kBAAM,iBAAiB,OAAO,iCAAiC,KAAK,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,CAAC,KAAK,kBAAkB,SAAS,wBAAwB,KAAK,OAAO,8BAA8B,IAAI;AAC/P,gBAAI,OAAO,WAAW,IAAI,KAAK,KAAK,oBAAoB,eAAe,kBAAkB,kBAAkB,kBAAkB,kBAAkB,qBAAqB,CAAC,KAAK,kBAAkB;AAC1L,yBAAW,CAAC,KAAK,mBAAmB,WAAW,IAAI;AACnD,wBAAU;AACV,mBAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AACzD;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,oBAAU;AACV;AAAA,QACF,WAAW,KAAK,kBAAkB,SAAS,WAAW,KAAK,eAAe,MAAM,MAAM,aAAa;AACjG,oBAAU;AACV;AAAA,QACF,WAAW,KAAK,kBAAkB;AAAA,UAAQ,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,MAAM,IAAI;AAChH,oBAAU,eAAe,MAAM;AAC/B;AACA,eAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AACzD;AAAA,QACF,WAAW,eAAe,MAAM,MAAM,OAAwC,KAAK,eAAe;AAChG,eAAK,WAAW,gBAAgB,QAAQ,WAAW,MAAM;AAAA,QAC3D,WAAW,KAAK;AAAA,UAAS,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,KAAK,KAAK;AAAA,UAAS,eAAe,MAAM,KAAK;AAAA;AAAA,QAAoC,GAAG,UAAU;AACnL,cAAI,CAAC,CAAC,WAAW,MAAM,KAAK,mBAAmB,qBAAqB,mBAAmB,oBAAoB,mBAAmB,wBAAwB,CAAC,eAAe,MAAM,UAAU,KAAK,CAAC,KAAK;AAAA,YAAS,eAAe,MAAM,KAAK;AAAA;AAAA,UAAoC,GAAG,UAAU;AACnR,sBAAU,WAAW,MAAM;AAAA,UAC7B;AACA,cAAI,eAAe;AAAA,YAAS;AAAA;AAAA,UAA2E,KAAK,eAAe;AAAA,YAAS;AAAA;AAAA,UAA2E,GAAG;AAChN;AAAA,UACF;AACA;AACA;AAAA,QACF,WAAW,KAAK,eAAe,SAAS,CAAC,MAAM,OAAwC,KAAK;AAAA,UAAiB,KAAK,eAAe,SAAS,CAAC,KAAK;AAAA;AAAA,QAAoC,KAAK,KAAK,iBAAiB,WAAW,MAAM,KAAK,eAAe,SAAS,CAAC,KAAK,OAAO;AACxQ,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK,eAAe,SAAS,CAAC,MAAM,OAA4C,KAAK;AAAA,UAAiB,KAAK,eAAe,SAAS,CAAC,KAAK;AAAA;AAAA,QAAoC,KAAK,KAAK,iBAAiB,WAAW,MAAM,KAAK,eAAe,SAAS,CAAC,KAAK,OAAO;AAC5Q,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK,iBAAiB,KAAK,kBAAkB,QAAQ,WAAW,IAAI,KAAK,gBAAgB,KAAK,wBAAwB,KAAK,qBAAqB,WAAW,GAAG;AACvK,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,SAAS,MAAM,eAAe,UAAU,KAAK,kBAAkB;AAAA,MAAQ,eAAe,eAAe,SAAS,CAAC,KAAK;AAAA;AAAA,IAAoC,MAAM,IAAI;AAC3K,gBAAU,eAAe,eAAe,SAAS,CAAC;AAAA,IACpD;AACA,QAAI,cAAc,WAAW;AAC7B,WAAO,KAAK,OAAO,IAAI,WAAW,GAAG;AACnC;AACA;AAAA,IACF;AACA,QAAI,cAAc,cAAc,CAAC,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,IAAI,SAAS,KAAK,OAAO,IAAI,QAAQ,IAAI,QAAQ;AACtJ,QAAI,UAAU;AACZ;AAAA,IACF;AACA,OAAG,aAAa,cAAc;AAC9B,QAAI,QAAQ,GAAG;AACb,WAAK,OAAO,MAAM;AAAA,IACpB;AACA,QAAI,cAAc;AAClB,QAAI,YAAY;AACd,oBAAc,WAAW,MAAM,UAAQ,KAAK,kBAAkB,SAAS,IAAI,CAAC;AAAA,IAC9E;AACA,QAAI,MAAM,GAAG,KAAK,MAAM,GAAG,cAAc,KAAuC,MAAM,GAAG,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAC9H,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,CAAC,KAAK,wBAAwB,GAAG,KAAK,MAAM,GAAG,MAAM,KAAK,GAAG,MAAM;AAAA,IAC3E;AACA,UAAM,oCAAoC,WAAW,WAAW,KAAK,KAAK,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,eAAe,eAAe,CAAC;AAC1J,QAAI,CAAC,KAAK,iBAAiB,YAAY,eAAe,CAAC,CAAC,KAAK,mCAAmC;AAC9F,aAAO;AAAA,IACT;AACA,QAAI,OAAO;AAAA,MAAS;AAAA;AAAA,IAA8B,KAAK,KAAK,UAAU,KAAK,sBAAsB;AAC/F,UAAI,cAAc,WAAW,KAAgC;AAC3D,eAAO;AAAA,MACT;AACA,YAAM,GAAG,GAA8B,GAAG,KAAK,MAAM,GAAG,OAAO;AAAA,QAAM;AAAA;AAAA,MAA8B,EAAE;AAAA,QAAK;AAAA;AAAA,MAAoC,CAAC,GAAG,KAAK,MAAM;AAAA,IAC/J;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,aAAa;AAChC,QAAI,MAAM,QAAQ,KAAK,qBAAqB,GAAG;AAC7C,aAAO,KAAK,sBAAsB,KAAK,SAAO,QAAQ,WAAW;AAAA,IACnE;AACA,WAAO,KAAK,iBAAiB,WAAW;AAAA,EAC1C;AAAA,EACA,iBAAiB,aAAa;AAC5B,WAAO,KAAK,kBAAkB,KAAK,SAAO,QAAQ,WAAW;AAAA,EAC/D;AAAA,EACA,iBAAiB,aAAa,YAAY;AACxC,SAAK,WAAW,KAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAC/D,YAAQ,KAAK,SAAS,UAAU,GAAG,WAAW,KAAK,SAAS,UAAU,GAAG,QAAQ,KAAK,WAAW,MAAM;AAAA,EACzG;AAAA,EACA,gBAAgB,KAAK;AACnB,WAAO,IAAI;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,OAAO,CAAC,GAAG,QAAQ;AACxE,YAAM,kBAAkB,OAAO,KAAK,kBAAkB,WAAW,MAAM,KAAK;AAAA;AAAA,QAE5E,KAAK,cAAc,SAAS,CAAC;AAAA;AAC7B,aAAO,EAAE,MAAM,QAAQ,KAAK,MAAM,KAAK,qBAAqB,mBAAmB,MAAM,OAAkC,QAAQ,KAAK,KAAK;AAAA,IAC3I,CAAC,EAAE;AAAA,MAAK;AAAA;AAAA,IAAoC;AAAA,EAC9C;AAAA,EACA,wBAAwB,MAAM;AAI5B,QAAI,MAAM;AACR,YAAM,gBAAgB;AACtB,aAAO,SAAS,MAAM,QAAQ,cAAc,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,IACjF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,gBAAgB,QAAQ,aAAa;AAC9C,UAAM,YAAY,QAAQ,KAAK,eAAe,MAAM,GAAG,MAAM,CAAC,IAAI,cAAc;AAChF,SAAK,OAAO,IAAI,YAAY,KAAK,OAAO,UAAU,CAAC;AAAA,EACrD;AAAA,EACA,mBAAmB,OAAO,eAAe,eAAe;AACtD,WAAO,MAAM,QAAQ,aAAa,IAAI,cAAc,OAAO,OAAK,MAAM,aAAa,EAAE,SAAS,KAAK,IAAI,UAAU;AAAA,EACnH;AAAA,EACA,SAAS,UAAU;AACjB,WAAO,EAAE,SAAS,WAAW,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,UAAU;AACjE,UAAI,SAAS,WAAW,QAAQ,GAAG;AACjC,eAAO,UAAU,MAAwC,OAAO,KAAK,IAAI;AAAA,MAC3E;AACA,aAAO,UAAU,MAAwC,OAAO,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI;AAAA,IAC3F,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,OAAO;AACvB,QAAI,UAAU,OAAkC,KAAK,sBAAsB;AACzE,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,kBAAkB,WAAW,MAAM,QAAQ,KAAK,aAAa,IAAI,MAAM;AAAA,MAAQ;AAAA;AAAA,IAA4B;AAC5I,UAAM,eAAe,KAAK,wBAAwB,MAAM;AAAA,MAAS;AAAA;AAAA,IAA8B,IAAI,MAAM;AACzG,QAAI,iBAAiB,IAAI;AACvB,YAAM,cAAc,SAAS,eAAe,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,OAAO,EAAE;AACpF,aAAO,MAAM,WAAW,IAAI,KAAuC,GAAG,YAAY,GAAG,WAAW;AAAA,IAClG,OAAO;AACL,YAAM,cAAc,SAAS,MAAM,QAAQ,KAAK,EAAE,EAAE,UAAU,GAAG,YAAY,GAAG,EAAE;AAClF,YAAM,cAAc,MAAM,UAAU,eAAe,CAAC;AACpD,YAAM,gBAAgB,MAAM,WAAW,IAAI,KAAK,YAAY,SAAS;AACrE,YAAM,UAAU,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB;AAC9E,aAAO,kBAAkB,KAAuC,KAAuC,GAAG,YAAY,GAAG,aAAa,GAAG,OAAO,GAAG,WAAW;AAAA,IAChK;AAAA,EACF;AAAA,EACA,4BAA4B,aAAa;AACvC,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,OAAO,YAAY,CAAC;AAC1B,UAAI,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAM,wBAAuB,sBAAsB;AAAA,EACjD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,SAAS;AAKd,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,uBAAuB,CAAC;AAC7B,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAElB,SAAK,WAAW,OAAK;AAAA,IAAC;AACtB,SAAK,cAAc,OAAO,YAAY;AAAA,MACpC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,UAAU,OAAO,eAAe;AACrC,SAAK,YAAY,OAAO,WAAW;AAAA,MACjC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,UAAU,YAAY,gBAAgB,WAAW,GAAG,aAAa,OAAO,aAAa,OAErF,KAAK,MAAM;AAAA,EAAC,GAAG;AACb,QAAI,CAAC,gBAAgB;AACnB,aAAO,eAAe,KAAK,cAAc,KAAK,cAAc;AAAA,IAC9D;AACA,SAAK,cAAc,KAAK,gBAAgB,KAAK,gBAAgB,IAAI;AACjE,QAAI,KAAK,mBAAmB,QAAgC,KAAK,eAAe;AAC9E,WAAK,cAAc,KAAK;AAAA,QAAgB,cAAc;AAAA;AAAA,MAA6B;AAAA,IACrF;AACA,QAAI,KAAK,mBAAmB,cAA4C,KAAK,eAAe;AAC1F,WAAK,cAAc,KAAK;AAAA,QAAgB,cAAc;AAAA;AAAA,MAA6B;AAAA,IACrF;AACA,QAAI,CAAC,cAAc,KAAK,eAAe;AACrC,WAAK,kBAAkB,KAAK,MAAM;AAClC,aAAO,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AAAA,IACxD;AACA,UAAM,YAAY,CAAC,CAAC,cAAc,OAAO,KAAK,aAAa,WAAW,WAAW,KAAK,QAAQ,KAAK,KAAuC;AAC1I,QAAI,gBAAgB;AACpB,QAAI,KAAK,gBAAgB,UAAa,CAAC,KAAK,cAAc;AACxD,UAAI,eAAe,cAAc,WAAW,WAAW,IAAI,WAAW;AAAA,QAAM;AAAA;AAAA,MAAoC,IAAI,KAAK,YAAY;AAAA,QAAM;AAAA;AAAA,MAAoC;AAE/K,UAAI,OAAO,KAAK,aAAa,YAAY,OAAO,KAAK,WAAW,UAAU;AACxE,aAAK,WAAW,OAAO,KAAK,QAAQ;AACpC,aAAK,SAAS,OAAO,KAAK,MAAM;AAAA,MAClC,OAAO;AACL,uBAAe,MAAwC,aAAa,SAAS,OAAO,KAAK,aAAa,YAAY,OAAO,KAAK,WAAW,WAAW,WAAW,SAAS,aAAa,SAAS,aAAa,OAAO,KAAK,UAAU,GAAG,SAAS,IAAI,WAAW,SAAS,aAAa,SAAS,aAAa,SAAS,WAAW,WAAW,IAAI,aAAa,aAAa,OAAO,KAAK,WAAW,GAAG,CAAC,IAAI,aAAa,OAAO,WAAW,SAAS,GAAG,CAAC,IAAI,aAAa,OAAO,KAAK,UAAU,KAAK,SAAS,KAAK,QAAQ,IAAI,OAAO,OAAO,eAAe,CAAC;AAAA,MACthB;AACA,UAAI,KAAK,eAAe;AACtB,YAAI,CAAC,KAAK,aAAa;AACrB,uBAAa,KAAK,WAAW,UAAU;AAAA,QACzC;AAAA,MACF;AAEA,sBAAgB,KAAK,YAAY,UAAU,aAAa,UAAU,WAAW,SAAS,KAAK,kBAAkB,aAAa;AAAA,QAAK;AAAA;AAAA,MAAoC,CAAC,IAAI;AAAA,IAC1K;AACA,QAAI,eAAe,KAAK,eAAe,CAAC,KAAK,cAAc;AACzD,sBAAgB;AAAA,IAClB;AACA,QAAI,cAAc,KAAK,kBAAkB;AAAA,MAAQ,KAAK,eAAe,QAAQ,KAAK;AAAA;AAAA,IAAoC,MAAM,MAAM,KAAK,iBAAiB,CAAC,KAAK,QAAQ;AACpK,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,2BAA2B,UAAU;AAC5C,UAAI,KAAK,kBAAkB,SAAS,KAAK,YAAY,MAAM,UAAU,WAAW,CAAC,CAAC,GAAG;AACnF,mBAAW,WAAW;AAAA,MACxB,WAAW,eAAe,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,MAAkC;AAChG,mBAAW,WAAW;AAAA,MACxB;AACA,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,KAAK,iBAAiB,KAAK,qBAAqB,WAAW,KAAK,CAAC,KAAK,kBAAkB;AAC1F,mBAAa,KAAK,WAAW,UAAU;AAAA,IACzC;AACA,QAAI,KAAK,aAAa;AACpB,sBAAgB;AAAA,IAClB,OAAO;AACL,sBAAgB,QAAQ,aAAa,KAAK,cAAc,SAAS,gBAAgB;AAAA,IACnF;AACA,QAAI,KAAK,iBAAiB,KAAK,0BAA0B,KAAK,eAAe,CAAC,cAAc,CAAC,KAAK,cAAc;AAC9G,YAAM,QAAQ,KAAK,wBAAwB,KAAK,WAAW,KAAK,WAAW,IAAI,KAAK;AACpF,WAAK,kBAAkB,KAAK;AAC5B,aAAO,KAAK,cAAc,KAAK,cAAc,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AAAA,IAC9F;AACA,UAAM,SAAS,MAAM,UAAU,eAAe,gBAAgB,UAAU,YAAY,YAAY,EAAE;AAClG,SAAK,cAAc,KAAK,eAAe,MAAM;AAG7C,QAAI,KAAK,sBAAsB,OAAgC,KAAK,kBAAkB,KAA8B;AAClH,WAAK,gBAAgB;AAAA,IACvB;AAEA,QAAI,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,KAAK,KAAK,0BAA0B,MAAM;AACrH,WAAK,oBAAoB,KAAK,kBAAkB;AAAA,QAAO,UAAQ,CAAC,KAAK,mBAAmB,MAAM,KAAK,eAAe,KAAK,iBAAiB;AAAA;AAAA,MACxI;AAAA,IACF;AACA,QAAI,UAAU,WAAW,IAAI;AAC3B,WAAK,iBAAiB,KAAK;AAC3B,WAAK,gBAAgB;AACrB,WAAK,aAAa,KAAK,mBAAmB,KAAK,iBAAiB,KAAK,eAAe,KAAK,mBAAmB,KAAK,iBAAiB;AAAA,IACpI;AACA,SAAK,aAAa,KAAK,gBAAgB,KAAK,sBAAsB,sBAAsB,MAAM,KAAK,kBAAkB,MAAM,CAAC,IAAI,KAAK,kBAAkB,MAAM,IAAI;AACjK,QAAI,CAAC,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,aAAa;AACjE,UAAI,KAAK,aAAa;AACpB,YAAI,YAAY;AACd,iBAAO,KAAK,UAAU,QAAQ,KAAK,cAAc;AAAA,QACnD;AACA,eAAO,GAAG,KAAK,UAAU,QAAQ,KAAK,cAAc,CAAC,GAAG,KAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AAAA,MAC/F;AACA,aAAO;AAAA,IACT;AACA,UAAM,SAAS,OAAO;AACtB,UAAM,YAAY,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AACjE,QAAI,KAAK,eAAe;AAAA,MAAS;AAAA;AAAA,IAA8B,GAAG;AAChE,YAAM,oBAAoB,KAAK,qBAAqB,MAAM;AAC1D,aAAO,GAAG,MAAM,GAAG,UAAU,MAAM,SAAS,iBAAiB,CAAC;AAAA,IAChE,WAAW,KAAK,mBAAmB,QAAgC,KAAK,mBAAmB,YAA0C;AACnI,aAAO,GAAG,MAAM,GAAG,SAAS;AAAA,IAC9B;AACA,WAAO,GAAG,MAAM,GAAG,UAAU,MAAM,MAAM,CAAC;AAAA,EAC5C;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ;AACd,QAAI,QAAQ,MAAM,KAAK,KAAK;AAC5B,QAAI,oBAAoB;AACxB,WAAO,SAAS,MAAM;AACpB,2BAAqB;AACrB,cAAQ,MAAM,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,UAAU,YAAY,YAExC,KAAK,MAAM;AAAA,EAAC,GAAG;AACb,UAAM,cAAc,KAAK,aAAa;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,gBAAY,QAAQ,KAAK,UAAU,YAAY,OAAO,KAAK,gBAAgB,UAAU,YAAY,YAAY,EAAE;AAC/G,QAAI,gBAAgB,KAAK,kBAAkB,GAAG;AAC5C;AAAA,IACF;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,UAAU,YAAY,gBAAgB;AACpC,WAAO,WAAW;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,IAAI,CAAC,MAAM,UAAU;AACjF,UAAI,KAAK,YAAY,KAAK;AAAA,QAAS,eAAe,KAAK,KAAK;AAAA;AAAA,MAAoC,KAAK,KAAK;AAAA,QAAS,eAAe,KAAK,KAAK;AAAA;AAAA,MAAoC,GAAG,QAAQ;AACzL,eAAO,KAAK;AAAA,UAAS,eAAe,KAAK,KAAK;AAAA;AAAA,QAAoC,GAAG;AAAA,MACvF;AACA,aAAO;AAAA,IACT,CAAC,EAAE;AAAA,MAAK;AAAA;AAAA,IAAoC;AAAA,EAC9C;AAAA;AAAA,EAEA,eAAe,KAAK;AAClB,UAAM,UAAU,IAAI;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,OAAO,CAAC,QAAQ,MAAM;AACpF,YAAM,WAAW,KAAK,eAAe,CAAC,KAAK;AAC3C,aAAO,KAAK,iBAAiB,QAAQ,QAAQ,KAAK,KAAK,kBAAkB,SAAS,QAAQ,KAAK,WAAW;AAAA,IAC5G,CAAC;AACD,QAAI,QAAQ;AAAA,MAAK;AAAA;AAAA,IAAoC,MAAM,KAAK;AAC9D,aAAO,QAAQ;AAAA,QAAK;AAAA;AAAA,MAAoC;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,YAAY;AAC5B,QAAI,kBAAkB;AACtB,UAAM,gBAAgB,cAAc,WAAW;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,IAAI,CAAC,YAAY,UAAU;AACpH,UAAI,KAAK,kBAAkB;AAAA,QAAS,WAAW,QAAQ,CAAC,KAAK;AAAA;AAAA,MAAoC,KAAK,WAAW,QAAQ,CAAC,MAAM,KAAK,eAAe,QAAQ,CAAC,GAAG;AAC9J,0BAAkB;AAClB,eAAO,WAAW,QAAQ,CAAC;AAAA,MAC7B;AACA,UAAI,gBAAgB,QAAQ;AAC1B,cAAM,gBAAgB;AACtB,0BAAkB;AAClB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,KAAK,CAAC;AACP,WAAO,cAAc;AAAA,MAAK;AAAA;AAAA,IAAoC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AACpB,QAAI,CAAC,SAAS,UAAU,KAAK,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,MAAM,KAAK,YAAY,CAAC,KAAK,0BAA0B,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,KAAK,KAAK,eAAe,SAAS,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI;AACvS,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,WAAO,OAAO,KAAK,EAAE,eAAe,YAAY;AAAA,MAC9C,aAAa;AAAA,MACb,uBAAuB;AAAA,IACzB,CAAC,EAAE;AAAA,MAAQ,IAAI,GAA8B;AAAA,MAAK;AAAA;AAAA,IAA8B;AAAA,EAClF;AAAA,EACA,gBAAgB,UAAU;AACxB,QAAI,KAAK,iBAAiB,CAAC,CAAC,KAAK,qBAAqB;AACpD,UAAI,KAAK,eAAe,WAAW,KAAK,oBAAoB,QAAQ;AAClE,cAAM,IAAI,MAAM,oDAAoD;AAAA,MACtE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF,WAAW,KAAK,eAAe;AAC7B,UAAI,UAAU;AACZ,YAAI,KAAK,mBAAmB,MAA8B;AACxD,iBAAO,KAAK,YAAY,QAAQ;AAAA,QAClC;AACA,YAAI,KAAK,mBAAmB,YAA0C;AACpE,iBAAO,KAAK,iBAAiB,QAAQ;AAAA,QACvC;AAAA,MACF;AACA,UAAI,KAAK,qBAAqB,WAAW,KAAK,eAAe,QAAQ;AACnE,eAAO,KAAK;AAAA,MACd;AACA,aAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,oBAAoB;AAAA,IACrE;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,UAAM,cAAc,KAAK,aAAa;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,KAAK,OAAO,SAAS,KAAK,eAAe,SAAS,KAAK,OAAO,WAAW,YAAY,MAAM;AAAA,MAAQ,KAAK;AAAA,MAAsB;AAAA;AAAA,IAAoC,EAAE,QAAQ;AACtM,WAAK,sBAAsB;AAAA,QAAC;AAAA,QAAS;AAAA;AAAA,MAAoC;AACzE,WAAK,UAAU,IAAI,KAAK,cAAc;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,oBAAoB,CAAC,MAAM,KAAK,GAAG;AACrC,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,aAAa;AACxC;AAAA,IACF;AAEA,YAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,WAAW,YAAY,KAAK,aAAa,eAAe,MAAM,KAAK,CAAC;AAAA,EACxG;AAAA,EACA,2BAA2B,MAAM;AAC/B,UAAM,QAAQ,KAAK;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,OAAO,UAAQ,KAAK,qBAAqB,IAAI,CAAC;AAC7G,WAAO,MAAM;AAAA,EACf;AAAA,EACA,WAAW,YAAY;AACrB,WAAO,KAAK,YAAY,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,GAAG,KAAK,kBAAkB,OAAO,GAAG,EAAE,OAAO,KAAK,oBAAoB,CAAC;AAAA,EAClJ;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,aAAa,KAA+B;AAC9C,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAC5H;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC,KAAK;AAC7B,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM,KAAK,GAAG;AACtB,YAAI,KAAK,KAAK;AAAA,MAChB;AAAA,IACF;AACA,QAAI,IAAI,UAAU,GAAG;AACnB,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAC/F;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAClE;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,KAAK;AAAA,IACd;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,IAAI;AACtC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,MAAM,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB;AAClV,UAAM,OAAO,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB;AAC7a,QAAI,aAAa,KAA+B;AAC9C,aAAO;AAAA,IACT;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC,KAAK;AAC7B,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM,KAAK,GAAG;AACtB,YAAI,KAAK,KAAK;AAAA,MAChB;AAAA,IACF;AACA,QAAI,IAAI,UAAU,GAAG;AACnB,aAAO,IAAI,MAAM,IAAI,QAAQ,IAAI,MAAM;AAAA,IACzC;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,WAAW,IAAI;AACrB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,WAAW,IAAI;AACrB,UAAI,SAAS,WAAW,IAAI;AAC1B,eAAO,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,MACnC;AACA,aAAO,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,IACnC;AACA,QAAI,IAAI,SAAS,MAAM,IAAI,UAAU,IAAI;AACvC,aAAO,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,MAAM;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,WAAW,KAAK,UAAU;AAC1C,UAAM,eAAe,UAAU,eAAe;AAC9C,QAAI,CAAC,cAAc,eAAe;AAChC,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,YAAY;AAC5B,QAAI,KAAK,gBAAgB,CAAC,KAAK,uBAAuB,KAAK,aAAa;AACtE,WAAK,uBAAuB,KAAK,cAAc,KAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;AAC/K,WAAK,cAAc;AACnB;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,qBAAqB,GAAG;AAC7C,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,YAAY,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,GAAG,KAAK,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAAA,IAC5K,WAAW,KAAK,yBAAyB,CAAC,KAAK,yBAAyB,KAAK,WAAW,YAAY;AAClG,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IAC9H,OAAO;AACL,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,UAAU,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,iBAAiB,UAAU,IAAsC;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,MAAM,KAAK,YAAY,CAAC,KAAK,wBAAwB;AAChI,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,EAAE,SAAS,MAAM,KAAK,eAAe,SAAS,IAAI;AAChE,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,UAAM,MAAM,OAAO,KAAK;AACxB,QAAI,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAA0C,KAAK,OAAO,MAAM,GAAG,GAAG;AACnG,YAAM,MAAM,OAAO,KAAK,EAAE,QAAQ,KAAK,GAAG;AAC1C,aAAO,OAAO,GAAG;AAAA,IACnB;AACA,WAAO,OAAO,MAAM,GAAG,IAAI,QAAQ;AAAA,EACrC;AAAA,EACA,YAAY,OAAO,4BAA4B;AAC7C,QAAI,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAAsC,KAAK,MAAM;AAAA,MAAS;AAAA;AAAA,IAA4B,GAAG;AAC1H,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM;AAAA,MAAQ,KAAK,iBAAiB,0BAA0B;AAAA,MAAG;AAAA;AAAA,IAAoC,IAAI;AAAA,EAC1H;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM;AAAA,MAAQ,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAoC,IAAI;AAAA,EACpF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM;AAAA,MAAQ,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAoC,IAAI;AAAA,EACpF;AAAA,EACA,wBAAwB,QAAQ;AAC9B,QAAI,oBAAoB,MAAM,QAAQ,KAAK,qBAAqB,IAAI,KAAK,kBAAkB,OAAO,OAAK;AACrG,aAAO,KAAK,sBAAsB,SAAS,CAAC;AAAA,IAC9C,CAAC,IAAI,KAAK;AACV,QAAI,CAAC,KAAK,2BAA2B,KAAK,sBAAsB,KAAK,OAAO;AAAA,MAAS;AAAA;AAAA,IAAoC,KAAK,KAAK,eAAe;AAAA,MAAS;AAAA;AAAA,IAAoC,GAAG;AAChM,0BAAoB,kBAAkB;AAAA,QAAO,UAAQ,SAAS;AAAA;AAAA,MAAoC;AAAA,IACpG;AACA,WAAO,KAAK,YAAY,QAAQ,iBAAiB;AAAA,EACnD;AAAA,EACA,iBAAiB,4BAA4B;AAC3C,WAAO,IAAI,OAAO,2BAA2B,IAAI,UAAQ,KAAK,IAAI,EAAE,EAAE,KAAK,GAAG,GAAG,IAAI;AAAA,EACvF;AAAA,EACA,2BAA2B,OAAO;AAChC,UAAM,UAAU,MAAM,QAAQ,KAAK,aAAa,IAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa;AAC5F,WAAO,MAAM;AAAA,MAAQ,KAAK,iBAAiB,OAAO;AAAA,MAAG;AAAA;AAAA,IAA4B;AAAA,EACnF;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,WAAW,IAAsC;AACnD,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe;AAAA,MAAW;AAAA;AAAA,IAAsC,KAAK,KAAK,kBAAkB,KAAgC;AACnI,eAAS,OAAO;AAAA,QAAQ;AAAA,QAAgC;AAAA;AAAA,MAA4B;AAAA,IACtF;AACA,UAAM,qBAAqB,KAAK,4BAA4B,KAAK,cAAc;AAC/E,UAAM,iBAAiB,KAAK,2BAA2B,KAAK,wBAAwB,MAAM,CAAC;AAC3F,QAAI,CAAC,KAAK,eAAe;AACvB,aAAO;AAAA,IACT;AACA,QAAI,oBAAoB;AACtB,UAAI,WAAW,KAAK,eAAe;AACjC,eAAO;AAAA,MACT;AACA,UAAI,KAAK,eAAe,SAAS,IAAI;AACnC,eAAO,OAAO,cAAc;AAAA,MAC9B;AACA,aAAO,KAAK,gBAAgB,KAAK,gBAAgB,cAAc;AAAA,IACjE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,eAAW,OAAO,KAAK,UAAU;AAE/B,UAAI,KAAK,SAAS,GAAG,KAAK,KAAK,SAAS,GAAG,GAAG,eAAe,SAAS,GAAG;AACvE,cAAM,gBAAgB,KAAK,SAAS,GAAG,GAAG,QAAQ,SAAS;AAC3D,cAAM,UAAU,KAAK,SAAS,GAAG,GAAG;AACpC,YAAI,eAAe;AAAA,UAAS;AAAA;AAAA,QAAoC,KAAK,SAAS,KAAK,KAAK,cAAc,GAAG;AACvG,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,4BAA4B,eAAe;AACzC,UAAM,UAAU,cAAc,MAAM,IAAI,OAAO,sBAAsB,CAAC;AACtE,WAAO,UAAU,OAAO,QAAQ,CAAC,CAAC,IAAI;AAAA,EACxC;AAAA,EACA,gBAAgB,qBAAqB,gBAAgB;AACnD,UAAM,qBAAqB,oBAAoB,MAAM,IAAI,EAAE;AAC3D,QAAI,oBAAoB,QAAQ,GAAG,IAAI,KAAK,KAAK,YAAY,OAAO,kBAAkB,IAAI,GAAG;AAC3F,UAAI,KAAK,kBAAkB,OAAkC,KAAK,UAAU;AAC1E,yBAAiB,eAAe,QAAQ,KAAK,GAAG;AAAA,MAClD;AACA,aAAO,KAAK,WAAW,OAAO,cAAc,EAAE,QAAQ,OAAO,kBAAkB,CAAC,IAAI,OAAO,cAAc,EAAE,QAAQ,CAAC;AAAA,IACtH;AACA,WAAO,KAAK,eAAe,cAAc;AAAA,EAC3C;AAAA,EACA,sBAAsB,SAAS;AAC7B,WAAO,QAAQ,MAAM,UAAU,KAAK,QAAQ;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,OAAO,CAAC,OAAO,SAAS,UAAU;AACxH,WAAK,SAAS,YAAY,MAA+C,QAAQ,KAAK;AACtF,UAAI,YAAY,KAA+C;AAC7D,eAAO,KAAK,iBAAiB,OAAO,IAAI,QAAQ,UAAU;AAAA,MAC5D;AACA,WAAK,OAAO;AACZ,YAAM,eAAe,OAAO,QAAQ,MAAM,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AACrE,YAAM,cAAc,IAAI,MAAM,eAAe,CAAC,EAAE,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC;AAC7E,UAAI,QAAQ,MAAM,GAAG,KAAK,MAAM,EAAE,SAAS,KAAK,QAAQ;AAAA,QAAS;AAAA;AAAA,MAAiC,GAAG;AACnG,cAAM,UAAU,QAAQ,MAAM,GAAG,KAAK,SAAS,CAAC;AAChD,eAAO,QAAQ;AAAA,UAAS;AAAA;AAAA,QAA4C,IAAI,QAAQ,cAAc,UAAU,QAAQ;AAAA,MAClH,OAAO;AACL,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,GAAG,EAAE,KAAK;AAAA,EACZ;AAAA,EACA,6BAA6B;AAC3B,WAAO,IAAI,eAAe,EAAE,UAAU,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,uBAAuB,GAAG;AACxC,gBAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,KAAK,eAAc;AAAA,MACtI;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,SAAS,iBAAiB;AACxB,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,cAAc,OAAO,UAAU;AACrC,SAAO,uBAAuB,WAAW,kCACpC,aACA,YAAY,KACb,kCACC,aACA;AAEP;AACA,SAAS,eAAe,aAAa;AACnC,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG,cAAc;AACnB;AACA,SAAS,0BAA0B,aAAa;AAC9C,SAAO,yBAAyB,eAAe,WAAW,CAAC;AAC7D;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,yBAAyB;AAC9B,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,uBAAuB,CAAC;AAC7B,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,aAAa;AAElB,SAAK,eAAe;AACpB,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,eAAe,OAAO,gBAAgB;AAAA,MACzC,MAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU,OAAO,eAAe;AAErC,SAAK,WAAW,OAAK;AAAA,IAAC;AACtB,SAAK,UAAU,MAAM;AAAA,IAAC;AAAA,EACxB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB;AAClB,UAAI,eAAe,iBAAiB,eAAe,iBAAiB,CAAC,eAAe,aAAa;AAC/F,aAAK,aAAa,cAAc;AAAA,MAClC;AACA,UAAI,eAAe,gBAAgB,eAAe,aAAa;AAAA,QAAM;AAAA;AAAA,MAA4B,EAAE,SAAS,GAAG;AAC7G,aAAK,uBAAuB,eAAe,aAAa;AAAA,UAAM;AAAA;AAAA,QAA4B,EAAE,KAAK,CAAC,GAAG,MAAM;AACzG,iBAAO,EAAE,SAAS,EAAE;AAAA,QACtB,CAAC;AACD,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,uBAAuB,CAAC;AAC7B,aAAK,aAAa,eAAe,gBAAgB;AACjD,aAAK,aAAa,iBAAiB,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,QAAI,mBAAmB;AACrB,UAAI,CAAC,kBAAkB,gBAAgB,CAAC,MAAM,QAAQ,kBAAkB,YAAY,GAAG;AACrF;AAAA,MACF,OAAO;AACL,aAAK,aAAa,oBAAoB,kBAAkB,gBAAgB,CAAC;AAAA,MAC3E;AAAA,IACF;AACA,QAAI,sBAAsB;AACxB,WAAK,aAAa,uBAAuB,qBAAqB;AAC9D,UAAI,KAAK,aAAa,sBAAsB;AAC1C,aAAK,aAAa,oBAAoB,KAAK,aAAa,kBAAkB;AAAA,UAAO,OAAK,MAAM;AAAA;AAAA,QAA8B;AAAA,MAC5H;AAAA,IACF;AAEA,QAAI,YAAY,SAAS,cAAc;AACrC,WAAK,aAAa,WAAW,SAAS;AAAA,IACxC;AACA,QAAI,OAAO,IAAI,cAAc;AAC3B,WAAK,aAAa,MAAM,IAAI;AAAA,IAC9B;AACA,QAAI,QAAQ;AACV,WAAK,aAAa,SAAS,OAAO;AAAA,IACpC;AACA,QAAI,QAAQ;AACV,WAAK,aAAa,SAAS,OAAO;AAAA,IACpC;AACA,QAAI,mBAAmB;AACrB,WAAK,aAAa,oBAAoB,kBAAkB;AAAA,IAC1D;AACA,QAAI,eAAe;AACjB,WAAK,aAAa,gBAAgB,cAAc;AAAA,IAClD;AACA,QAAI,uBAAuB;AACzB,WAAK,aAAa,wBAAwB,sBAAsB;AAAA,IAClE;AACA,QAAI,aAAa;AACf,WAAK,aAAa,cAAc,YAAY;AAAA,IAC9C;AACA,QAAI,eAAe;AACjB,WAAK,aAAa,gBAAgB,cAAc;AAChD,UAAI,cAAc,kBAAkB,SAAS,cAAc,iBAAiB,QAAQ,KAAK,YAAY;AACnG,8BAAsB,MAAM;AAC1B,eAAK,aAAa,aAAa,cAAc,MAAM;AAAA,QACrD,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,sBAAsB;AACxB,WAAK,aAAa,uBAAuB,qBAAqB;AAAA,IAChE;AACA,QAAI,qBAAqB;AACvB,WAAK,aAAa,sBAAsB,oBAAoB;AAAA,IAC9D;AACA,QAAI,cAAc;AAChB,WAAK,aAAa,eAAe,aAAa;AAAA,IAChD;AACA,QAAI,iBAAiB;AACnB,WAAK,aAAa,kBAAkB,gBAAgB;AAAA,IACtD;AACA,QAAI,YAAY;AACd,WAAK,aAAa,aAAa,WAAW;AAAA,IAC5C;AACA,QAAI,gBAAgB;AAClB,WAAK,aAAa,iBAAiB,eAAe;AAAA,IACpD;AACA,QAAI,kBAAkB;AACpB,WAAK,aAAa,mBAAmB,iBAAiB;AAAA,IACxD;AACA,QAAI,UAAU;AACZ,WAAK,aAAa,WAAW,SAAS;AAAA,IACxC;AACA,QAAI,qBAAqB;AACvB,WAAK,aAAa,sBAAsB,oBAAoB;AAAA,IAC9D;AACA,QAAI,kBAAkB;AACpB,WAAK,aAAa,mBAAmB,iBAAiB;AAAA,IACxD;AACA,QAAI,mBAAmB;AACrB,WAAK,aAAa,oBAAoB,kBAAkB;AAAA,IAC1D;AACA,QAAI,wBAAwB;AAC1B,WAAK,aAAa,yBAAyB,uBAAuB;AAAA,IACpE;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,QAAI,CAAC,KAAK,aAAa,cAAc,CAAC,KAAK,YAAY;AACrD,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,SAAS;AAC7B,aAAO,KAAK,uBAAuB,KAAK;AAAA,IAC1C;AACA,QAAI,KAAK,aAAa,cAAc;AAClC,aAAO,KAAK,uBAAuB,KAAK;AAAA,IAC1C;AACA,QAAI,KAAK,WAAW;AAAA,MAAW;AAAA;AAAA,IAA0C,GAAG;AAC1E,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,SAAS,KAAK,UAAU,GAAG;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,iBAAiB;AACrC,aAAO;AAAA,IACT;AACA,QAAI,UAAU,SAAS,KAAK,UAAU,GAAG;AACvC,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC;AACA,QAAI,SAAS,MAAM,SAAS,EAAE,UAAU,GAAG;AACzC,UAAI,eAAe;AACnB,UAAI,KAAK,WAAW;AAAA,QAAS;AAAA;AAAA,MAA4C,KAAK,KAAK,WAAW;AAAA,QAAS;AAAA;AAAA,MAA6C,GAAG;AACrJ,cAAM,4BAA4B,KAAK,WAAW,MAAM,KAAK,WAAW;AAAA,UAAQ;AAAA;AAAA,QAA4C,IAAI,GAAG,KAAK,WAAW;AAAA,UAAQ;AAAA;AAAA,QAA6C,CAAC;AACzM,eAAO,8BAA8B,OAAO,MAAM,MAAM,IAAI,OAAO,KAAK,uBAAuB,KAAK;AAAA,MACtG;AACA,UAAI,KAAK,WAAW;AAAA,QAAW;AAAA;AAAA,MAAsC,GAAG;AACtE,eAAO;AAAA,MACT;AACA,iBAAW,OAAO,KAAK,aAAa,UAAU;AAC5C,YAAI,KAAK,aAAa,SAAS,GAAG,GAAG,UAAU;AAC7C,cAAI,KAAK,WAAW,QAAQ,GAAG,MAAM,KAAK,WAAW,YAAY,GAAG,GAAG;AACrE,kBAAM,MAAM,KAAK,WAAW;AAAA,cAAM;AAAA;AAAA,YAAoC,EAAE,OAAO,OAAK,MAAM,GAAG,EAAE;AAAA,cAAK;AAAA;AAAA,YAAoC;AACxI,4BAAgB,IAAI;AAAA,UACtB,WAAW,KAAK,WAAW,QAAQ,GAAG,MAAM,IAAI;AAC9C;AAAA,UACF;AACA,cAAI,KAAK,WAAW,QAAQ,GAAG,MAAM,MAAM,MAAM,SAAS,EAAE,UAAU,KAAK,WAAW,QAAQ,GAAG,GAAG;AAClG,mBAAO;AAAA,UACT;AACA,cAAI,iBAAiB,KAAK,WAAW,QAAQ;AAC3C,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAoC,IAAI,KAAK,MAAM,SAAS,EAAE,SAAS,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAoC,KAAK,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAwC,IAAI,KAAK,MAAM,SAAS,EAAE,SAAS,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAwC,GAAG;AACxU,eAAO,KAAK,uBAAuB,KAAK;AAAA,MAC1C;AACA,UAAI,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAoC,MAAM,MAAM,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAwC,MAAM,IAAI;AACpJ,gBAAQ,OAAO,UAAU,WAAW,OAAO,KAAK,IAAI;AACpD,cAAM,QAAQ,KAAK,WAAW,MAAM,GAAG;AACvC,cAAM,SAAS,KAAK,aAAa,wBAAwB,KAAK,WAAW,SAAS,KAAK,aAAa,2BAA2B,KAAK,UAAU,IAAI,eAAe,KAAK,SAAS,KAAK,WAAW,SAAS,KAAK,OAAO,SAAS,eAAe,KAAK,WAAW,SAAS;AACrQ,YAAI,MAAM,WAAW,GAAG;AACtB,cAAI,MAAM,SAAS,EAAE,SAAS,QAAQ;AACpC,mBAAO,KAAK,uBAAuB,KAAK;AAAA,UAC1C;AAAA,QACF;AACA,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,iBAAiB,MAAM,MAAM,SAAS,CAAC;AAC7C,cAAI,kBAAkB,KAAK,aAAa,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,OAAO,KAAK,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,uBAAuB;AACvK,kBAAM,UAAU,MAAM,MAAM,eAAe,CAAC,CAAC;AAC7C,mBAAO,QAAQ,QAAQ,SAAS,CAAC,EAAE,WAAW,eAAe,SAAS,IAAI,OAAO,KAAK,uBAAuB,KAAK;AAAA,UACpH,YAAY,kBAAkB,CAAC,KAAK,aAAa,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,CAAC,kBAAkB,KAAK,aAAa,0BAA0B,MAAM,UAAU,SAAS,GAAG;AAC3L,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,KAAK,uBAAuB,KAAK;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAoC,MAAM,KAAK,KAAK,WAAW;AAAA,QAAQ;AAAA;AAAA,MAAwC,MAAM,GAAG;AAClJ,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO;AACT,WAAK,WAAW,KAAK;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU;AACR,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc,OAAO;AAEnB,SAAK,UAAU,MAAwC,UAAU,QAAQ,UAAU,WAAc,KAAK,aAAa,aAAa;AAC9H,WAAK,aAAa,cAAc,KAAK,aAAa;AAAA,QAAe;AAAA;AAAA,MAAoC;AAAA,IACvG;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AAET,QAAI,KAAK,aAAc;AACvB,UAAM,KAAK,EAAE;AACb,UAAM,mBAAmB,KAAK,aAAa,iBAAiB,GAAG,KAAK;AACpE,QAAI,GAAG,SAAS,UAAU;AACxB,UAAI,OAAO,qBAAqB,YAAY,OAAO,qBAAqB,UAAU;AAChF,WAAG,QAAQ,iBAAiB,SAAS;AACrC,aAAK,cAAc,GAAG;AACtB,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,YAAY;AACpB,eAAK,SAAS,GAAG,KAAK;AACtB;AAAA,QACF;AACA,YAAI,WAAW,GAAG,mBAAmB,IAAI,GAAG,iBAAiB,KAAK,aAAa,OAAO,SAAS,GAAG;AAClG,YAAI,KAAK,iBAAiB,KAAK,0BAA0B,KAAK,aAAa,qBAAqB,WAAW,GAAG;AAC5G,gBAAM,cAAc,GAAG,MAAM,MAAM,WAAW,GAAG,QAAQ;AACzD,gBAAM,eAAe,KAAK,OAAO;AACjC,gBAAM,eAAe,KAAK,aAAa;AAAA,YAAiB;AAAA,YAAa,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK;AAAA;AAAA,UAAoC;AAC1K,gBAAM,wBAAwB,KAAK,aAAa;AAAA,YAAiB;AAAA,YAAa,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK;AAAA;AAAA,UAAoC;AACnL,gBAAM,uBAAuB,KAAK,aAAa,aAAa,KAAK,aAAa;AAC9E,gBAAM,WAAW,OAAO,KAAK,aAAa,QAAQ,IAAI;AACtD,gBAAM,SAAS,OAAO,KAAK,aAAa,MAAM,IAAI;AAClD,cAAI,KAAK,UAAU,aAA4C;AAC7D,gBAAI,CAAC,sBAAsB;AACzB,kBAAI,KAAK,aAAa,aAAa,cAAc;AAC/C,qBAAK,aAAa,cAAc,GAAG,KAAK,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,YAAY,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,cAChJ,WAAW,KAAK,aAAa,aAAa,KAAK,aAAa,YAAY,SAAS,cAAc;AAC7F,qBAAK,aAAa,cAAc,GAAG,KAAK,WAAW,GAAG,KAAK,aAAa,YAAY,MAAM,UAAU,MAAM,CAAC;AAAA,cAC7G,OAAO;AACL,qBAAK,aAAa,cAAc,GAAG,KAAK,MAAM,GAAG,KAAK,YAAY,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG,QAAQ,CAAC,GAAG,KAAK,aAAa,YAAY,MAAM,UAAU,MAAM,CAAC,GAAG,KAAK,aAAa,YAAY,MAAM,SAAS,cAAc,KAAK,aAAa,YAAY,SAAS,YAAY,CAAC,GAAG,KAAK,MAAM;AAAA,cAC1S;AAAA,YACF,WAAW,CAAC,KAAK,aAAa,kBAAkB,SAAS,KAAK,aAAa,eAAe,MAAM,WAAW,KAAK,OAAO,QAAQ,WAAW,IAAI,KAAK,OAAO,MAAM,CAAC,KAAK,sBAAsB;AAC1L,kBAAI,aAAa,KAAK,KAAK,QAAQ;AACjC,qBAAK,aAAa,cAAc,GAAG,KAAK,MAAM,GAAG,KAAK,aAAa,oBAAoB,GAAG,GAAG,MAAM,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,MAAM;AACxK,2BAAW,WAAW;AAAA,cACxB,OAAO;AACL,sBAAM,QAAQ,GAAG,MAAM,UAAU,GAAG,QAAQ;AAC5C,sBAAM,QAAQ,GAAG,MAAM,UAAU,QAAQ;AACzC,qBAAK,aAAa,cAAc,GAAG,KAAK,GAAG,KAAK,aAAa,oBAAoB,GAAG,KAAK;AAAA,cAC3F;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,UAAU,aAA4C;AAC7D,gBAAI,CAAC,gBAAgB,CAAC,yBAAyB,sBAAsB;AACnE,yBAAW,OAAO,GAAG,cAAc,IAAI;AAAA,YACzC,WAAW,KAAK,aAAa,kBAAkB,SAAS,GAAG,MAAM,MAAM,UAAU,WAAW,CAAC,CAAC,KAAK,yBAAyB,CAAC,KAAK,aAAa,kBAAkB,SAAS,GAAG,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,GAAG;AACrN,mBAAK,aAAa,cAAc,GAAG,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,GAAG,GAAG,MAAM,MAAM,UAAU,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,MAAM,MAAM,WAAW,CAAC,CAAC;AACxJ,yBAAW,WAAW;AAAA,YACxB,WAAW,cAAc;AACvB,kBAAI,GAAG,MAAM,WAAW,KAAK,aAAa,GAAG;AAC3C,qBAAK,aAAa,cAAc,GAAG,KAAK,MAAM,GAAG,WAAW,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,CAAC,GAAG,KAAK,MAAM;AAAA,cAC3J,OAAO;AACL,qBAAK,aAAa,cAAc,GAAG,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,MAAM,MAAM,WAAW,CAAC,EAAE,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,MAAM;AAAA,cAC3J;AAAA,YACF,WAAW,KAAK,UAAU,GAAG,MAAM,WAAW,KAAK,WAAW,iBAAiB,KAAK,KAAK,aAAa;AAAA,cAAiB,GAAG;AAAA,cAAO,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK;AAAA;AAAA,YAAoC,GAAG;AACvO,mBAAK,aAAa,cAAc,GAAG,KAAK,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,CAAC,GAAG,KAAK,MAAM;AAAA,YACxJ;AAAA,UACF;AAAA,QACF;AACA,YAAI,aAAa;AACjB,YAAI,iBAAiB;AACrB,YAAI,KAAK,UAAU,YAAwC,aAA4C;AACrG,eAAK,aAAa,0BAA0B;AAAA,QAC9C;AACA,YAAI,KAAK,YAAY,UAAU,KAAK,aAAa,eAAe,SAAS,KAAK,KAAK,UAAU,eAA8C,KAAK,aAAa,mBAAmB,gBAAuD,WAAW,IAAI;AACpP,gBAAM,cAAc,KAAK,YAAY,MAAM,WAAW,GAAG,QAAQ;AACjE,aAAG,QAAQ,KAAK,YAAY,MAAM,GAAG,WAAW,CAAC,IAAI,cAAc,KAAK,YAAY,MAAM,WAAW,CAAC;AAAA,QACxG;AACA,YAAI,KAAK,aAAa,mBAAmB,gBAAuD,KAAK,kBAAkB;AACrH,cAAI,WAAW,KAAK,OAAO,GAAG,KAAK,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI,MAAM,aAAa,KAAK,OAAO,GAAG,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;AACzH,uBAAW,WAAW;AAAA,UACxB;AAAA,QACF;AACA,YAAI,KAAK,aAAa,mBAAmB,cAAyD,KAAK,KAAK;AAC1G,cAAI,KAAK,eAAe,GAAG,MAAM,MAAM,GAAG,CAAC,MAAM,MAAuC;AACtF,eAAG,QAAQ,GAAG,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,MAAM,MAAM,GAAG,GAAG,MAAM,MAAM;AAAA,UACrE;AACA,aAAG,QAAQ,GAAG,UAAU,OAAwC,MAAuC,GAAG;AAAA,QAC5G;AACA,aAAK,aAAa,kBAAkB,UAAU,KAAK,aAAa,KAAK,UAAU,eAA8C,KAAK,UAAU,UAAsC,CAAC,OAAO,oBAAoB;AAC5M,eAAK,cAAc;AACnB,uBAAa;AACb,2BAAiB;AAAA,QACnB,CAAC;AAED,YAAI,KAAK,kBAAkB,MAAM,IAAI;AACnC;AAAA,QACF;AACA,YAAI,KAAK,aAAa,iBAAiB;AACrC,qBAAW,WAAW;AACtB,eAAK,aAAa,kBAAkB;AAAA,QACtC;AAEA,YAAI,KAAK,qBAAqB,QAAQ;AACpC,cAAI,KAAK,UAAU,aAA4C;AAC7D,kBAAM,uBAAuB,KAAK,kBAAkB,SAAS,KAAK,aAAa,YAAY,MAAM,WAAW,GAAG,QAAQ,CAAC;AACxH,kBAAM,sBAAsB,KAAK,kBAAkB,SAAS,KAAK,aAAa,YAAY,MAAM,UAAU,WAAW,CAAC,CAAC;AACvH,gBAAI,KAAK,2BAA2B,CAAC,qBAAqB;AACxD,yBAAW,GAAG,iBAAiB;AAC/B,mBAAK,0BAA0B;AAAA,YACjC,OAAO;AACL,yBAAW,uBAAuB,WAAW,IAAI;AAAA,YACnD;AAAA,UACF,OAAO;AACL,uBAAW,GAAG,mBAAmB,IAAI,GAAG,iBAAiB,KAAK,aAAa,OAAO,SAAS,GAAG;AAAA,UAChG;AAAA,QACF;AACA,aAAK,YAAY,KAAK,cAAc,KAAK,KAAK,YAAY,WAAW,IAAI,OAAO,KAAK;AACrF,YAAI,kBAAkB,KAAK,YAAY,KAAK,YAAY,SAAS,WAAW,aAAa,YAAY,KAAK,UAAU,eAA8C,CAAC,iBAAiB,IAAI;AACxL,YAAI,kBAAkB,KAAK,sBAAsB,GAAG;AAClD,4BAAkB,GAAG,UAAU,KAAK,aAAa,iBAAiB,GAAG,MAAM,WAAW,IAAI,KAAK,sBAAsB,IAAI,IAAI,KAAK,sBAAsB;AAAA,QAC1J;AACA,YAAI,kBAAkB,GAAG;AACvB,4BAAkB;AAAA,QACpB;AACA,WAAG,kBAAkB,iBAAiB,eAAe;AACrD,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,gBAAQ,KAAK,sEAAsE,OAAO,gBAAgB;AAAA,MAC5G;AAAA,IACF,OAAO;AACL,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,SAAS,GAAG,KAAK;AACtB;AAAA,MACF;AACA,WAAK,aAAa;AAAA,QAAkB,GAAG,MAAM;AAAA,QAAQ,KAAK;AAAA,QAAa,KAAK,UAAU,eAA8C,KAAK,UAAU;AAAA;AAAA,MAAoC;AAAA,IACzL;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,iBAAiB,GAAG;AAClB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,YAAY;AACnB,YAAM,KAAK,EAAE;AACb,UAAI,KAAK,YAAY,GAAG,MAAM,SAAS,KAAK,OAAO,KAAK,kBAAkB,UAAU;AAClF,cAAM,iBAAiB,KAAK,aAAa;AACzC,cAAM,YAAY,OAAO,KAAK,aAAa,eAAe,MAAM,eAAe,SAAS,GAAG,eAAe,MAAM,CAAC;AACjH,YAAI,YAAY,GAAG;AACjB,aAAG,QAAQ,KAAK,SAAS,GAAG,MAAM,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG;AACnE,gBAAM,cAAc,GAAG,MAAM,MAAM,KAAK,aAAa,EAAE,CAAC;AACxD,aAAG,QAAQ,GAAG,MAAM,SAAS,KAAK,aAAa,IAAI,GAAG,QAAQ,IAAqC,OAAO,YAAY,YAAY,MAAM,IAAI,KAAK,SAAS,GAAG,QAAQ,KAAK,gBAAgB,IAAqC,OAAO,SAAS,IAAI,KAAK;AACxP,eAAK,aAAa,cAAc,GAAG;AAAA,QACrC;AAAA,MACF;AACA,WAAK,aAAa,kBAAkB;AAAA,IACtC;AACA,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,UAAM,KAAK,EAAE;AACb,UAAM,WAAW;AACjB,UAAM,SAAS;AACf,QAAI,OAAO,QAAQ,GAAG,mBAAmB,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,iBAAiB,KAAK,aAAa,OAAO;AAAA,IAEvI,EAAE,YAAY,IAAI;AAChB,UAAI,KAAK,aAAa,iBAAiB,CAAC,KAAK,wBAAwB;AAEnE,aAAK,aAAa,cAAc,KAAK,aAAa,gBAAgB;AAClE,YAAI,GAAG,qBAAqB,KAAK,aAAa,SAAS,KAAK,aAAa,gBAAgB,GAAG,OAAO;AAEjG,aAAG,MAAM;AACT,aAAG,kBAAkB,UAAU,MAAM;AAAA,QACvC,OAAO;AAEL,cAAI,GAAG,iBAAiB,KAAK,aAAa,YAAY,QAAQ;AAE5D,eAAG,kBAAkB,KAAK,aAAa,YAAY,QAAQ,KAAK,aAAa,YAAY,MAAM;AAAA,UACjG;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,OAAO,GAAG,UAAU,KAAK,aAAa,SAAS,KAAK,aAAa,SAAS,KAAK,aAAa,cAAc,GAAG;AAE/H,QAAI,MAAM,GAAG,UAAU,WAAW;AAChC,SAAG,QAAQ;AAAA,IACb;AAEA,QAAI,MAAM,GAAG,SAAS,aAAa,GAAG,kBAAkB,GAAG,iBAAiB,KAAK,aAAa,OAAO,QAAQ;AAC3G,SAAG,iBAAiB,KAAK,aAAa,OAAO;AAC7C;AAAA,IACF;AAEA,QAAI,MAAM,GAAG,eAAe,KAAK,sBAAsB,GAAG;AACxD,SAAG,eAAe,KAAK,sBAAsB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,UAAU,GAAG;AACX,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AAErB,UAAI,EAAE,QAAQ,QAAS,MAAK,iBAAiB,CAAC;AAC9C;AAAA,IACF;AACA,SAAK,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;AACjC,UAAM,KAAK,EAAE;AACb,SAAK,cAAc,GAAG;AACtB,SAAK,SAAS;AACd,QAAI,GAAG,SAAS,UAAU;AACxB,UAAI,EAAE,QAAQ,WAAyC;AACrD,UAAE,eAAe;AAAA,MACnB;AACA,UAAI,EAAE,QAAQ,eAA+C,EAAE,QAAQ,eAA8C,EAAE,QAAQ,UAAsC;AACnK,YAAI,EAAE,QAAQ,eAA8C,GAAG,MAAM,WAAW,GAAG;AACjF,aAAG,iBAAiB,GAAG;AAAA,QACzB;AACA,YAAI,EAAE,QAAQ,eAA8C,GAAG,mBAAmB,GAAG;AAEnF,eAAK,oBAAoB,KAAK,mBAAmB,SAAS,KAAK,oBAAoB,KAAK,QAAQ;AAChG,cAAI,KAAK,OAAO,SAAS,KAAK,GAAG,kBAAkB,KAAK,OAAO,QAAQ;AACrE,eAAG,kBAAkB,KAAK,OAAO,QAAQ,GAAG,YAAY;AAAA,UAC1D,OAAO;AACL,gBAAI,KAAK,YAAY,WAAW,GAAG,kBAAkB,GAAG,mBAAmB,GAAG;AAC5E,qBAAO,KAAK,kBAAkB,UAAU,KAAK,YAAY,GAAG,iBAAiB,CAAC,KAAK,IAAsC,SAAS,CAAC,MAAM,KAAK,OAAO,UAAU,KAAK,GAAG,iBAAiB,KAAK,OAAO,UAAU,KAAK,OAAO,WAAW,IAAI;AACvO,mBAAG,kBAAkB,GAAG,iBAAiB,GAAG,GAAG,YAAY;AAAA,cAC7D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,aAAK,yBAAyB,EAAE;AAChC,YAAI,KAAK,aAAa,OAAO,UAAU,GAAG,kBAAkB,KAAK,aAAa,OAAO,UAAU,GAAG,gBAAgB,KAAK,aAAa,OAAO,QAAQ;AACjJ,YAAE,eAAe;AAAA,QACnB;AACA,cAAM,cAAc,GAAG;AACvB,YAAI,EAAE,QAAQ,eAA8C,CAAC,GAAG,YAAY,gBAAgB,KAAK,GAAG,iBAAiB,GAAG,MAAM,UAAU,GAAG,MAAM,WAAW,GAAG;AAC7J,eAAK,YAAY,KAAK,aAAa,SAAS,KAAK,aAAa,OAAO,SAAS;AAC9E,eAAK,aAAa,UAAU,KAAK,aAAa,QAAQ,KAAK,aAAa,gBAAgB,KAAK,SAAS;AAAA,QACxG;AAAA,MACF;AACA,UAAI,CAAC,CAAC,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,KAAK,YAAY,SAAS,KAAK,OAAO,SAAS,GAAG,gBAAgB;AAC/G,WAAG,kBAAkB,KAAK,YAAY,SAAS,KAAK,OAAO,QAAQ,KAAK,YAAY,MAAM;AAAA,MAC5F,WAAW,EAAE,SAAS,UAAU,EAAE,WAAW,EAAE,SAAS,UAAU,EAAE,SAClE;AACA,WAAG,kBAAkB,GAAG,KAAK,sBAAsB,CAAC;AACpD,UAAE,eAAe;AAAA,MACnB;AACA,WAAK,aAAa,WAAW,GAAG;AAChC,WAAK,aAAa,SAAS,GAAG;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEM,WAAW,cAAc;AAAA;AAC7B,UAAI,OAAO,iBAAiB,YAAY,iBAAiB,QAAQ,WAAW,cAAc;AACxF,YAAI,aAAa,cAAc;AAC7B,eAAK,iBAAiB,QAAQ,aAAa,OAAO,CAAC;AAAA,QACrD;AACA,uBAAe,aAAa;AAAA,MAC9B;AACA,UAAI,iBAAiB,MAAM;AACzB,uBAAe,KAAK,mBAAmB,KAAK,iBAAiB,YAAY,IAAI;AAAA,MAC/E;AACA,UAAI,OAAO,iBAAiB,YAAY,OAAO,iBAAiB,YAAY,iBAAiB,QAAQ,iBAAiB,QAAW;AAC/H,YAAI,iBAAiB,QAAQ,iBAAiB,UAAa,iBAAiB,IAAI;AAC9E,eAAK,aAAa,gBAAgB;AAClC,eAAK,aAAa,iBAAiB;AAAA,QACrC;AACA,YAAI,aAAa;AACjB,YAAI,OAAO,eAAe,YAAY,KAAK,WAAW;AAAA,UAAW;AAAA;AAAA,QAA0C,GAAG;AAC5G,uBAAa,OAAO,UAAU;AAC9B,gBAAM,sBAAsB,KAAK,aAAa,2BAA2B;AACzE,cAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,aAAa,GAAG;AACnD,yBAAa,KAAK,aAAa,kBAAkB,sBAAsB,WAAW,QAAQ,qBAAqB,KAAK,aAAa,aAAa,IAAI;AAAA,UACpJ;AACA,cAAI,KAAK,aAAa,YAAY,cAAc,KAAK,kBAAkB,KAAK,0BAA0B,OAAO;AAC3G,yBAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,gBAAgB,UAAU;AAAA,UAC7F;AACA,cAAI,KAAK,kBAAkB,OAAkC,MAAM,QAAQ,KAAK,aAAa,aAAa,KAAK,KAAK,sBAAsB,KAA8B;AACtK,yBAAa,WAAW,SAAS,EAAE;AAAA,cAAQ;AAAA,cAA8B;AAAA;AAAA,YAA8B;AAAA,UACzG;AACA,cAAI,KAAK,gBAAgB;AAAA,YAAW;AAAA;AAAA,UAA0C,KAAK,KAAK,UAAU;AAChG,kCAAsB,MAAM;AAC1B,mBAAK,aAAa,UAAU,YAAY,SAAS,KAAK,IAAI,KAAK,aAAa,cAAc;AAAA,YAC5F,CAAC;AAAA,UACH;AACA,eAAK,aAAa,gBAAgB;AAAA,QACpC;AACA,YAAI,OAAO,eAAe,UAAU;AAClC,uBAAa;AAAA,QACf;AACA,aAAK,cAAc;AACnB,aAAK,SAAS;AACd,YAAI,cAAc,KAAK,aAAa,kBAAkB,KAAK,aAAa,mBAAmB,KAAK,aAAa,UAAU,KAAK,aAAa,gBAAgB;AAEvJ,iBAAO,KAAK,qBAAqB,aAAa,KAAK,aAAa,eAAe,OAAO;AACtF,eAAK,aAAa,sBAAsB,CAAC,SAAS,KAAK,aAAa,UAAU,YAAY,KAAK,aAAa,cAAc,CAAC;AAE3H,iBAAO,KAAK,qBAAqB,aAAa,KAAK,aAAa,eAAe,QAAQ;AAAA,QACzF,OAAO;AACL,eAAK,aAAa,sBAAsB,CAAC,SAAS,UAAU;AAAA,QAC9D;AACA,aAAK,cAAc;AAAA,MACrB,OAAO;AACL,gBAAQ,KAAK,sEAAsE,OAAO,YAAY;AAAA,MACxG;AAAA,IACF;AAAA;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,aAAa,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,WAAW,KAAK,UAAU;AAC1C,UAAM,eAAe,UAAU,eAAe;AAC9C,QAAI,CAAC,cAAc,eAAe;AAChC,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,yBAAyB,IAAI;AAC3B,OAAG,iBAAiB,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,QAAQ,GAAG,cAAc,GAAG,KAAK,YAAY,SAAS,KAAK,OAAO,MAAM;AAC1H,OAAG,eAAe,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,QAAQ,GAAG,YAAY,GAAG,KAAK,YAAY,SAAS,KAAK,OAAO,MAAM;AAAA,EACxH;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,aAAa,sBAAsB,CAAC,YAAY,UAAU;AAAA,EACjE;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,aAAa,iBAAiB,KAAK,aAAa,sBAAsB,KAAK,cAAc,EAAE;AAChG,SAAK,aAAa,sBAAsB,CAAC,SAAS,KAAK,aAAa,UAAU,KAAK,aAAa,KAAK,aAAa,cAAc,CAAC;AAAA,EACnI;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,aAAa,KAAK,WAAW;AAAA,MAAM;AAAA;AAAA,IAAoC,EAAE,OAAO,OAAK,MAAM,GAAG,EAAE;AACtG,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,EAAE,MAAM,MAAM,SAAS,CAAC,KAAK,QAAQ,KAAK,MAAM,SAAS,cAAc,MAAM,UAAU,aAAa,GAAG;AACzG,aAAO,KAAK,uBAAuB,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,YAAY,UAAU,KAAK,aAAa,YAAY,SAAS,KAAK,aAAa,OAAO;AAAA,EACjH;AAAA,EACA,uBAAuB,aAAa;AAClC,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,cAAc,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,KAAK,UAAQ;AACrC,YAAM,eAAe,KAAK;AAAA,QAAM;AAAA;AAAA,MAAoC,EAAE,KAAK,UAAQ,KAAK,aAAa,kBAAkB,SAAS,IAAI,CAAC;AACrI,UAAI,gBAAgB,KAAK,eAAe,KAAK,kCAAkC,KAAK,oBAAoB,KAAK,KAAK;AAAA,QAAS;AAAA;AAAA,MAA4C,GAAG;AACxK,cAAM,OAAO,KAAK,aAAa,WAAW,KAAK,WAAW,GAAG,UAAU,KAAK,aAAa,WAAW,IAAI,GAAG;AAC3G,YAAI,MAAM;AACR,eAAK,aAAa,KAAK,iBAAiB,KAAK,aAAa,iBAAiB,KAAK;AAAA,YAAS;AAAA;AAAA,UAA4C,IAAI,KAAK,aAAa,sBAAsB,IAAI,IAAI;AACzL,iBAAO;AAAA,QACT,OAAO;AACL,cAAI,KAAK,UAAU,aAA4C;AAC7D,iBAAK,0BAA0B;AAAA,UACjC;AACA,gBAAM,aAAa,KAAK,qBAAqB,KAAK,qBAAqB,SAAS,CAAC,KAAK;AACtF,eAAK,aAAa,KAAK,iBAAiB,KAAK,aAAa,iBAAiB,WAAW;AAAA,YAAS;AAAA;AAAA,UAA4C,IAAI,KAAK,aAAa,sBAAsB,UAAU,IAAI;AAAA,QACvM;AAAA,MACF,OAAO;AACL,cAAM,QAAQ,KAAK,aAAa,WAAW,KAAK,WAAW,GAAG;AAAA,UAAM;AAAA;AAAA,QAAoC,EAAE,MAAM,CAAC,WAAW,UAAU;AACpI,gBAAM,YAAY,KAAK,OAAO,KAAK;AACnC,iBAAO,KAAK,aAAa,iBAAiB,WAAW,SAAS;AAAA,QAChE,CAAC;AACD,YAAI,SAAS,KAAK,aAAa;AAC7B,eAAK,aAAa,KAAK,iBAAiB,KAAK,aAAa,iBAAiB;AAC3E,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kCAAkC,OAAO;AACvC,UAAM,oBAAoB,KAAK,aAAa;AAC5C,aAAS,wBAAwB,KAAK;AACpC,YAAM,QAAQ,IAAI,OAAO,IAAI,kBAAkB,IAAI,QAAM,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG;AACpF,aAAO,IAAI,QAAQ,OAAO,EAAE;AAAA,IAC9B;AACA,UAAM,eAAe,MAAM,IAAI,uBAAuB;AACtD,WAAO,aAAa,MAAM,SAAO;AAC/B,YAAM,mBAAmB,IAAI,IAAI,GAAG;AACpC,aAAO,iBAAiB,SAAS;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,GAAG,CAAC,YAAY,QAAQ,EAAE,CAAC;AAAA,MAC3D,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC,EAAE,iBAAiB,SAAS,kDAAkD,QAAQ;AACrF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC,EAAE,oBAAoB,SAAS,qDAAqD,QAAQ;AAC3F,mBAAO,IAAI,mBAAmB,MAAM;AAAA,UACtC,CAAC,EAAE,kBAAkB,SAAS,mDAAmD,QAAQ;AACvF,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC,EAAE,QAAQ,SAAS,yCAAyC,QAAQ;AACnE,mBAAO,IAAI,OAAO,MAAM;AAAA,UAC1B,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC,EAAE,WAAW,SAAS,4CAA4C,QAAQ;AACzE,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB,CAAI,WAAa,MAAM,QAAQ,gBAAgB;AAAA,QAC/D,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,QAAQ,SAAS;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG,cAAc,CAAC,GAAM,oBAAoB;AAAA,IAC9C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG,cAAc;AAAA,MACjB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AAAA,IACpC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;AAAA,IACvC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;AAAA,IACrC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc;AACZ,SAAK,iBAAiB,OAAO,eAAe;AAC5C,SAAK,eAAe,OAAO,cAAc;AACzC,SAAK,uBAAuB,CAAC;AAC7B,SAAK,OAAO;AAAA,EACd;AAAA,EACA,UAAU,OAAO,MAAM,KAGnB,CAAC,GAAG;AAHe,iBACrB;AAAA;AAAA,IA18DJ,IAy8DyB,IAElB,mBAFkB,IAElB;AAAA,MADH;AAAA;AAGA,UAAM,gBAAgB;AAAA,MACpB,gBAAgB;AAAA,OACb,KAAK,iBACL,SAHiB;AAAA,MAIpB,UAAU,kCACL,KAAK,aAAa,WAClB;AAAA,IAEP;AACA,WAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAKA,MAAK,MAAM;AAEtD,WAAK,aAAa,GAAG,IAAIA;AAAA,IAC3B,CAAC;AACD,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,UAAI,KAAK,MAAM,IAAI,EAAE,SAAS,GAAG;AAC/B,aAAK,uBAAuB,KAAK,MAAM,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM;AAC1D,iBAAO,EAAE,SAAS,EAAE;AAAA,QACtB,CAAC;AACD,aAAK,SAAS,KAAK;AACnB,eAAO,KAAK,aAAa,UAAU,GAAG,KAAK,IAAI,KAAK,IAAI;AAAA,MAC1D,OAAO;AACL,aAAK,uBAAuB,CAAC;AAC7B,eAAO,KAAK,aAAa,UAAU,GAAG,KAAK,IAAI,KAAK,IAAI;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,KAAK;AAAA,MAAS;AAAA;AAAA,IAA4C,GAAG;AAC/D,aAAO,KAAK,aAAa,UAAU,GAAG,KAAK,IAAI,KAAK,aAAa,sBAAsB,IAAI,CAAC;AAAA,IAC9F;AACA,QAAI,KAAK;AAAA,MAAW;AAAA;AAAA,IAA0C,GAAG;AAC/D,UAAI,OAAO,eAAe;AACxB,aAAK,aAAa,gBAAgB,OAAO;AAAA,MAC3C;AACA,UAAI,OAAO,mBAAmB;AAC5B,aAAK,aAAa,oBAAoB,OAAO;AAAA,MAC/C;AACA,UAAI,OAAO,UAAU;AACnB,aAAK,aAAa,WAAW,OAAO;AAAA,MACtC;AACA,cAAQ,OAAO,KAAK;AACpB,YAAM,sBAAsB,KAAK,aAAa,2BAA2B;AACzE,UAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,aAAa,GAAG;AACnD,gBAAQ,KAAK,aAAa,kBAAkB,sBAAsB,MAAM,QAAQ,qBAAqB,KAAK,aAAa,aAAa,IAAI;AAAA,MAC1I;AACA,UAAI,KAAK,aAAa,YAAY,SAAS,KAAK,aAAa,0BAA0B,OAAO;AAC5F,gBAAQ,KAAK,aAAa,gBAAgB,MAAM,KAAK;AAAA,MACvD;AACA,UAAI,KAAK,aAAa,kBAAkB,KAAgC;AACtE,gBAAQ,MAAM,SAAS,EAAE;AAAA,UAAQ;AAAA,UAA8B;AAAA;AAAA,QAA8B;AAAA,MAC/F;AACA,WAAK,aAAa,gBAAgB;AAAA,IACpC;AACA,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,aAAO,KAAK,aAAa,UAAU,IAAI,IAAI;AAAA,IAC7C;AACA,WAAO,KAAK,aAAa,UAAU,GAAG,KAAK,IAAI,IAAI;AAAA,EACrD;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,qBAAqB,SAAS,GAAG;AACxC,WAAK,qBAAqB,KAAK,UAAQ;AACrC,cAAM,OAAO,KAAK,aAAa,WAAW,KAAK,GAAG,UAAU,KAAK,aAAa,WAAW,IAAI,GAAG;AAChG,YAAI,SAAS,MAAM;AACjB,eAAK,OAAO;AACZ,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,aAAa,KAAK,qBAAqB,KAAK,qBAAqB,SAAS,CAAC,KAAK;AACtF,eAAK,OAAO;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAa;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["value"]}