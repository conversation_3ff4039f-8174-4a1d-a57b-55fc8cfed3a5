// Angular Core
import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, IsActiveMatchOptions, NavigationEnd, Router } from '@angular/router';

// Angular Animations
import { animate, style, transition, trigger } from '@angular/animations';

// Third-Party
import { TranslateService } from '@ngx-translate/core';
import { filter } from 'rxjs';

// Services
import { AuthService } from '../../shared/services/auth.services';
import { BreadcrumbService } from '../../shared/services/breadcrumb.service';
import { CommonService } from '../../shared/services/common.service';

// Utils
import { CommonUtil } from '../../shared/common.util';
import { StringUtilService } from '../../utils/StringUtil.Service';

// Models
import { User } from '../../models/access/user';
import { AccountService } from '../../services/account.service';
import { FeatureVisibilityService } from '../../services/feature-visibility.service';
import { CalendarResizeService } from '../../shared/services/resize-notifier.service';

@Component({
    selector: 'app-layout',
    templateUrl: './layout.component.html',
    styleUrls: ['./layout.component.scss'],

    animations: [
        trigger('submenuAnimation', [
            transition(':enter', [
                style({ height: '0px', opacity: 0 }),
                animate('300ms ease-out', style({ height: '*', opacity: 1 })),
            ]),
            transition(':leave', [animate('300ms ease-in', style({ height: '0px', opacity: 0 }))]),
        ]),
    ],
})
export class LayoutComponent implements OnInit {
    @ViewChild('myContentElement', { static: false }) myContentElement!: ElementRef;
    @ViewChild('mySideBar', { static: false }) mySideBar!: ElementRef;

    pageTitle = '';
    actions: Array<any> = [];
    breadcrumbs: Array<any> = [];
    isShowSubMenu: boolean[] = [];
    user!: User;

    fullName!: string;

    isSettingOpen = false;
    isToggleOpen: boolean = false;

    public userData!: User;
    public breadCrumbNumber: any = null;

    visibilityData = {} as any;

    exactMatchOptions: IsActiveMatchOptions = {
        paths: 'exact',
        matrixParams: 'ignored',
        queryParams: 'ignored',
        fragment: 'ignored',
    };

    sidebarItems = [
        {
            key: 'DashboardMenu',
            title: 'DASHBOARD.objName',
            route: '/dashboard',
            icon: 'home.svg',
            permission: null,
            matchType: 'exact'
        },
        {
            key: 'EmployeeMenu',
            title: 'USERS.objNames',
            route: '/dashboard/employees',
            icon: 'user.svg',
            permission: { entity: 'EMPLOYEES', type: 'Page' },
            matchRoutes: ['/dashboard/employees', '/dashboard/employee/edit']
        },
        {
            key: 'CustomerMenu',
            title: 'CUSTOMER.objNames',
            route: '/dashboard/customers',
            icon: 'customers.svg',
            permission: { entity: 'CUSTOMER', type: 'Page' },
            matchRoutes: ['/dashboard/customers', '/dashboard/customer/edit']
        },
        {
            key: 'RateSheetMenu',
            title: 'RATE_SHEET.objNames',
            route: '/dashboard/rate-sheets',
            icon: 'price-list.svg',
            permission: { entity: 'RATESHEET', type: 'Page' },
            matchRoutes: ['/dashboard/rate-sheets', '/dashboard/rate-sheet/edit']
        },
        {
            key: 'QuotationMenu',
            title: 'Quotation.objNames',
            route: '/dashboard/quotations',
            icon: 'app.svg',
            permission: { entity: 'QUOTATION', type: 'Page' },
            matchRoutes: ['/dashboard/quotations', '/dashboard/quotation/edit']
        },
        {
            key: 'BarcodeMenu',
            title: 'BARCODE.objNames',
            route: '/dashboard/barcodes',
            icon: 'barcode.svg',
            permission: { entity: 'BARCODE', type: 'Page' },
            matchRoutes: ['/dashboard/barcodes', '/dashboard/barcode/edit']
        },
        {
            key: 'ShipmentMenu',
            title: 'SHIPMENT.objNames',
            route: '/dashboard/shipments',
            icon: 'delivery-truck.svg',
            permission: { entity: 'SHIPMENT', type: 'Page' },
            matchRoutes: ['/dashboard/shipments', '/dashboard/shipment/edit']
        },
        {
            key: 'ShipmentCalenderMenu',
            title: 'SHIPMENT.shipmentCalender',
            route: '/dashboard/shipment/calender',
            icon: 'calendar_view_month.svg',
            permission: { entity: 'SHIPMENT_CALENDER', type: 'Page' },
            matchRoutes: ['/dashboard/shipment/calender']
        },
        {
            key: 'DriverMenu',
            title: 'DriverLocation.objNames',
            route: '/dashboard/driver/locations',
            icon: 'driverLocation.svg',
            permission: { entity: 'DRIVER', type: 'Page' },
            matchRoutes: ['/dashboard/driver/locations']
        },
        {
            key: 'FuelReceiptMenu',
            title: 'FuelReceipt.objNames',
            route: '/dashboard/fuel/receipt',
            icon: 'fuel_receipt.svg',
            permission: null,
            matchRoutes: ['/dashboard/fuel/receipt']
        },
        {
            key: 'ContactUsMenu',
            title: 'Contacts.objNames',
            route: '/dashboard/contacts',
            icon: 'contact_us.svg',
            permission: null,
            matchRoutes: ['/dashboard/contacts']
        }
    ];

    constructor(
        public authService: AuthService,
        public commonUtil: CommonUtil,
        public stringUtilService: StringUtilService,
        protected commonService: CommonService,
        protected activatedRoute: ActivatedRoute,
        protected renderer: Renderer2,
        protected featureVisibilityService: FeatureVisibilityService,
        protected changeDetectorRef: ChangeDetectorRef,
        private readonly translate: TranslateService,
        private readonly titleService: Title,
        private readonly breadcrumbService: BreadcrumbService,
        private readonly cdr: ChangeDetectorRef,
        private readonly router: Router,
        private calendarResizeService: CalendarResizeService,
        protected accountService: AccountService,
    ) {
        this.translate.setDefaultLang('en');

        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(() => {
            this.processMetaData();
        });
    }

    ngOnInit(): void {
        this.breadcrumbService.breadcrumbs$.subscribe((breadcrumbs) => {
            if (breadcrumbs && breadcrumbs.length > 0) {
                this.breadcrumbs = breadcrumbs;
                this.cdr.detectChanges();
            }
        });

        this.breadcrumbService.number$.subscribe((value) => {
            this.breadCrumbNumber = value;
        });

        this.userData = this.authService.getUser();
        this.fullName = this.getFullName();

        this.breadcrumbService.number$.subscribe((value) => {
            this.breadCrumbNumber = value;
        });

        this.authService.user$.subscribe((user) => {
            if (user.id ?? null) {
                this.userData = user;
                this.fullName = this.getFullName();
                this.changeDetectorRef.detectChanges();
            }
        });

        this.featureVisibilityService.getVisibilityData().subscribe((data) => {
            this.visibilityData = data.pages.Sidebar;
        });

        this.updateThemeBasedOnLocation('canada');

        // ✅ Close sidebar on route change if screen < 992px
        this.router.events.subscribe((event) => {
            if (event instanceof NavigationEnd && window.innerWidth < 992) {
                if (this.isToggleOpen) {
                    this.toggleSidebar(); // 👈 use your existing method to close it
                }
            }
        });
    }

    ngAfterViewInit() {
        this.cdr.detectChanges();
    }

    processMetaData(): void {
        const route = this.getChild(this.activatedRoute);

        route.data.subscribe((data: any) => {
            this.titleService.setTitle(data.title);
            this.pageTitle = data.title;
            this.breadcrumbs = data.breadcrumbs ?? [];
            this.actions = data.actions ?? [];
            this.user = this.authService.getUser();
        });
    }

    getFullName() {
        const user = this.authService.getUser();
        return user ? `${user.firstName} ${user.lastName}` : '';
    }

    getFormKeyword(breadcrumb: { active: boolean }) {
        if (!breadcrumb?.active) return '';

        const url = this.router.url;

        if (url.endsWith('/edit/0')) {
            return 'Add New';
        }

        if (url.includes('/edit/')) {
            return 'Edit';
        }

        return '';
    }

    isRouteActiveContainsAny(paths: string[]): boolean {
        const currentUrl = this.router.url.split('?')[0].split('#')[0].replace(/\/+$/, '');

        return paths.some((path) => {
            const normalizedPath = path.replace(/\/+$/, '');
            return currentUrl.startsWith(normalizedPath);
        });
    }

    isRouteActiveExact(path: string): boolean {
        const currentUrl = this.router.url.split('?')[0].split('#')[0].replace(/\/+$/, '');
        const normalizedPath = path.replace(/\/+$/, '');
        return currentUrl === normalizedPath;
    }

    openLink(link: any): void {
        if (link.link) {
            this.router.navigate([link.link]);
        }
    }

    onLogout(): void {
        this.authService.logout();
    }

    settingToggleAccordion() {
        this.isSettingOpen = !this.isSettingOpen;
    }

    toggleSidebar() {
        this.isToggleOpen = !this.isToggleOpen;

        this.toggleClass(this.myContentElement, 'active');
        this.toggleClass(this.mySideBar, 'final-active');

        setTimeout(() => {
            this.calendarResizeService.triggerResize();
        }, 300);
    }

    toggleClass(elementRef: ElementRef, className: string) {
        if (elementRef) {
            const nativeElement = elementRef.nativeElement;

            if (nativeElement.classList.contains(className)) {
                this.renderer.removeClass(nativeElement, className);
            } else {
                this.renderer.addClass(nativeElement, className);
            }
        }
    }

    updateThemeBasedOnLocation(location?: string) {
        const theme = location?.toLowerCase() === 'us' ? 'US' : 'CANADA';
        document.body.setAttribute('data-theme', theme);
    }

    private getChild(activatedRoute: ActivatedRoute): ActivatedRoute {
        return activatedRoute.firstChild ? this.getChild(activatedRoute.firstChild) : activatedRoute;
    }
}
