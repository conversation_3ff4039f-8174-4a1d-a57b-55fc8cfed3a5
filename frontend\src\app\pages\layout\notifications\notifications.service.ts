import { Injectable } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class NotificationsService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/notifications', '/api/notifications');
    }

    getNotificationCount(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/Notifications/count', filterParam);
    }

    getNotifications(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/Notifications/count', filterParam);
    }

    notificationAction(notifications: any, isDeleteAction?: boolean): Observable<RestResponse> {
        const url = `/api/${isDeleteAction ? 'delete' : 'view'}/notifications`;
        return this.getRecords(url, notifications);
    }

    notificationAllAction(isDeleteAction?: boolean): Observable<RestResponse> {
        const url = `/api/${isDeleteAction ? 'delete' : 'view'}/all/notifications`;
        return this.getRecords(url);
    }
}
