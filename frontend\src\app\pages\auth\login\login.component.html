<div class="public-page-wrapper min-vh-100 display-flex-column login-bg">

  <div class="container-fluid ">
    <div class="row justify-content-md-center justify-content-xl-end ">
      <div class=" col-lg-5 col-md-6 display-flex-column public-page-left-section login-page">
        <div class="from-container">
          <div class="logo-section mb-4">
            <!-- Logo Image -->
            <div class="mb-3 text-center">
              <img src="/assets/images/logo.png" class="img-fluid app-logo" alt="Bees Express Logo" loading="eager">
            </div>
            <!-- Welcome Message -->

            <h2 class="welcome-label-text">Welcome to <span>{{"LOGIN.projectName" | translate}}</span></h2>

            <p>Log in to unlock your personalized experience.</p>
          </div>
          <!-- Login Form -->
          <form autocomplete="off" class="form-section mb-3 mb-md-4" novalidate #loginForm="ngForm">
            <!-- Email input field -->
            <div class="form-floating mb-3">
              <input class="form-control is-invalid" type="email" name="userEmail" #userEmail="ngModel"
                [(ngModel)]="loginData!.email" required placeholder="{{ 'USERS.Email' | translate }}"
                [ngClass]="{ 'is-invalid': !userEmail.valid && onClickValidation }" appEmailValidator />
              <label for="userEmail">{{ 'USERS.Email' | translate }}</label>
              <span class="input-right-icon">
                <img src="/assets/images/svg/email.svg" alt="Email icon" class="icon-file" />
              </span>
              <app-validation-message [field]="userEmail"
                [customPatternMessage]="'COMMON.REQUIRED_EMAIL_VALIDATION_MESSAGE' | translate"
                [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
            <!-- Password Input field -->
            <div class="form-floating mb-3 toggle-password-visible">
              <input class="form-control" type="password" name="userPassword" #userPassword="ngModel" [minlength]="8"
                [maxlength]="20" [(ngModel)]="loginData!.password" required="required"
                placeholder="{{ 'USERS.Password' | translate }}"
                [ngClass]="{ 'is-invalid': !userPassword.valid && onClickValidation }" />
              <label for="userPassword">{{ "PASSWORD" | translate }}</label>
              <app-validation-message [field]="userPassword"
                [onClickValidation]="onClickValidation"></app-validation-message>
              @if(loginData!.password && loginData!.password.length > 0){
              <i class="eye-icon bi bi-eye-slash" [ngClass]="{'eye-icon-left':!userPassword.valid && onClickValidation}"
                appTogglePasswordVisibility></i>
              }
            </div>
            <!-- Forgot password link -->
            <div class="text-end mb-3">
              <a class="forgot-password-label" [routerLink]="['/forgot/password']">{{"FORGOT PASSWORD" |
                translate}}</a>
            </div>
            <!-- Button -->
            <div class="action-button-container mt-2">
              <button class="btn btn-login custom-medium-button overflow-hidden btn-login w-100"
                (click)="login(loginForm.form)" appRippleEffect>
                {{"LOGIN.objName" | translate}}
              </button>
            </div>
          </form>
          <!-- Terms and conditions link -->
          <p class="term-condition-text mb-2">
            By signing in or creating an account, you are agreeing to our
            <a class="cursor-pointer" [routerLink]="['/terms-of-use']">{{"LOGIN.TermsOfUse" |translate}}</a>
            and <a class="cursor-pointer" [routerLink]="['/privacy-policy']">
              {{"LOGIN.PrivacyPolicy" |translate}}</a>
          </p>
        </div>
      </div>
      <!-- Right side login page content -->
      <div class="col-xl-8 col-lg-6 px-0 d-none  public-page-right-section ">
        <app-auth-right-section></app-auth-right-section>
      </div>
    </div>
  </div>
</div>