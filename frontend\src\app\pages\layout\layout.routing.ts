import { Routes } from '@angular/router';
import { COMMON_ROLES } from '../../config/role-permissions';
import { AuthGuard } from '../../shared/auth.guard';
import { LayoutComponent } from './layout.component';

export const LAYOUTROUTING: Routes = [
    {
        path: '',
        component: LayoutComponent,
        children: [
            {
                path: '',
                loadComponent: () => import('../dashboard/dashboard.component').then((m) => m.DashboardComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Dashboard',
                    breadcrumbs: [{ title: 'Dashboard', link: '/dashboard', active: true }],
                },
            },
            {
                path: 'my/profile',
                loadComponent: () => import('../profile/profile.component').then((m) => m.ProfileComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Profile',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Manage Profile', link: '/dashboard/my/profile', active: true },
                    ],
                },
            },
            {
                path: 'change-password',
                loadComponent: () =>
                    import('../change-password/change-password.component').then((m) => m.ChangePasswordComponent),
                data: {
                    title: 'Change Password',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/change-password', active: false },
                        { title: 'Change Password', link: '/dashboard/change-password', active: true },
                    ],
                },
            },
            {
                path: 'employees',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./employees/employees.component').then((m) => m.EmployeesComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Employees',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Manage Employees', link: '/dashboard/employees', active: true },
                    ],
                },
            },
            {
                path: 'employee/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./employees/edit/employee-edit.component').then((m) => m.EmployeeEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Employees',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Manage Employees', link: '/dashboard/employees', active: false },
                        { title: 'Employee', link: '/dashboard/employees', active: true },
                    ],
                },
            },
            // Customers layout

            // customers
            {
                path: 'customers',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./customer/customer.component').then((m) => m.CustomerComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Customers',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        {
                            title: 'Manage Customers',
                            link: '/dashboard/customers',
                            active: true,
                        },
                    ],
                },
            },

            {
                path: 'customer/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./customer/edit/customer-edit.component').then((m) => m.CustomerEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Customer',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        {
                            title: 'Customers',
                            link: '/dashboard/customers',
                            active: false,
                        },
                        { title: 'Customer', active: true },
                    ],
                },
            },

            // rate-sheets
            {
                path: 'rate-sheets',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./ratesheet/rate-sheet.component').then((m) => m.RateSheetComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage RateSheets',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'RateSheets', link: '/dashboard/rate-sheets', active: true },
                    ],
                },
            },
            {
                path: 'rate-sheet/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./ratesheet/edit/ratesheet-edit.component').then((m) => m.RateSheetEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage RateSheet',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'RateSheets', link: '/dashboard/rate-sheets', active: false },
                        { title: 'RateSheet', link: '/dashboard/rate-sheet', active: true },
                    ],
                },
            },

            // Quotation
            {
                path: 'quotations',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./quotation/quotation.component').then((m) => m.QuotationComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Quotations',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Quotations', link: '/dashboard/quotation', active: true },
                    ],
                },
            },
            {
                path: 'quotation/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./quotation/edit/quotation-edit.component').then((m) => m.QuotationEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Quotation',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Quotations', link: '/dashboard/quotations', active: false },
                        { title: 'Quotation', link: '/dashboard/quotation', active: true },
                    ],
                },
            },

            // Barcode
            {
                path: 'barcodes',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./barcode/barcode.component').then((m) => m.BarcodeComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Barcodes',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Barcodes', link: '/dashboard/barcode', active: true },
                    ],
                },
            },
            {
                path: 'barcode/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./barcode/edit/barcode-edit.component').then((m) => m.BarcodeEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Barcode',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Barcode', link: '/dashboard/barcode', active: false },
                        { title: 'Barcode', link: '/dashboard/barcode', active: true },
                    ],
                },
            },

            {
                path: 'shipments',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./shipment/shipment.component').then((m) => m.ShipmentComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Shipments',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Shipment', link: '/dashboard/shipment', active: true },
                    ],
                },
            },

            {
                path: 'shipment/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./shipment/edit/shipment-edit.component').then((m) => m.ShipmentEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Shipment',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Shipments', link: '/dashboard/shipments', active: false },
                        { title: 'Shipment', link: '/dashboard/shipment', active: true },
                    ],
                },
            },

            {
                path: 'fuel/receipt',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./fuel-receipt/fuel-receipt.component').then((m) => m.FuelReceiptComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Fuel Receipts',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Fuel Receipt', link: '/dashboard/fuel/receipt', active: true },
                    ],
                },
            },
            {
                path: 'contacts',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () => import('./contacts/contacts.component').then((m) => m.ContactsComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Contact Us',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Contacts', link: '/dashboard/contacts', active: true },
                    ],
                },
            },
            {
                path: 'notifications',
                loadComponent: () =>
                    import('./notifications/notifications.component').then((m) => m.NotificationsComponent),
                data: {
                    data: { roles: COMMON_ROLES },
                    title: 'Manage Notifications',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Notification', link: '/dashboard/notification', active: true },
                    ],
                },
            },
            {
                path: 'notification/edit/:id',
                loadComponent: () =>
                    import('./notifications/edit-notification-detail/edit-notification-detail.component').then(
                        (m) => m.EditNotificationDetailComponent,
                    ),
                data: {
                    data: { roles: COMMON_ROLES },
                    title: 'Manage Notification',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Notifications', link: '/dashboard/notifications', active: false },
                        { title: 'Notification', link: '/dashboard/notification', active: true },
                    ],
                },
            },

            {
                path: 'shipment/calender',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./shipment-calender/shipment-calender.component').then((m) => m.ShipmentCalenderComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Shipment Calendar',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: ' Shipment Calendar', link: '/dashboard/shipment/calender', active: true },
                    ],
                },
            },
            {
                path: 'vehicle/edit/:id',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./vehicle/edit/vehicle-edit.component').then((m) => m.VehicleEditComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Vehicle',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Manage vehicles', link: '/dashboard/vehicles', active: false },
                        { title: 'vehicle', link: '/dashboard/vehicles', active: true },
                    ],
                },
            },
            {
                path: 'driver/locations',
                canActivate: [AuthGuard.canActivate()],
                loadComponent: () =>
                    import('./driver-locations/driver-locations.component').then((m) => m.DriverLocationsComponent),
                data: {
                    roles: COMMON_ROLES,
                    title: 'Manage Driver Locations',
                    breadcrumbs: [
                        { title: 'Dashboard', link: '/dashboard', active: false },
                        { title: 'Driver Locations', link: '/dashboard/locations', active: true },
                    ],
                },
            },
        ],
    },
];
