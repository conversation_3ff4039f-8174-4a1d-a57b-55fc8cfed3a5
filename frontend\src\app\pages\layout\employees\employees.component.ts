// Angular Framework
import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-Party Libraries
import { NgbAccordionModule, NgbDatepickerModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { NgxMaskPipe, provideNgxMask } from 'ngx-mask';

// Application Core
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { Constant } from '../../../config/constants';

// Models
import { RestResponse } from '../../../models/common/auth.model';
import { Employees } from '../../../models/customer/employees';

// Services
import { LoadingService } from '../../../services/loading.service';
import { AuthService } from '../../../shared/services/auth.services';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';
import { StringUtilService } from '../../../utils/StringUtil.Service';

// Directives
import { RippleEffectDirective } from '../../../shared/directives/ripple-effect.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';

// Pipes
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { RoleTransformPipe } from '../../../shared/pipes/role-transform.pipe';

// Managers
import { NgSelectModule } from '@ng-select/ng-select';
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { EmployeesManager } from './employees.manager';

@Component({
    selector: 'app-users',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        DateFormatPipe,
        FormsModule,
        DelayedInputDirective,
        NgbDatepickerModule,
        NgbTooltipModule,
        NgxMaskPipe,
        NgSelectModule,
        NgbAccordionModule,
        RoleTransformPipe,
        RippleEffectDirective,
        RouterLink,
        StatusBadgeDirective,
        TranslateModule,
        NgSelectModule,
        TooltipEllipsisDirective,
    ],
    providers: [provideNgxMask()],
    templateUrl: './employees.component.html',
    styleUrl: './employees.component.scss',
})
export class EmployeesComponent extends BaseListServerSideComponent implements OnInit {
    @ViewChild('email') email!: TemplateRef<string>;
    @ViewChild('name') name!: TemplateRef<string>;
    @ViewChild('phoneNumber') phoneNumber!: TemplateRef<number>;
    @ViewChild('address') address!: TemplateRef<string>;
    @ViewChild('position') position!: TemplateRef<string>;
    @ViewChild('status') status!: TemplateRef<string>;
    @ViewChild('hireDate') hireDate!: TemplateRef<string>;
    @ViewChild('createdOn') createdOn!: TemplateRef<string>;
    @ViewChild('action') action!: TemplateRef<any>;

    // Data Arrays
    availableRoles: Array<any> = new Array<any>();
    employees!: Array<Employees>;

    searchText: string = '';
    fromDate: string | null = null;
    toDate: string | null = null;

    alertMessage = Constant.ALERT_MESSAGES;
    phoneMaskPattern = Constant.MASKS.PHONE_MASK;
    durationFilter = Constant.DURATION_FILTERS;
    statusList = Constant.STATUS;
    userRoles = Constant.EMPLOYEE_ROLES;
    resourceType: string = Constant.RESOURCE_TYPE.EMPLOYEE;

    constructor(
        protected employeesManager: EmployeesManager,
        protected override toastService: ToastService,
        public authService: AuthService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
        public stringUtilService: StringUtilService,
    ) {
        super(employeesManager, commonService, toastService, loadingService, router);
        this.fromDate = null;
        this.toDate = this.fromDate;
    }

    ngOnInit() {
        this.employees = new Array<Employees>();
        this.filterParam.fileName = this.resourceType;

        if (history.state.Filtering) {
            this.filterParam.filtering.createdWithinDays = history.state.Filtering;
        }

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.EMPLOYEES;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.EMPLOYEES;

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            name: this.name,
            email: this.email,
            phoneNumber: this.phoneNumber,
            address: this.address,
            position: this.position,
            status: this.status,
            hireDate: this.hireDate,
            createdOn: this.createdOn,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override onFetchCompleted() {
        this.employees = this.records.map((data) => Employees.fromResponse(data));
        super.onFetchCompleted();
    }

    updateEmployeeStatus(userRecord: any) {
        const message = `${userRecord?.isActive ? this.alertMessage.deActivateMsg : this.alertMessage.activateMsg} Employee?`;

        const data = {
            id: userRecord.id,
            isActive: userRecord.isActive,
        };

        this.commonService.confirmActionDialog(
            message,
            this.updateEmployeeStatusCallback.bind(this),
            data,
            !userRecord.isActive,
        );
    }

    async updateEmployeeStatusCallback(data: { id: string; isActive: boolean }) {
        let employee = new Employees();
        employee.isActive = !data.isActive;

        this.loadingService.show();
        this.employeesManager.updateEmployeeStatus(data.id, employee).subscribe({
            next: (response: RestResponse) => {
                this.toastService.success(response?.message);
                this.refreshRecord();
            },
            error: (error: any) => {
                this.toastService.error(error?.message);
            },
        });
    }
}
