{"version": 3, "sources": ["../../../../../node_modules/@googlemaps/js-api-loader/dist/index.mjs"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nvar fastDeepEqual = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqual);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n    LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n    LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n    LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n    LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n    /**\n     * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n     * using this library, instead the defaults are set by the Google Maps\n     * JavaScript API server.\n     *\n     * ```\n     * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n     * ```\n     */\n    constructor({ apiKey, authReferrerPolicy, channel, client, id = DEFAULT_ID, language, libraries = [], mapIds, nonce, region, retries = 3, url = \"https://maps.googleapis.com/maps/api/js\", version, }) {\n        this.callbacks = [];\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.apiKey = apiKey;\n        this.authReferrerPolicy = authReferrerPolicy;\n        this.channel = channel;\n        this.client = client;\n        this.id = id || DEFAULT_ID; // Do not allow empty string\n        this.language = language;\n        this.libraries = libraries;\n        this.mapIds = mapIds;\n        this.nonce = nonce;\n        this.region = region;\n        this.retries = retries;\n        this.url = url;\n        this.version = version;\n        if (Loader.instance) {\n            if (!isEqual(this.options, Loader.instance.options)) {\n                throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(Loader.instance.options)}`);\n            }\n            return Loader.instance;\n        }\n        Loader.instance = this;\n    }\n    get options() {\n        return {\n            version: this.version,\n            apiKey: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            id: this.id,\n            libraries: this.libraries,\n            language: this.language,\n            region: this.region,\n            mapIds: this.mapIds,\n            nonce: this.nonce,\n            url: this.url,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n    }\n    get status() {\n        if (this.errors.length) {\n            return LoaderStatus.FAILURE;\n        }\n        if (this.done) {\n            return LoaderStatus.SUCCESS;\n        }\n        if (this.loading) {\n            return LoaderStatus.LOADING;\n        }\n        return LoaderStatus.INITIALIZED;\n    }\n    get failed() {\n        return this.done && !this.loading && this.errors.length >= this.retries + 1;\n    }\n    /**\n     * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n     *\n     * @ignore\n     * @deprecated\n     */\n    createUrl() {\n        let url = this.url;\n        url += `?callback=__googleMapsCallback&loading=async`;\n        if (this.apiKey) {\n            url += `&key=${this.apiKey}`;\n        }\n        if (this.channel) {\n            url += `&channel=${this.channel}`;\n        }\n        if (this.client) {\n            url += `&client=${this.client}`;\n        }\n        if (this.libraries.length > 0) {\n            url += `&libraries=${this.libraries.join(\",\")}`;\n        }\n        if (this.language) {\n            url += `&language=${this.language}`;\n        }\n        if (this.region) {\n            url += `&region=${this.region}`;\n        }\n        if (this.version) {\n            url += `&v=${this.version}`;\n        }\n        if (this.mapIds) {\n            url += `&map_ids=${this.mapIds.join(\",\")}`;\n        }\n        if (this.authReferrerPolicy) {\n            url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n        }\n        return url;\n    }\n    deleteScript() {\n        const script = document.getElementById(this.id);\n        if (script) {\n            script.remove();\n        }\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     * @deprecated, use importLibrary() instead.\n     */\n    load() {\n        return this.loadPromise();\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     *\n     * @ignore\n     * @deprecated, use importLibrary() instead.\n     */\n    loadPromise() {\n        return new Promise((resolve, reject) => {\n            this.loadCallback((err) => {\n                if (!err) {\n                    resolve(window.google);\n                }\n                else {\n                    reject(err.error);\n                }\n            });\n        });\n    }\n    importLibrary(name) {\n        this.execute();\n        return google.maps.importLibrary(name);\n    }\n    /**\n     * Load the Google Maps JavaScript API script with a callback.\n     * @deprecated, use importLibrary() instead.\n     */\n    loadCallback(fn) {\n        this.callbacks.push(fn);\n        this.execute();\n    }\n    /**\n     * Set the script on document.\n     */\n    setScript() {\n        var _a, _b;\n        if (document.getElementById(this.id)) {\n            // TODO wrap onerror callback for cases where the script was loaded elsewhere\n            this.callback();\n            return;\n        }\n        const params = {\n            key: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            libraries: this.libraries.length && this.libraries,\n            v: this.version,\n            mapIds: this.mapIds,\n            language: this.language,\n            region: this.region,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n        // keep the URL minimal:\n        Object.keys(params).forEach(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (key) => !params[key] && delete params[key]);\n        if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n            // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n            // which also sets the base url, the id, and the nonce\n            /* eslint-disable */\n            ((g) => {\n                // @ts-ignore\n                let h, a, k, p = \"The Google Maps JavaScript API\", c = \"google\", l = \"importLibrary\", q = \"__ib__\", m = document, b = window;\n                // @ts-ignore\n                b = b[c] || (b[c] = {});\n                // @ts-ignore\n                const d = b.maps || (b.maps = {}), r = new Set(), e = new URLSearchParams(), u = () => \n                // @ts-ignore\n                h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n                    var _a;\n                    yield (a = m.createElement(\"script\"));\n                    a.id = this.id;\n                    e.set(\"libraries\", [...r] + \"\");\n                    // @ts-ignore\n                    for (k in g)\n                        e.set(k.replace(/[A-Z]/g, (t) => \"_\" + t[0].toLowerCase()), g[k]);\n                    e.set(\"callback\", c + \".maps.\" + q);\n                    a.src = this.url + `?` + e;\n                    d[q] = f;\n                    a.onerror = () => (h = n(Error(p + \" could not load.\")));\n                    // @ts-ignore\n                    a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n                    m.head.append(a);\n                })));\n                // @ts-ignore\n                d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : (d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)));\n            })(params);\n            /* eslint-enable */\n        }\n        // While most libraries populate the global namespace when loaded via bootstrap params,\n        // this is not the case for \"marker\" when used with the inline bootstrap loader\n        // (and maybe others in the future). So ensure there is an importLibrary for each:\n        const libraryPromises = this.libraries.map((library) => this.importLibrary(library));\n        // ensure at least one library, to kick off loading...\n        if (!libraryPromises.length) {\n            libraryPromises.push(this.importLibrary(\"core\"));\n        }\n        Promise.all(libraryPromises).then(() => this.callback(), (error) => {\n            const event = new ErrorEvent(\"error\", { error }); // for backwards compat\n            this.loadErrorCallback(event);\n        });\n    }\n    /**\n     * Reset the loader state.\n     */\n    reset() {\n        this.deleteScript();\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.onerrorEvent = null;\n    }\n    resetIfRetryingFailed() {\n        if (this.failed) {\n            this.reset();\n        }\n    }\n    loadErrorCallback(e) {\n        this.errors.push(e);\n        if (this.errors.length <= this.retries) {\n            const delay = this.errors.length * Math.pow(2, this.errors.length);\n            console.error(`Failed to load Google Maps script, retrying in ${delay} ms.`);\n            setTimeout(() => {\n                this.deleteScript();\n                this.setScript();\n            }, delay);\n        }\n        else {\n            this.onerrorEvent = e;\n            this.callback();\n        }\n    }\n    callback() {\n        this.done = true;\n        this.loading = false;\n        this.callbacks.forEach((cb) => {\n            cb(this.onerrorEvent);\n        });\n        this.callbacks = [];\n    }\n    execute() {\n        this.resetIfRetryingFailed();\n        if (this.loading) {\n            // do nothing but wait\n            return;\n        }\n        if (this.done) {\n            this.callback();\n        }\n        else {\n            // short circuit and warn if google.maps is already loaded\n            if (window.google && window.google.maps && window.google.maps.version) {\n                console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" +\n                    \"This may result in undesirable behavior as options and script parameters may not match.\");\n                this.callback();\n                return;\n            }\n            this.loading = true;\n            this.setScript();\n        }\n    }\n}\n\nexport { DEFAULT_ID, Loader, LoaderStatus };\n\n"], "mappings": ";;;AAiBA,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAOA,SAAS,wBAAyB,GAAG;AACpC,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AACjG;AAMA,IAAI,gBAAgB,SAAS,MAAM,GAAG,GAAG;AACvC,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,QAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,aAAO;AAAA,IACT;AAIA,QAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,QAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,QAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAEjF,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,MAAM,KAAK,CAAC;AAEhB,UAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;AAAA,IACrC;AAEA,WAAO;AAAA,EACT;AAGA,SAAO,MAAI,KAAK,MAAI;AACtB;AAEA,IAAI,UAAuB,wBAAwB,aAAa;AAiBhE,IAAM,aAAa;AAInB,IAAI;AAAA,CACH,SAAUA,eAAc;AACrB,EAAAA,cAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AAChD,EAAAA,cAAaA,cAAa,SAAS,IAAI,CAAC,IAAI;AAC5C,EAAAA,cAAaA,cAAa,SAAS,IAAI,CAAC,IAAI;AAC5C,EAAAA,cAAaA,cAAa,SAAS,IAAI,CAAC,IAAI;AAChD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAoBtC,IAAM,SAAN,MAAM,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUT,YAAY,EAAE,QAAQ,oBAAoB,SAAS,QAAQ,KAAK,YAAY,UAAU,YAAY,CAAC,GAAG,QAAQ,OAAO,QAAQ,UAAU,GAAG,MAAM,2CAA2C,QAAS,GAAG;AACnM,SAAK,YAAY,CAAC;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,SAAS;AACd,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,KAAK,MAAM;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,UAAU;AACf,QAAI,QAAO,UAAU;AACjB,UAAI,CAAC,QAAQ,KAAK,SAAS,QAAO,SAAS,OAAO,GAAG;AACjD,cAAM,IAAI,MAAM,2DAA2D,KAAK,UAAU,KAAK,OAAO,CAAC,QAAQ,KAAK,UAAU,QAAO,SAAS,OAAO,CAAC,EAAE;AAAA,MAC5J;AACA,aAAO,QAAO;AAAA,IAClB;AACA,YAAO,WAAW;AAAA,EACtB;AAAA,EACA,IAAI,UAAU;AACV,WAAO;AAAA,MACH,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,IAAI,KAAK;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,KAAK,KAAK;AAAA,MACV,oBAAoB,KAAK;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,IAAI,SAAS;AACT,QAAI,KAAK,OAAO,QAAQ;AACpB,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,MAAM;AACX,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,SAAS;AACd,aAAO,aAAa;AAAA,IACxB;AACA,WAAO,aAAa;AAAA,EACxB;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK,QAAQ,CAAC,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,UAAU;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACR,QAAI,MAAM,KAAK;AACf,WAAO;AACP,QAAI,KAAK,QAAQ;AACb,aAAO,QAAQ,KAAK,MAAM;AAAA,IAC9B;AACA,QAAI,KAAK,SAAS;AACd,aAAO,YAAY,KAAK,OAAO;AAAA,IACnC;AACA,QAAI,KAAK,QAAQ;AACb,aAAO,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,KAAK,UAAU,SAAS,GAAG;AAC3B,aAAO,cAAc,KAAK,UAAU,KAAK,GAAG,CAAC;AAAA,IACjD;AACA,QAAI,KAAK,UAAU;AACf,aAAO,aAAa,KAAK,QAAQ;AAAA,IACrC;AACA,QAAI,KAAK,QAAQ;AACb,aAAO,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,KAAK,SAAS;AACd,aAAO,MAAM,KAAK,OAAO;AAAA,IAC7B;AACA,QAAI,KAAK,QAAQ;AACb,aAAO,YAAY,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IAC5C;AACA,QAAI,KAAK,oBAAoB;AACzB,aAAO,yBAAyB,KAAK,kBAAkB;AAAA,IAC3D;AACA,WAAO;AAAA,EACX;AAAA,EACA,eAAe;AACX,UAAM,SAAS,SAAS,eAAe,KAAK,EAAE;AAC9C,QAAI,QAAQ;AACR,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACH,WAAO,KAAK,YAAY;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACV,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,WAAK,aAAa,CAAC,QAAQ;AACvB,YAAI,CAAC,KAAK;AACN,kBAAQ,OAAO,MAAM;AAAA,QACzB,OACK;AACD,iBAAO,IAAI,KAAK;AAAA,QACpB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,cAAc,MAAM;AAChB,SAAK,QAAQ;AACb,WAAO,OAAO,KAAK,cAAc,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,IAAI;AACb,SAAK,UAAU,KAAK,EAAE;AACtB,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACR,QAAI,IAAI;AACR,QAAI,SAAS,eAAe,KAAK,EAAE,GAAG;AAElC,WAAK,SAAS;AACd;AAAA,IACJ;AACA,UAAM,SAAS;AAAA,MACX,KAAK,KAAK;AAAA,MACV,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK,UAAU,UAAU,KAAK;AAAA,MACzC,GAAG,KAAK;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,oBAAoB,KAAK;AAAA,IAC7B;AAEA,WAAO,KAAK,MAAM,EAAE;AAAA;AAAA,MAEpB,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,OAAO,OAAO,GAAG;AAAA,IAAC;AAC3C,QAAI,GAAG,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAInL,OAAC,CAAC,MAAM;AAEJ,YAAI,GAAG,GAAG,GAAG,IAAI,kCAAkC,IAAI,UAAU,IAAI,iBAAiB,IAAI,UAAU,IAAI,UAAU,IAAI;AAEtH,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;AAErB,cAAM,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,oBAAI,IAAI,GAAG,IAAI,IAAI,gBAAgB,GAAG,IAAI;AAAA;AAAA,UAEjF,MAAM,IAAI,IAAI,QAAQ,CAAC,GAAG,MAAM,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACzE,gBAAIC;AACJ,kBAAO,IAAI,EAAE,cAAc,QAAQ;AACnC,cAAE,KAAK,KAAK;AACZ,cAAE,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE;AAE9B,iBAAK,KAAK;AACN,gBAAE,IAAI,EAAE,QAAQ,UAAU,CAAC,MAAM,MAAM,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;AACpE,cAAE,IAAI,YAAY,IAAI,WAAW,CAAC;AAClC,cAAE,MAAM,KAAK,MAAM,MAAM;AACzB,cAAE,CAAC,IAAI;AACP,cAAE,UAAU,MAAO,IAAI,EAAE,MAAM,IAAI,kBAAkB,CAAC;AAEtD,cAAE,QAAQ,KAAK,WAAWA,MAAK,EAAE,cAAc,eAAe,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,UAAU;AACnH,cAAE,KAAK,OAAO,CAAC;AAAA,UACnB,CAAC,CAAC;AAAA;AAEF,UAAE,CAAC,IAAI,QAAQ,KAAK,IAAI,+BAA+B,CAAC,IAAK,EAAE,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,MAAM,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AAAA,MAC7H,GAAG,MAAM;AAAA,IAEb;AAIA,UAAM,kBAAkB,KAAK,UAAU,IAAI,CAAC,YAAY,KAAK,cAAc,OAAO,CAAC;AAEnF,QAAI,CAAC,gBAAgB,QAAQ;AACzB,sBAAgB,KAAK,KAAK,cAAc,MAAM,CAAC;AAAA,IACnD;AACA,YAAQ,IAAI,eAAe,EAAE,KAAK,MAAM,KAAK,SAAS,GAAG,CAAC,UAAU;AAChE,YAAM,QAAQ,IAAI,WAAW,SAAS,EAAE,MAAM,CAAC;AAC/C,WAAK,kBAAkB,KAAK;AAAA,IAChC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,wBAAwB;AACpB,QAAI,KAAK,QAAQ;AACb,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA,EACA,kBAAkB,GAAG;AACjB,SAAK,OAAO,KAAK,CAAC;AAClB,QAAI,KAAK,OAAO,UAAU,KAAK,SAAS;AACpC,YAAM,QAAQ,KAAK,OAAO,SAAS,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;AACjE,cAAQ,MAAM,kDAAkD,KAAK,MAAM;AAC3E,iBAAW,MAAM;AACb,aAAK,aAAa;AAClB,aAAK,UAAU;AAAA,MACnB,GAAG,KAAK;AAAA,IACZ,OACK;AACD,WAAK,eAAe;AACpB,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ,CAAC,OAAO;AAC3B,SAAG,KAAK,YAAY;AAAA,IACxB,CAAC;AACD,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,UAAU;AACN,SAAK,sBAAsB;AAC3B,QAAI,KAAK,SAAS;AAEd;AAAA,IACJ;AACA,QAAI,KAAK,MAAM;AACX,WAAK,SAAS;AAAA,IAClB,OACK;AAED,UAAI,OAAO,UAAU,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,SAAS;AACnE,gBAAQ,KAAK,uJACgF;AAC7F,aAAK,SAAS;AACd;AAAA,MACJ;AACA,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACJ;", "names": ["LoaderStatus", "_a"]}