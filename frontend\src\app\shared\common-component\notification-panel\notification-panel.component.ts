import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-notification-panel',
    standalone: true,
    imports: [RouterLink, TranslateModule],
    templateUrl: './notification-panel.component.html',
    styleUrl: './notification-panel.component.scss',
})
export class NotificationPanelComponent {
    @Input() groupedNotifications: { label: string; items: any[] }[] = [];
} 