import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
export class Vehicle extends BaseModel {
    tenantId!: number;
    slug!: string;
    name!: string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
    }

    static fromResponse(data: any): Vehicle {
        return { ...data };
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.name = this.trimMe(this.name);
        return this;
    }
}
