<div class="site-customer-main-container">
    <div class="profile-wrapper">
        <div class="row w-100">
            <div class="col-md-12">
                <!-- Tabs Section -->
                <div class="profile-form-wrapper mt-3 ">
                    <div class="detail-page-container pt-2 tabs-wrapper">
                        <div class="custom-tab mx-2" id="otherDetails-tab" (click)="onClickTab('BASIC_INFO')"
                            [ngClass]="{ active: tabView === 'BASIC_INFO' }">
                            <span class="tab-icon">
                                <img src="/assets/images/svg/basic-info.svg" alt="">
                            </span>
                            <span class="d-flex flex-nowrap"> {{"PROFILE.Basic_information"| translate}}</span>

                        </div>
                        <div class="custom-tab" id="userDetails-tab" (click)="onClickTab('CHANGE_PASSWORD')"
                            [ngClass]="{ active: tabView === 'CHANGE_PASSWORD' }">
                            <span class="tab-icon">
                                <img src="/assets/images/svg/change-password.svg" alt="">
                            </span>
                            {{"PROFILE.change_password"| translate}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs Contents -->
        <div class="tab-content-wrap mt-3 px-md-5">
            <div class="card card-background">
                <!-- Basic Information -->
                @if (tabView === 'BASIC_INFO') {
                <div class="card-body bg-white p-3 p-md-5 d-flex justify-content-center">
                    <form autocomplete="off" novalidate="novalidate" #myProfileForm="ngForm"
                        class="col-md-12 col-xl-10 col-xxl-4">
                        <div class="row">
                            <!-- Row-1 -->
                            <div class="col-12 mb-14">
                                <div class="form-floating form-group">
                                    <input class="form-control" type="text" name="firstName" #firstName="ngModel"
                                        [(ngModel)]="user!.firstName" required="required"
                                        placeholder="{{ 'USERS.FirstName' | translate }}"
                                        [ngClass]="{ 'is-invalid': !firstName.valid && onClickValidation }" />
                                    <label for="firstName">{{"FIRST_NAME" | translate}}</label>
                                </div>
                                <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                            <div class="col-12 mb-14">
                                <div class="form-floating form-group ">
                                    <input class="form-control" type="text" name="lastName" #lastName="ngModel"
                                        [(ngModel)]="user!.lastName" required="required"
                                        placeholder="{{ 'USERS.lastName' | translate }}"
                                        [ngClass]="{ 'is-invalid': !lastName.valid && onClickValidation }" />
                                    <label for="lastName">{{"LAST_NAME" | translate}}</label>
                                </div>
                                <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                            <div class="col-12 mb-14">
                                <div class="form-floating form-group ">
                                    <input class="form-control" type="email" name="userEmail" #userEmail="ngModel"
                                        [(ngModel)]="user!.email" placeholder="{{ 'LOGIN.EMAIL' | translate }}"
                                        disabled />
                                    <label for="userEmail">{{"USERS.Email" | translate}}</label>
                                </div>
                                <app-validation-message [field]="userEmail" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                            <div class="col-12 mb-2">
                                <app-dial-code-input [(countryCode)]="user.countryCode" [(number)]="user.phoneNumber"
                                    [required]="true" [labelName]="'Address.phoneNumber'"
                                    [onClickValidation]="onClickValidation" fieldName="phoneNumber"
                                    nameCode="countryCode"></app-dial-code-input>
                            </div>
                            <div class="clearfix d-none d-md-block"></div>

                            <div class="col-md-12 custom-buttons-container mb-0">
                                <button class="btn cancel-button" appRippleEffect type="button"
                                    [routerLink]="['/dashboard']">
                                    {{ "COMMON.CANCEL" | translate }}
                                </button>
                                <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                    (click)="updateMyProfile(myProfileForm.form)">
                                    {{ "COMMON.SAVE" | translate }}
                                </button>
                                <div class="clearfix"></div>
                            </div>

                        </div>
                    </form>
                </div>
                }

                <!-- Change password tabs -->
                @if (tabView === 'CHANGE_PASSWORD') {
                <app-change-password class="change-password-container"></app-change-password>
                }
            </div>
        </div>
    </div>
</div>