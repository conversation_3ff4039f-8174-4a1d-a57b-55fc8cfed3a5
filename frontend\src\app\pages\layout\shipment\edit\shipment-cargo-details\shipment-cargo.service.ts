import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../../../../config/base.service';
import { FilterParam } from '../../../../../models/common/filter-param';

// services

@Injectable({
    providedIn: 'root',
})
export class ShipmentCargoService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/shipmentItem', '/api/shipmentItems');
    }

    getFreightAmount(filterParam: FilterParam) {
        return this.getRecords('/api/shipmentitem/ratesheet/weight/charges', filterParam);
    }
}
