import { Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { BarcodeService } from './barcode.service';

@Injectable({
    providedIn: 'root',
})
export class BarcodeManager extends BaseManager {
    constructor(
        protected barcodeService: BarcodeService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(barcodeService, loadingService, toastService);
    }

    getBarCodes(data: any): Observable<RestResponse> {
        return this.barcodeService.getBarCodes(data).pipe(map((response: RestResponse) => response));
    }

    downloadBarcodePDfFile(filterParam: FilterParam) {
        return this.barcodeService.downloadBarcodePDfFile(filterParam).pipe(
            map((response: RestResponse) => response),
            catchError((error) => {
                this.onFailure(error);
                return of({ status: error.status, message: error.message, data: error } as RestResponse);
            }),
        );
    }

    markBarcodesPrinted(filterParam: FilterParam) {
        return this.barcodeService.markBarcodesPrinted(filterParam).pipe(map((response: RestResponse) => response));
    }
}
