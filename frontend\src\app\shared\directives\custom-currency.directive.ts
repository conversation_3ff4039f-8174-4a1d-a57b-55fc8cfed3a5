import { Directive, ElementRef, HostListener, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Directive({
    selector: '[appCurrencyFormatter]',
    standalone: true,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => CurrencyFormatterDirective),
            multi: true,
        },
    ],
})
export class CurrencyFormatterDirective implements ControlValueAccessor {
    private onChange = (value: any) => {};
    private onTouched = () => {};
    private lastRawValue = '';

    constructor(private readonly el: ElementRef<HTMLInputElement>) {}

    @HostListener('input', ['$event.target.value'])
    onInput(value: string) {
        const raw = value.replace(/[^0-9.]/g, '');
        const dotIndex = raw.indexOf('.');

        let processed = raw;

        if (dotIndex >= 0) {
            const integerPart = raw.substring(0, dotIndex);
            let decimalPart = raw.substring(dotIndex + 1);

            // Limit decimal digits to 2
            decimalPart = decimalPart.slice(0, 2);

            processed = integerPart + '.' + decimalPart;
        }

        this.lastRawValue = processed;
        const numericValue = processed ? parseFloat(processed) : null;
        this.onChange(numericValue);
        this.el.nativeElement.value = this.formatDisplay(processed, false);
    }

    @HostListener('blur')
    onBlur() {
        if (!this.lastRawValue) {
            this.el.nativeElement.value = '';
            return;
        }

        const parsed = parseFloat(this.lastRawValue);
        const rounded = parsed ? parsed.toFixed(2) : '0.00';
        this.lastRawValue = rounded;
        this.onChange(parseFloat(rounded));
        this.el.nativeElement.value = this.formatDisplay(rounded, true);
        this.onTouched();
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.el.nativeElement.disabled = isDisabled;
    }

    writeValue(value: any): void {
        if (value === null || value === undefined) {
            this.el.nativeElement.value = '';
            this.lastRawValue = '';
        } else {
            // Ensure we store the number as a string for formatting
            this.lastRawValue = typeof value === 'number' ? value.toString() : value;
            this.el.nativeElement.value = this.formatDisplay(this.lastRawValue, true);
        }
    }

    private formatDisplay(value: string | number, padDecimals: boolean): string {
        if (value === null || value === undefined || value === '') return '';

        const strValue = value.toString();
        const [intPart, decPart = ''] = strValue.split('.');
        const formattedInt = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        if (strValue.endsWith('.') && decPart === '') {
            return `$ ${formattedInt}.`; // user typed trailing dot
        }

        if (padDecimals) {
            // pad to exactly 2 decimal places (change to 3 if you prefer)
            const padded = (decPart || '').padEnd(2, '0');
            return `$ ${formattedInt}.${padded}`;
        }

        if (decPart !== '') {
            return `$ ${formattedInt}.${decPart}`;
        }

        return `$ ${formattedInt}`;
    }

    private restorePreviousValue() {
        this.el.nativeElement.value = this.formatDisplay(this.lastRawValue, false);
    }
}
