@import '../../variables.scss';

.page-wrapper {
    .public-page-wrapper {
        position: relative;

        &.login-bg {
            background: url(../images/login-section/login-backg.webp);
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;

            .login-page {
                .from-container {
                    padding: 40px 20px !important;
                    background: rgba(0, 0, 0, 0.7);
                    border-radius: 30px;

                    .logo-section {
                        .app-logo {
                            max-width: 220px;

                            @media (max-width: 575px) {
                                max-width: 150px;
                            }
                        }

                        .welcome-label-text {
                            color: $white-color !important;
                        }

                        p {
                            color: $white-color !important;
                            word-break: keep-all;
                        }
                    }

                    .form-section {
                        .form-floating>.form-control:focus~label {
                            background: var(--primary-color) !important;
                            color: $black-color !important;
                        }

                        .form-floating>.form-control:focus~label,
                        .form-floating>.form-control:not(:placeholder-shown)~label {
                            background: $white-color;
                            color: $black-color;
                        }

                        .form-floating {
                            .form-control {
                                background: transparent;
                                color: $white-color !important;
                                border: 2px solid rgba(250, 250, 250, 0.4);
                            }

                            .form-control:not(.question-form-control):focus,
                            .form-select:focus {
                                border: 2px solid var(--primary-color) !important;
                            }

                            label {
                                background: transparent;
                                color: rgba(250, 250, 250, 0.7);
                                border-radius: 12px;
                            }
                        }

                        .forgot-password-label {
                            color: $white-color !important;

                            &:hover {
                                color: var(--primary-color) !important;
                            }
                        }
                    }

                    .term-condition-text {
                        color: $white-color !important;

                        .cursor-pointer {
                            color: $white-color !important;

                            &:hover {
                                color: var(--primary-color) !important;
                            }
                        }
                    }
                }
            }
        }

        .public-page-left-section {
            &.login-page {
                .from-container {
                    @include flex-column-center-vertical;

                    max-width: 100%;
                    margin: auto;
                    text-align: center;
                    padding: 0 10px;

                    @media (min-width: 450px) {
                        max-width: 450px;
                    }

                    .logo-section {
                        .welcome-label-text {
                            font-size: 24px;
                            letter-spacing: 0;
                            color: $black-color;
                            font-weight: 300;
                            line-height: normal;

                            span {
                                font-weight: $font-weight-600;
                            }

                            @media (min-width: 767px) {
                                font-size: 30px;
                            }
                        }

                        p {
                            color: #5f5f5f;
                        }
                    }

                    .form-section {
                        .form-floating {
                            .input-right-icon {
                                position: absolute;
                                top: 14px;
                                right: 1.3rem;
                                height: max-content;

                                .icon-file {
                                    max-width: 24px;
                                }
                            }
                        }

                        .forgot-password-label {
                            color: $black-color;
                            font-weight: $font-weight-600;
                            font-size: $font-size-14;
                        }
                    }

                    .term-condition-text {
                        color: $black-color;
                        word-break: break-word;
                        line-height: 26px;

                        a {
                            color: $black-color;
                            font-weight: $font-weight-600;

                            strong {
                                font-weight: $font-weight-600;
                            }

                            &:hover {
                                color: var(--primary-color);
                            }
                        }
                    }
                }

                .copy-right-text {
                    color: $body-color;
                    padding: 11px 15px;
                    font-weight: 400;
                    font-size: 12px;

                    span {
                        font-weight: $font-weight-700;
                    }
                }
            }
        }

        .public-page-right-section {
            padding: 0;
            height: 100%;
            align-content: center;
            background-color: var(--login-color);

            &.login-page {
                background-color: var(--login-color);
            }

            .content {
                @include flex-column-center;
                text-align: center;

                h2 {
                    line-height: 3rem;
                    font-weight: $font-weight-600;
                }

                p {
                    line-height: 1.5rem;
                }
            }

            img {
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center bottom;
                position: relative;
                width: 70%;
            }
        }
    }
}