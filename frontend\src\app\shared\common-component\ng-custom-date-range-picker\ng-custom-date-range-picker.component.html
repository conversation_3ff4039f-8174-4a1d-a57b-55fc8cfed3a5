<div class="col-12">
    <div class="dp-hidden position-absolute">
        <div class="input-group">
            <input name="datepicker" class="form-control ms-2" ngbDatepicker #datepicker="ngbDatepicker"
                datepickerClass="date-picker-range-cls" [autoClose]="'outside'"
                (dateSelect)="onDateSelection($event, datepicker)" [displayMonths]="displayMonths"
                [footerTemplate]="footerTemplate" [dayTemplate]="t" outsideDays="hidden" [startDate]="fromDate!"
                [placement]="isMobile ? 'bottom-start' : placement" tabindex="-1" />
            <ng-template #t let-date let-focused="focused">
                <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)"
                    [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
                    (mouseleave)="hoveredDate = null">
                    {{ date.day }}
                </span>
            </ng-template>
        </div>
    </div>
    <div [ngClass]="this.customClass ?? 'margin-bottom-12'">
        <div class="form-floating input-group custom-range-date-picker" [ngClass]="this.customZIndex">
            <input #dpFromDate class="form-control z-index-4 pe-none" placeholder="yyyy-mm-dd" name="dpFromDate"
                #Date="ngModel" [disabled]="readOnly" [ngModel]="combinedDates"
                [ngClass]="{'is-invalid':!Date.valid && isRequired && onClickValidation,'cursor-not-allowed': readOnly}"
                [required]="isRequired" placeholder="{{labelName | translate}}"
                [ngClass]="{'z-index-5': !!this.customZIndex}" (click)="datepicker.toggle()"
                (input)="fromDate = validateInput(fromDate, dpFromDate.value)" />
            <label for="date" [ngClass]="{'z-index-5': !!this.customZIndex}" class="z-index-4">{{labelName |
                translate}}</label>
            <!-- Calendar button -->
            <button class="btn btn-outline-secondary bi bi-calendar3 calender-button-cls"
                [ngClass]="{'is-invalid':!Date.valid && isRequired && onClickValidation}" (click)="datepicker.toggle()"
                type="button">
            </button>
        </div>
    </div>

    <ng-template #footerTemplate>
        <hr class="my-0" />
        <button class="btn clear-button-color btn-sm m-2 float-end" (click)="clearButton(datepicker)">Clear</button>
    </ng-template>

    <app-validation-message [field]="Date" [onClickValidation]="onClickValidation"></app-validation-message>
</div>