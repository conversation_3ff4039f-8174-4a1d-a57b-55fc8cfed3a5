// Angular Core Modules
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-party Modules
import { TranslateModule } from '@ngx-translate/core';

// Services
import { FormHandlerManager } from '../../../managers/form-handler.manager';
import { LoginService } from '../../../services/login.service';
import { ToastService } from '../../../shared/services/toast.service';

// Models & Utils
import { Profile } from '../../../models/access/profile';
import { CommonUtil } from '../../../shared/common.util';

// Components & Directives
import { ValidationMessageComponent } from '../../../shared/common-component/validation-message/validation-message.component';
import { TogglePasswordVisibilityModule } from '../../../shared/directives/toggle-password-visibility.directive';
import { AuthRightSectionComponent } from '../auth-right-section/auth-right-section.component';

@Component({
    selector: 'app-recover-password',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        RouterLink,
        TogglePasswordVisibilityModule,
        TranslateModule,
        ValidationMessageComponent,
        AuthRightSectionComponent,
    ],
    templateUrl: './recover-password.component.html',
    styleUrls: ['./recover-password.component.scss'], // Corrected "styleUrl" to "styleUrls"
})
export class RecoverPasswordComponent implements OnInit {
    onClickValidation: boolean;
    resetPasswordData!: Profile;
    showNewPassword = false;
    showConfirmPassword = false;
    queryParams: any;
    recoveryId!: string;
    isCreatePasswordPage: boolean = false;

    headerText: string = '';

    constructor(
        public commonUtil: CommonUtil,
        private readonly toastService: ToastService,
        private readonly route: ActivatedRoute,
        private readonly router: Router,
        private readonly loginService: LoginService,
        private readonly formHandlerManager: FormHandlerManager,
    ) {
        this.onClickValidation = false;
    }

    ngOnInit(): void {
        this.resetPasswordData = new Profile();
        this.route.queryParams.subscribe((params) => {
            this.resetPasswordData.token = encodeURIComponent(params['token']);
            this.resetPasswordData.uniqueCode = this.route.snapshot.params['ucode'];
            if (CommonUtil.isNullOrUndefined(this.resetPasswordData.token) || this.resetPasswordData.token === '') {
                this.router.navigate(['404']);
            }
        });
        this.setHeader();
    }

    processPasswordReset(form: { valid: boolean }) {
        if (!form.valid) {
            this.onClickValidation = true;
            return;
        }

        if (this.resetPasswordData.confirmPassword !== this.resetPasswordData.password) {
            this.onClickValidation = true;
            this.toastService.error("Password doesn't matches");
            return;
        }

        this.formHandlerManager.handleFormSubmission(
            this.loginService.recoverPassword(this.resetPasswordData),
            (response) => {
                this.toastService.success(response.message);
                this.router.navigate(['/login']);
            },
        );
    }

    setHeader() {
        if (this.route.snapshot.routeConfig?.path === 'account/recover/:ucode') {
            this.headerText = 'Reset Password';
        } else {
            this.headerText = 'Create Password';
        }
    }
}
