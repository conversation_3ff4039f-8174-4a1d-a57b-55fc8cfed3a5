import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormsModule, NgModel } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// third party import
import { NgbDateAdapter, NgbDateParserFormatter, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

// service import
import { CustomAdapterService } from './custom-adapter.service';
import { CustomDateParserFormatter } from './custom-date-parser-formatter.service';

// common component import
import { ValidationMessageComponent } from '../validation-message/validation-message.component';

@Component({
    selector: 'app-ng-custom-date-picker',
    standalone: true,
    imports: [ValidationMessageComponent, NgbDatepickerModule, FormsModule, CommonModule, TranslateModule],
    templateUrl: './ng-custom-date-picker.component.html',
    styleUrls: ['./ng-custom-date-picker.component.scss'],
    providers: [
        { provide: NgbDateAdapter, useClass: CustomAdapterService },
        { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
    ],
})
export class NgCustomDatePickerComponent {
    @Input() readOnly: boolean = false;
    @Input() labelName!: string;
    @Input() onClickValidation!: boolean;
    @Input() selectedDate!: string | null;
    @Input() customClass?: string;
    @Input() customZIndex?: string;
    @Input() isRequired: boolean = false;
    @Input() minDate: string | null = null;
    @Input() disabled: boolean = false;
    @Input() clearFooterAction!: boolean;
    @Output() setDate = new EventEmitter<string | null>(); // Output to notify parent of changes
    @Output() datePickerClosed = new EventEmitter<boolean>();
    @Output() datePickerOpened = new EventEmitter<boolean>();

    @ViewChild('Date') dateModel!: NgModel;

    minimumDate: any;

    constructor(
        protected dateAdapter: NgbDateAdapter<string>,
        protected cdRef: ChangeDetectorRef,
    ) {}

    ngOnInit() {
        if (this.minDate) {
            let dateString = this.minDate;

            // Check if the date format is ISO (contains 'T')
            if (dateString.includes('T')) {
                // Extract only the date portion (YYYY-MM-DD) from ISO string
                dateString = dateString.split('T')[0];
            }

            // Split the date string into year, month, and day
            const dated = dateString.split('-');

            // Convert to the expected date object format
            this.minimumDate = {
                year: +dated[0],
                month: +dated[1],
                day: +dated[2],
            };
        }
    }

    ngAfterViewInit() {
        this.cdRef.detectChanges();
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.cdRef.detectChanges();
    }

    onDateSelect(event: any) {
        const getDate = this.dateAdapter.toModel(event); // Adapt date if necessary
        this.selectedDate = getDate;
        this.setDate.emit(getDate); // Emit the selected date
        this.updateValidation(); // Update validation state
    }

    // Clear the selected date
    clearButton(datepicker: any) {
        this.selectedDate = null; // Set the date to null
        this.setDate.emit(this.selectedDate); // Emit null to the parent
        this.updateValidation();
        datepicker.close();
    }

    private updateValidation() {
        if (this.dateModel) {
            this.dateModel.control.setValue(this.selectedDate); // Update ngModel
            this.dateModel.control.updateValueAndValidity(); // Ensure validation state is updated
        }
    }

    datePickerClose() {
        this.datePickerClosed.emit(true);
    }

    datePickerOpen() {
        this.datePickerOpened.emit(true);
    }
}
