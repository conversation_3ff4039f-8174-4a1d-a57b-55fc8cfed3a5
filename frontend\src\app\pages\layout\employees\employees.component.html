<div class="main-site-container" [hidden]="request.loadEditPage">
    <div class="position-relative mt-1">
        <div class="dashboard-main-filter-section">
            <!-- Search Bar -->
            <div class="custom-input-group custom-search-bar-outer mb-sm-0 ">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>
            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Filter Button -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="accordion.toggle('filter');">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button>
                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [routerLink]="['/dashboard/employee/edit/0']">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
        <div ngbAccordion #accordion="ngbAccordion" class="mt-1">
            <div ngbAccordionItem="filter" class="border-0">
                <div ngbAccordionCollapse>
                    <div ngbAccordionBody class="filter-container p-4 w-100">
                        <ng-template>
                            <div class="row gx-0 gy-3 w-100">
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select [items]="userRoles" bindLabel="name"
                                            [(ngModel)]="filterParam.filtering.role" bindValue="id" name="StatusFilter"
                                            #StatusFilter="ngModel">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "USERS.Position" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select [items]="statusList" bindLabel="name"
                                            [(ngModel)]="filterParam.filtering.status" bindValue="id"
                                            name="StatusFilter" #StatusFilter="ngModel">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "COMMON.STATUS" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Duration Filter -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="durationFilter"
                                            #DurationFilter="ngModel"
                                            [(ngModel)]="filterParam.filtering.createdWithinDays"
                                            [items]="durationFilter">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "DashboardFilter.durationFilter" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2 mt-3 mt-md-2">
                                    <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                        appRippleEffect>{{"COMMON.APPLY" |
                                        translate}}</button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="onClearFilter(searchInput)" appRippleEffect>{{"COMMON.CLEAR" |
                                        translate}}</button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <!-- Table-layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': employees.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th class="width-150">{{ 'USERS.Name' | translate }}</th>
                            <th class="width-200">{{ 'USERS.Email' | translate }}</th>
                            <th class="width-150">{{ 'USERS.PhoneNumber' | translate }}</th>
                            <th class="width-250">{{ 'CUSTOMER.Address' | translate }}</th>
                            <th class="width-100">{{ 'USERS.Position' | translate }}</th>
                            <th class="width-100">{{ 'COMMON.STATUS' | translate }}</th>
                            <th class="width-100">{{ 'USERS.HireDate' | translate }}</th>
                            <th class="width-100">{{ 'COMMON.createdOn' | translate }}</th>
                            <th class="th-action width-100 text-center">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <div class="table-container-body">
            <ng-template #name let-data="adtData">
                <a class="action-button text-capitalize ellipsis-2-line"
                    [routerLink]="['/dashboard/employee/edit', data?.id]"
                    [appTooltipEllipsis]="data?.firstName + ' ' + data?.lastName" ngbTooltip>
                    <strong>{{ data?.firstName }} {{ data?.lastName }}</strong>
                </a>
            </ng-template>

            <ng-template #email let-data="adtData">
                <span class="ellipsis-2-line word-break-all" [appTooltipEllipsis]="data.email" ngbTooltip>{{
                    data.email }}</span>
            </ng-template>

            <ng-template #phoneNumber let-data="adtData">
                <span>{{ data.phoneNumber | mask:phoneMaskPattern}}</span>
            </ng-template>

            <ng-template #address let-data="adtData">
                <ng-container *ngIf="data?.addressDetail?.address">
                    <span class="display-align-center">
                        <i class="bi bi-geo-alt-fill pe-1 font-size-16 primary-color"></i>
                        <span class="ellipsis-2-line" [appTooltipEllipsis]="data.addressDetail.address" ngbTooltip>
                            {{ data.addressDetail.address }}
                        </span>
                    </span>
                </ng-container>
            </ng-template>

            <ng-template #position let-data="adtData">
                <div class="d-flex" *ngIf="data.roleName">
                    <span [appStatusBadge]="data?.roleName | roleTransform"></span>
                </div>
            </ng-template>

            <ng-template #status let-data="adtData">
                <div class="d-flex" (click)="updateEmployeeStatus(data)">
                    <span [editable]="true" [appStatusBadge]="data?.isActive ? 'active' : 'inactive'"></span>
                </div>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }} </span>
            </ng-template>

            <ng-template #hireDate let-data="adtData">
                <span>{{ data.hireDate | dateFormat }} </span>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons">
                    <button class="edit-btn" [routerLink]="['/dashboard/employee/edit/' + data.id]" appRippleEffect>
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>
                    <button class="delete-btn " (click)="remove(data.id,resourceType)" appRippleEffect>
                        <img width="22" src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>