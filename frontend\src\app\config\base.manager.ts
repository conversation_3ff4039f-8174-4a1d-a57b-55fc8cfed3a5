import { Observable, catchError, lastValueFrom, map, of, throwError } from 'rxjs';

// models
import { RestResponse } from '../models/common/auth.model';
import { FilterParam } from '../models/common/filter-param';

// services
import { LoadingService } from '../services/loading.service';
import { ToastService } from '../shared/services/toast.service';
import { BaseService } from './base.service';

export class BaseManager {
    constructor(
        private service: BaseService,
        protected loadingService: LoadingService,
        protected toastService: ToastService,
    ) { }

    downloadExcelFile(param: FilterParam, customURL?: string) {
        return this.service.exportToExcel(param, customURL).pipe(
            map((response: RestResponse) => response),
            catchError((error) => {
                this.onFailure(error);
                return of({ status: error.status, message: error.message, data: error } as RestResponse);
            }),
        );
    }

    downloadPDfFile(param: FilterParam, id: string) {
        return this.service.exportToPDF(param, id).pipe(
            map((response: RestResponse) => response),
            catchError((error) => {
                this.onFailure(error);
                return of({ status: error.status, message: error.message, data: error } as RestResponse);
            }),
        );
    }

    fetchAllData(filterParam: FilterParam | null): Promise<any> {
        return lastValueFrom(this.service.fetchAll(filterParam).pipe(map((response: RestResponse) => response.data)));
    }

    fetchAll(filterParam: FilterParam): Observable<RestResponse> {
        return this.service.fetchAll(filterParam).pipe(map((response: RestResponse) => response));
    }

    fetch(id: string): Observable<RestResponse> {
        return this.service.fetch(id).pipe(map((response: RestResponse) => response));
    }

    fetchDropdownData(fetchMethod: () => Observable<RestResponse>): Promise<any> {
        return lastValueFrom(
            fetchMethod().pipe(
                map((response: RestResponse) => response.data),
                catchError((error) => {
                    this.toastService.error(error.message ?? 'An unexpected error occurred.');
                    this.onFailure(error);
                    return of(null);
                }),
            ),
        );
    }

    fetchForDropdownData(filterParam: FilterParam, endpoint: string = 'Selection') {
        return this.fetchForDropdown(filterParam, endpoint).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
            catchError((error) => {
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
                this.toastService.error(errorMessage);
                return of([]);
            }),
        );
    }

    fetchForDropdown(filterParam: FilterParam, endpoint: string = 'Selection'): Observable<RestResponse> {
        return this.service.fetchForDropdown(filterParam, endpoint).pipe(
            map((response: RestResponse) => {
                response.data = this.onFetchAllSuccess(response.data);
                return response;
            }),
            catchError((error) => {
                this.onFailure(error);
                return throwError(() => error);
            }),
        );
    }

    remove(id: string, resourceType?: string): Observable<RestResponse> {
        return this.service.remove(id).pipe(
            map((response: RestResponse) => {
                if (!response) {
                    this.toastService.success(`${resourceType} deleted successfully`);
                } else {
                    response.data = this.onFetchAllSuccess(response.data);

                    this.toastService.success(response?.message || response?.title);
                }
                return response;
            }),
            catchError((error) => {
                this.onFailure(error);
                return throwError(() => error);
            }),
        );
    }

    save(data: any, hasResponse?: boolean): Observable<RestResponse> {
        return this.service.save(data, hasResponse).pipe(map((response: RestResponse) => response));
    }

    update(data: any, hasResponse?: boolean): Observable<RestResponse> {
        return this.service.update(data, hasResponse).pipe(map((response: RestResponse) => response));
    }

    onFetchAllSuccess(data: any) {
        return data;
    }

    onFetchSuccess(data: any) {
        return data;
    }

    onSaveSuccess(data: any) {
        return data;
    }

    onUpdateSuccess(data: any) {
        return data;
    }

    onDeleteSuccess(data: any) {
        return data;
    }

    onFailure(error: any) { }
}
