import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { IResourceWithId, RestResponse } from '../../models/common/auth.model';
import { FilterParam } from '../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class HttpServiceRequests<TResource extends IResourceWithId> {
    headers = environment.AppHeaders;

    constructor(public http: HttpClient) { }

    getRecords(path: string, filterParam?: FilterParam | null): Observable<RestResponse> {
        return this.http
            .post<RestResponse>(environment.BaseApiUrl + path, filterParam, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    getRecord(path: string): Observable<RestResponse> {
        return this.http
            .get<RestResponse>(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    saveRecord(path: string, resource: TResource): Observable<RestResponse> {
        return this.http
            .post<RestResponse>(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    updateRecord(path: string, resource: any): Observable<RestResponse> {
        return this.http
            .put<RestResponse>(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    removeRecord(path: string): Observable<RestResponse> {
        return this.http
            .delete<RestResponse>(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    protected downloadFile(url: string): Observable<HttpResponse<Blob>> {
        return this.http
            .get<Blob>(environment.BaseApiUrl + url, {
                observe: 'response',
                responseType: 'blob' as 'json',
            })
            .pipe(catchError(this.handleError));
    }

    postUrl(path: string): Observable<RestResponse> {
        return this.http
            .post<RestResponse>(environment.BaseApiUrl + path, null, { headers: environment.AppHeaders })
            .pipe(catchError((error) => this.handleError(error)));
    }

    logoutSession(path: string, token: any): Observable<RestResponse> {
        const headers = environment.AppHeaders;
        headers.set('Authorization', 'Bearer ' + token);
        return this.http
            .post<RestResponse>(environment.BaseApiUrl + path, { headers })
            .pipe(catchError((error) => this.handleError(error)));
    }

    validateOtp(path: string, resource: TResource): Observable<RestResponse> {
        return this.http
            .post<RestResponse>(environment.BaseApiUrl + path, resource)
            .pipe(catchError((error) => this.handleError(error)));
    }

    protected handleError(errorResponse: HttpErrorResponse): Promise<any> {
        let errorMessage: string;

        switch (errorResponse.status) {
            case 400:
            case 401:
            case 404:
            case 500:
                errorMessage = errorResponse.error?.message || 'An error occurred';
                break;
            default:
                errorMessage = 'Unexpected error occurred';
        }

        // Create an Error object with a meaningful message
        const error = new Error(errorMessage);
        (error as any).status = errorResponse.status; // Add custom properties if needed
        (error as any).details = errorResponse.error;

        return Promise.reject(error);
    }
}
