// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-party modules
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Shipment } from '../../../../../models/shipment/shipment';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';

// Custom components
import { FileUploaderComponent } from '../../../../../shared/common-component/file-uploader/file-uploader.component';

@Component({
    selector: 'app-shipment-documents',
    standalone: true,
    imports: [FormsModule, TranslateModule, CommonModule, FileUploaderComponent, RouterModule],
    templateUrl: './shipment-documents.component.html',
    styleUrl: './shipment-documents.component.scss',
})
export class ShipmentDocumentsComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    uploaderVisibility: boolean = true;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['shipment']?.currentValue?.id) {
            this.uploaderVisibility = false;
            setTimeout(() => {
                this.uploaderVisibility = true;
            });
        }
    }

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected route: ActivatedRoute,
        protected loadingService: LoadingService,
        protected router: Router,
    ) { }

    onNext(form: any) {
        if (this.isFormInvalid(form)) return;
        this.onNextClick.emit(8);
    }

    onBack() {
        this.onNextOrBackClick.emit(6);
    }

    save(form: any) {
        if (this.isFormInvalid(form)) return;
        this.saveButtonClicked.emit();
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    private isFormInvalid(form: any): boolean {
        if (form.valid) return false;
        this.onClickValidation = true;
        return true;
    }
}
