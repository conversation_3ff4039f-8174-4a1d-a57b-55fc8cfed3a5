import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class RefreshService {
    private isPageRefreshed = false;

    constructor() {
        window.addEventListener('beforeunload', (event) => {
            // Set flag or handle page unload
            this.isPageRefreshed = true;
        });
    }

    public checkIfPageRefreshed(): boolean {
        const refreshed = this.isPageRefreshed;
        this.isPageRefreshed = false; // Reset the flag
        return refreshed;
    }
}
