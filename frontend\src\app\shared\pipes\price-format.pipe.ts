import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'price',
  standalone: true
})
export class PricePipe implements PipeTransform {

  transform(value: number, decimalLength: number = 2): string {
    if (value == null || isNaN(value)) return '';

    // Convert the number to a fixed decimal length
    const isNegative = value < 0;
    const absoluteValue = Math.abs(value);
    const valueParts = absoluteValue.toFixed(decimalLength).split('.');

    // Format the integer part of the value for the Indian numbering system
    const integerPart = valueParts[0];
    const lastThree = integerPart.substring(integerPart.length - 3);
    const otherNumbers = integerPart.substring(0, integerPart.length - 3);
    const formattedValue = otherNumbers !== '' 
      ? otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + ',' + lastThree 
      : lastThree;

    // Combine the integer and decimal parts
    const decimalPart = valueParts[1] ? `.${valueParts[1]}` : '';

    // Return the formatted value with the minus sign if negative
    return `${isNegative ? '- ' : ''}${formattedValue}${decimalPart}`;
  }
}
