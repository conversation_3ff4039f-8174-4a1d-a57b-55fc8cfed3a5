<!-- Forget Password Page -->
<div class="public-page-wrapper min-vh-100 display-flex-column login-bg">
  <div class="container-fluid">
    <div class="row justify-content-md-center justify-content-xl-end ">
      <div class="col-lg-5 col-md-6 display-flex-column public-page-left-section login-page">

        <div class="from-container">

          <div class="logo-section mb-4">
            <img src="/assets/images/svg/password.svg" class="img-fluid mb-3 app-logo forgot-logo" alt="Logo" />
            <h2 class="welcome-label-text">Forgot <span>Password?</span></h2>
            <p>Please Enter your email, and we’ll send a reset link!</p>
          </div>

          <form autocomplete="off" class="form-section mb-3 mb-md-4" #forgotPasswordForm="ngForm">
            <div class="form-floating mb-3">
              <input class="form-control" type="email" name="email" #email="ngModel"
                [ngClass]="{ 'is-invalid': !email.valid && onClickValidation }" [(ngModel)]="forgotPasswordData.email"
                required placeholder="{{ 'LOGIN.EMAIL' | translate }}" appEmailValidator />
              <label for="email">{{ 'EMAIL' | translate }}</label>
              <app-validation-message [field]="email" [onClickValidation]="onClickValidation" *ngIf="!email.valid"
                [customPatternMessage]="'COMMON.REQUIRED_EMAIL_VALIDATION_MESSAGE' | translate">
              </app-validation-message>
              <span class="input-right-icon">
                <img src="/assets/images/svg/email.svg" alt="Email icon" class="icon-file" />
              </span>
            </div>

            <div class="action-button-container mt-2">
              <button class="btn btn-login custom-medium-button overflow-hidden btn-login w-100" appRippleEffect
                (click)="requestPasswordReset(forgotPasswordForm.form)">
                Send
              </button>
            </div>
          </form>

          <!-- Terms and conditions link -->
          <p class="term-condition-text mb-2">
            Back to
            <a class="text-decoration-underline cursor-pointer" routerLink="/login"><strong>{{"LOGIN.objName" |
                translate}}</strong></a>
          </p>
        </div>
      </div>

      <!-- Forgot Bg -->
      <div class="col-xl-8 col-lg-6 px-0 d-none public-page-right-section">
        <app-auth-right-section></app-auth-right-section>
      </div>
    </div>
  </div>
</div>