// Angular core imports
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Routing and navigation
import { ActivatedRoute, Router } from '@angular/router';

// Third-party libraries
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Manager
import { VehicleManager } from '../vehicle.manager';

// Base component and constants
import { BaseEditComponent } from '../../../../config/base.edit.component';

// User model
import { Vehicle } from '../../../../models/vehicle';

// Services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

// Custom components and directives
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ValidationMessageComponent } from '../../../../shared/common-component/validation-message/validation-message.component';
import { NoWhitespaceValidatorDirective } from '../../../../shared/directives/no-whitespace-validator.directive';
import { RippleEffectDirective } from '../../../../shared/directives/ripple-effect.directive';

declare const $: any;

@Component({
    selector: 'app-vehicle-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        NgSelectModule,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
        NoWhitespaceValidatorDirective,
    ],
    providers: [],

    templateUrl: './vehicle-edit.component.html',
    styleUrls: ['./vehicle-edit.component.scss'],
})
export class VehicleEditComponent extends BaseEditComponent implements OnInit {
    @Input() isOpenedInModal!: boolean;
    @Input() vehicleModalRef!: NgbModalRef;

    @Output() newVehicleAdded = new EventEmitter();

    vehicle!: Vehicle;
    constructor(
        public authService: AuthService,
        private readonly vehicleManager: VehicleManager,
        protected override route: ActivatedRoute,
        public override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
    ) {
        super(vehicleManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit(): void {
        this.vehicle = new Vehicle();
        this.setRecord(this.vehicle);
        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;

        this.init();
    }

    override onFetchCompleted() {
        this.vehicle = Vehicle.fromResponse(this.record);
        this.setRecord(this.vehicle);
    }

    handleCancelClick() {
        if (this.isOpenedInModal) {
            this.vehicleModalRef?.close();
        } else {
            this.router.navigate(['/dashboard/vehicles']);
        }
    }

    override onSaveSuccess(data: any): void {
        if (!this.isOpenedInModal) {
            this.router.navigate(['/dashboard/vehicles']);
            return;
        }
        this.newVehicleAdded.emit(data);
    }
}
