<div class="site-page-container mt-3">
    <div class="site-card">
        <form #customerForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <div class="label-wrap-box">
                            <span>{{"CustomerShipments.pickupAddress" | translate}} {{"USERS.Information" |
                                translate}}</span>
                        </div>

                        <!-- Company Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="PickupCompanyName"
                                    #PickupCompanyName="ngModel" required="required"
                                    [(ngModel)]="shipment.pickupCompanyName"
                                    placeholder="{{'USERS.FirstName' | translate}}" [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !PickupCompanyName.valid && onClickValidation && !disabled }"
                                    [disabled]="disabled" />
                                <label for="FirstName">{{"Quotation.pickupCompanyName" | translate}}</label>
                                <app-validation-message [field]="PickupCompanyName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Contact Name field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="pickupContactName"
                                    #PickupContactName="ngModel" [(ngModel)]="shipment.pickupContactPersonName"
                                    placeholder="{{'USERS.LastName' | translate}}" required="required"
                                    [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !PickupContactName.valid && onClickValidation && !disabled }"
                                    [disabled]="disabled" />
                                <label for="PickupContactName">{{"Quotation.pickupContactPersonName" |
                                    translate}}</label>
                                <app-validation-message [field]="PickupContactName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Phone Number -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14" *ngIf="numberVisibility">
                                <app-dial-code-input [(countryCode)]="shipment.pickupContactCountryCode"
                                    [(number)]="shipment.pickupContactPersonPhone" [required]="true"
                                    [onClickValidation]="onClickValidation" fieldName="pickupPhoneNumber"
                                    nameCode="pickupCountryCode" [disableNumber]="disabled"
                                    [labelName]="'Quotation.pickupContactPersonPhone' | translate"></app-dial-code-input>
                            </div>
                        </div>

                        <!-- Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="shipment.pickupAddressDetail"
                            [onClickValidation]="onClickValidation" [isAddressDisabled]="disabled"
                            [isCityDisabled]="disabled" [isProvinceDisabled]="disabled" [isZipCodeDisabled]="disabled"
                            [isCountryDisabled]="disabled"
                            [addressLabel]="'Quotation.pickupAddress' | translate"></app-custom-address>

                        <!-- below delivery section starts -->
                        <div class="label-wrap-box mt-3">
                            <span>{{"CustomerShipments.deliveryAddress" | translate}} {{"USERS.Information" |
                                translate}}</span>
                        </div>

                        <!-- Company Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="deliveryContactPersonName"
                                    #DeliveryContactPersonName="ngModel" required="required"
                                    [(ngModel)]="shipment.deliveryCompanyName"
                                    placeholder="{{'USERS.FirstName' | translate}}" [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !DeliveryContactPersonName.valid && onClickValidation && !disabled }"
                                    [disabled]="disabled" />
                                <label for="FirstName">{{"Quotation.deliveryCompanyName" | translate}}</label>
                                <app-validation-message [field]="DeliveryContactPersonName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Contact Name field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="lastName"
                                    #DeliveryContactPersonName="ngModel"
                                    [(ngModel)]="shipment.deliveryContactPersonName"
                                    placeholder="{{'USERS.LastName' | translate}}" required="required"
                                    [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !DeliveryContactPersonName.valid && onClickValidation && !disabled }"
                                    [disabled]="disabled" />
                                <label for="LastName">{{"Quotation.deliveryContactPersonName" | translate}}</label>
                                <app-validation-message [field]="DeliveryContactPersonName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Phone Number -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <app-dial-code-input [(countryCode)]="shipment.deliveryContactCountryCode"
                                    [(number)]="shipment.deliveryContactPersonPhone" [required]="true"
                                    [onClickValidation]="onClickValidation" fieldName="deliveryPhoneNumber"
                                    nameCode="deliveryCountryCode" [disableNumber]="disabled"
                                    [labelName]="'Quotation.deliveryContactPersonPhone' | translate"></app-dial-code-input>
                            </div>
                        </div>

                        <!-- Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="shipment.deliveryAddressDetail"
                            [onClickValidation]="onClickValidation" [isAddressDisabled]="disabled"
                            [isCityDisabled]="disabled" [isProvinceDisabled]="disabled" [isZipCodeDisabled]="disabled"
                            [isCountryDisabled]="disabled" [addressLabel]="'Quotation.deliveryAddress' | translate">
                        </app-custom-address>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(customerForm.form)" *ngIf="!disabled">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(customerForm.form)" *ngIf="!disabled">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNextOrBackClick.emit(3)" *ngIf="disabled">
                                <div class="site-button-inner">
                                    {{ "COMMON.NEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>