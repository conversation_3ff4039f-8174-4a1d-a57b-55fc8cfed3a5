import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appSpecialCharRestrictor]',
  standalone: true
})
export class SpecialCharRestrictorDirective {
  constructor(protected el: ElementRef) { }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    const invalidChars = /[!@#$%^&*(),.?":{}|<>]/; 
    if (invalidChars.test(event.key)) {
      event.preventDefault();
    }
  }
}
