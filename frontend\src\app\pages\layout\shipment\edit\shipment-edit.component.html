<div class="Shipment-Tab-menu">
    <app-tab-menu [stepperList]="stepperList" (updatedStep)="onSelectionCallBack($event)"
        [lastStoredStep]="this.shipment.step > 2 ? 8:this.shipment.step"></app-tab-menu>
</div>

<ng-container [ngSwitch]="selectedStep">
    <app-shipment-basic-info *ngSwitchCase="1" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment"
        (onNextClick)="tabMenuUpdate($event,true)" [shipmentStatusOptions]="shipmentStatusOptions"
        (onfetchDropdownDataCompleted)="onfetchDropdownDataCompleted($event)"
        (onNextOrBackClick)="tabMenuUpdate($event,false)" [disabled]="disabled"
        [userRole]="userRole"></app-shipment-basic-info>

    <app-shipment-pickup-delivery *ngSwitchCase="2" [onClickValidation]="onClickValidation" [filterParam]="filterParam"
        (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment" (onNextClick)="tabMenuUpdate($event,true)"
        (onNextOrBackClick)="tabMenuUpdate($event,false)" [disabled]="disabled"></app-shipment-pickup-delivery>

    <app-shipment-detail *ngSwitchCase="3" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment"
        (onNextClick)="tabMenuUpdate($event,true)" (onNextOrBackClick)="tabMenuUpdate($event,false)"
        (onfetchDropdownDataCompleted)="onfetchDropdownDataCompleted($event)"
        [intialRateSheetValue]="intialRateSheetValue" [disabled]="disabled">
    </app-shipment-detail>

    <app-shipment-cargo-details *ngSwitchCase="4" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" [shipment]="shipment" [disabled]="disabled"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-shipment-cargo-details>

    <app-shipment-special-request *ngSwitchCase="5" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment"
        (onNextClick)="tabMenuUpdate($event,true)" [rateSheetId]="shipment.rateSheet" [disabled]="disabled"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-shipment-special-request>

    <app-shipment-customer-calculations *ngSwitchCase="6" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" [shipment]="shipment" [disabled]="disabled"
        (onNextOrBackClick)="tabMenuUpdate($event,false)"></app-shipment-customer-calculations>

    <app-shipment-documents *ngSwitchCase="7" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment" [disabled]="disabled"
        (onNextClick)="tabMenuUpdate($event,true)" (onNextOrBackClick)="tabMenuUpdate($event,false)">
    </app-shipment-documents>

    <app-shipment-pod-delivery *ngSwitchCase="8" [request]="request" [onClickValidation]="onClickValidation"
        [filterParam]="filterParam" (saveButtonClicked)="saveShipmentStep()" [shipment]="shipment" [disabled]="disabled"
        (onNextOrBackClick)="tabMenuUpdate($event,true)"></app-shipment-pod-delivery>


</ng-container>