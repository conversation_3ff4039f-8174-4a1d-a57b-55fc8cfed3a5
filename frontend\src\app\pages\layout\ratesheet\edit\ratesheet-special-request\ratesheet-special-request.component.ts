// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';

// Custom components
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { RateSheet } from '../../../../../models/rate-sheet';
import { CurrencyFormatterDirective } from '../../../../../shared/directives/custom-currency.directive';
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';

@Component({
    selector: 'app-rate-sheet-special-request',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        NgSelectModule,
        NgSelectModule,
        RouterModule,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
        CurrencyFormatterDirective,
    ],
    templateUrl: './ratesheet-special-request.component.html',
    styleUrl: './ratesheet-special-request.component.scss',
})
export class RateSheetSpecialRequestComponent {
    @Input() rateSheet!: RateSheet;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;

    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    customerStatus = Constant.CUSTOMER_STATUS;

    openDatePicker: 'startDate' | 'endDate' | null = null;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
    ) { }

    ngOnChanges(changes: SimpleChanges) {
        if (this.rateSheet.startDate && this.rateSheet.id) {
            this.updateToEndDateField = false;
            this.showToEndDateField = true;
            setTimeout(() => {
                this.updateToEndDateField = true;
            });
        }
    }

    onBack() {
        this.onNextOrBackClick.emit(2);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.saveButtonClicked.emit();
    }
}
