.country-code-container {
    // max-width: 90px;
}

::ng-deep .country-code-ng-select-container .ng-select .ng-select-container {
    border-right-width: 1px !important;
    border-radius: 12px 0 0 12px;
}

::ng-deep .country-code-input-container .form-control {
    border-left-width: 1px !important;
    border-radius: 0 12px 12px 0;
}

// Optional: make sure both elements look cohesive
.custom-ng-select,
.country-code-input-container {
    height: 100%;
}

// Align vertically
.row.g-0.align-items-center > .col-auto,
.row.g-0.align-items-center > .col {
    display: flex;
    align-items: stretch;
}
