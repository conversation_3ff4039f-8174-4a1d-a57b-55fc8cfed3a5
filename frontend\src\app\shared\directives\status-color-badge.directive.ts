import { Directive, ElementRef, Input, OnChanges, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appStatusBadge]',
    standalone: true,
})
export class StatusBadgeDirective implements OnChanges {
    @Input('appStatusBadge') status: string = '';
    @Input() editable: boolean = false;

    constructor(
        private el: ElementRef,
        private renderer: Renderer2,
    ) {}

    ngOnChanges(): void {
        this.updateBadge();
    }

    private updateBadge(): void {
        const statusKey = this.status?.replace(/[^a-zA-Z0-9]/g, '').toLowerCase() || 'default';
        const badgeClass = `badge-${statusKey}`;

        // Clear previous content
        this.renderer.setProperty(this.el.nativeElement, 'innerHTML', '');
        this.renderer.removeAttribute(this.el.nativeElement, 'class');

        // Create badge container
        const badgeDiv = this.renderer.createElement('div');
        this.renderer.addClass(badgeDiv, 'status-color-badge');
        this.renderer.addClass(badgeDiv, badgeClass);
        this.renderer.setStyle(badgeDiv, 'cursor', 'pointer');

        if (this.editable) {
            this.renderer.setStyle(badgeDiv, 'cursor', 'pointer');
            this.renderer.addClass(badgeDiv, 'editable');
        } else {
            this.renderer.setStyle(badgeDiv, 'cursor', 'auto');
        }

        // Create status dot
        // const dotSpan = this.renderer.createElement('div');
        // this.renderer.addClass(dotSpan, 'status-dot');

        // Create text span
        const textSpan = this.renderer.createElement('div');
        this.renderer.addClass(textSpan, 'text-nowrap');
        this.renderer.setProperty(textSpan, 'textContent', this.status);

        // Append elements
        // this.renderer.appendChild(badgeDiv, dotSpan);
        this.renderer.appendChild(badgeDiv, textSpan);
        this.renderer.appendChild(this.el.nativeElement, badgeDiv);
    }
}
