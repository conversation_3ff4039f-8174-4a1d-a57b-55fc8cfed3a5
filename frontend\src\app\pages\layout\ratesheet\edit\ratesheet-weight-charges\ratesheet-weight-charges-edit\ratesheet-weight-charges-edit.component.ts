// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../../services/loading.service';
import { AuthService } from '../../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../../shared/services/common.service';
import { ToastService } from '../../../../../../shared/services/toast.service';

// modals
import { RateSheetWeightCharge } from '../../../../../../models/ratesheet-weight-charge';

//Constant
import { Constant } from '../../../../../../config/constants';

// Custom components
import { BaseEditComponent } from '../../../../../../config/base.edit.component';
import { ValidationMessageComponent } from '../../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { AllowNumberOnlyDirective } from '../../../../../../shared/directives/allow-number-only.directive';
import { CurrencyFormatterDirective } from '../../../../../../shared/directives/custom-currency.directive';
import { RippleEffectDirective } from '../../../../../../shared/directives/ripple-effect.directive';

//Manager
import { RateSheetSpecialRequestManager } from '../ratesheet-weight-charges.manager';

@Component({
    selector: 'app-rate-sheet-weight-charges-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        NgSelectModule,
        NgSelectModule,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
        AllowNumberOnlyDirective,
        CurrencyFormatterDirective,
    ],
    templateUrl: './ratesheet-weight-charges-edit.component.html',
    styleUrl: './ratesheet-weight-charges-edit.component.scss',
})
export class RateSheetWeightChargesEditComponent extends BaseEditComponent {
    @Input() modalRef!: NgbModalRef;
    @Input() isOpenedInModal!: boolean;
    @Input() disabled!: boolean;
    @Input() rateSheetWeightCharge!: RateSheetWeightCharge;
    @Input() isCalculationDisabled: boolean = true;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    rateTypes = Constant.RATE_TYPES;

    constructor(
        public override toastService: ToastService,
        protected authService: AuthService,
        protected override commonService: CommonService,
        protected override loadingService: LoadingService,
        protected override route: ActivatedRoute,
        protected override router: Router,
        protected override translateService: TranslateService,
        protected rateSheetSpecialRequestManager: RateSheetSpecialRequestManager,
    ) {
        super(
            rateSheetSpecialRequestManager,
            commonService,
            toastService,
            loadingService,
            route,
            router,
            translateService,
        );
    }

    ngOnInit() {
        if (!this.rateSheetWeightCharge?.id) {
            this.rateSheetWeightCharge = new RateSheetWeightCharge();
            this.rateSheetWeightCharge.fuelRate = 33;
            this.rateSheetWeightCharge.gstRate = 5;

            this.request.isNewRecord = true;
        } else {
            this.request.isNewRecord = false;
            this.rateSheetWeightCharge = RateSheetWeightCharge.fromResponse(this.rateSheetWeightCharge);
        }

        this.initializeRateSheetId();
        this.setRecord(this.rateSheetWeightCharge);
    }

    initializeRateSheetId() {
        this.rateSheetWeightCharge.rateSheet = this.route.snapshot.paramMap.get('id') as string;
    }

    calculateChanges(): void {
        const charge = this.rateSheetWeightCharge;

        const freight = charge.freight || 0;
        const fuelRate = charge.fuelRate || 0;
        const gstRate = charge.gstRate || 0;

        charge.fuelCharges = this.roundToTwo((freight * fuelRate) / 100);

        charge.gst = this.roundToTwo(((freight + charge.fuelCharges) * gstRate) / 100);

        charge.total = this.roundToTwo(freight + charge.fuelCharges + charge.gst);
    }

    private roundToTwo(value: number): number {
        return Math.round(value * 100) / 100;
    }

    override onSaveSuccess = (data: any) => {
        this.saveButtonClicked.emit();
        this.modalRef?.close();
    };
}
