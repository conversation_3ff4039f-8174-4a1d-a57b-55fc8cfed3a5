<div class="d-xl-flex justify-content-xl-center">
    <app-tab-menu
        [stepperList]="stepperList"
        (updatedStep)="onSelectionCallBack($event)"
        [lastStoredStep]="this.customer.customerDetail.step"
    ></app-tab-menu>
</div>

<ng-container [ngSwitch]="selectedStep">
    <app-customer-basic
        *ngSwitchCase="1"
        [request]="request"
        [customer]="customer"
        [onClickValidation]="onClickValidation"
        [filterParam]="filterParam"
        (saveButtonClicked)="saveCustomer()"
        (onNextClick)="tabMenuUpdate($event)"
    ></app-customer-basic>

    <app-customer-company-contacts
        *ngSwitchCase="2"
        [request]="request"
        [customer]="customer"
        [onClickValidation]="onClickValidation"
        [filterParam]="filterParam"
        (saveButtonClicked)="saveCustomer()"
        (onNextClick)="tabMenuUpdate($event)"
        (onNextOrBackClick)="tabMenuUpdate($event)"
    ></app-customer-company-contacts>

    <app-customer-documents
        *ngSwitchCase="3"
        [request]="request"
        [customer]="customer"
        [onClickValidation]="onClickValidation"
        [filterParam]="filterParam"
        (saveButtonClicked)="saveCustomer()"
        (onNextClick)="tabMenuUpdate($event)"
        (onNextOrBackClick)="tabMenuUpdate($event)"
    ></app-customer-documents>

    <app-customer-shipments
        *ngSwitchCase="4"
        (placeChanged)="($event)"
        [customerId]="customer.id"
        (onNextOrBackClick)="tabMenuUpdate($event)"
    ></app-customer-shipments>
</ng-container>
