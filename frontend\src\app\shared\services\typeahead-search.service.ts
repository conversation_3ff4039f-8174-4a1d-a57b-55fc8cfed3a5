import { Injectable } from '@angular/core';
import { Subject, of } from 'rxjs';
import { debounceTime, switchMap, tap } from 'rxjs/operators';
import { FilterParam } from '../../models/common/filter-param';

@Injectable({
    providedIn: 'root', // This makes the service available globally
})
export class TypeAheadService {
    constructor() { }

    setupSearchSubscription(
        searchSubject: Subject<string>,
        filterParam: FilterParam,
        getDataFn: (param: any) => any,
        resultCallback: (result: any) => void,
        loadingCallback: (isLoading: boolean) => void,
        modifyFilterParam?: (filterParam: FilterParam) => void,
    ) {
        searchSubject
            .pipe(
                debounceTime(500),
                tap(() => loadingCallback(true)),
                switchMap((searchTerm) => {
                    if (!searchTerm || searchTerm === '' || searchTerm.trim() === '') {
                        loadingCallback(false);
                        return of([]);
                    }

                    // Update filter params or any other logic
                    filterParam.filtering.searchTerm = searchTerm.trim();

                    // If modifyFilterParam is provided, apply it to modify filterParam
                    if (modifyFilterParam) {
                        modifyFilterParam(filterParam); // Dynamically modify the filterParam
                    }

                    return getDataFn(filterParam);
                }),
            )
            .subscribe((results) => {
                resultCallback(results);
                loadingCallback(false);
            });
    }
}
