<div class="site-customer-main-container mt-2 mt-md-4 p-2 p-md-3">
    <div class="dashboard-content-container section-edit-form mb-4 padding-0">
        <form class="form-item center-input-field" #changePasswordForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-center">
                <div class="col-sm-8 col-md-6 col-xxl-4 justify-content-center">
                    <div class="form-box mb-3 p-2">
                        <!-- old password field -->
                        <div class="col-12 mb-14 px-0">
                            <div class="form-floating toggle-password-visible">
                                <input class="form-control" type="password" id="oldPassword"
                                    placeholder="{{'OLD PASSWORD' | translate}}" name="oldPassword"
                                    #oldPassword="ngModel" [(ngModel)]="profile.oldPassword" required="required"
                                    minlength="8" maxlength="20"
                                    [ngClass]="{'is-invalid':!oldPassword.valid && onClickValidation}">
                                <label for="oldPassword">{{'OLD PASSWORD' | translate}}*</label>

                                @if(profile.oldPassword && profile.oldPassword.length>0){
                                <i class="eye-icon bi bi-eye-slash"
                                    [ngClass]="{'eye-icon-left':!oldPassword.valid && onClickValidation}"
                                    appTogglePasswordVisibility></i>
                                }
                            </div>
                            <app-validation-message [field]="oldPassword" [onClickValidation]="onClickValidation">
                            </app-validation-message>
                        </div>

                        <!-- new password field -->
                        <div class="col-12 mb-14  px-0">
                            <div class="form-floating toggle-password-visible">
                                <input class="form-control" type="password" id="newPassword"
                                    placeholder="{{'NEW PASSWORD' | translate}}" name="newPassword" minlength="8"
                                    maxlength="20" #newPassword="ngModel" [(ngModel)]="profile.password"
                                    required="required"
                                    [ngClass]="{'is-invalid':!newPassword.valid && onClickValidation}">
                                <label for="newPassword">{{'NEW PASSWORD' | translate}}*</label>
                                @if(profile.password && profile.password.length>0){
                                <i class="eye-icon bi bi-eye-slash"
                                    [ngClass]="{'eye-icon-left':!newPassword.valid && onClickValidation}"
                                    appTogglePasswordVisibility></i>
                                }
                            </div>
                            <app-validation-message [field]="newPassword" [onClickValidation]="onClickValidation">
                            </app-validation-message>
                        </div>

                        <!-- confirm password field -->
                        <div class="col-12 mb-14  px-0">
                            <div class="form-floating toggle-password-visible ">
                                <input class="form-control" type="password" id="confirmPassword"
                                    placeholder="{{'CONFIRM NEW PASSWORD' | translate}}" name="confirmPassword"
                                    minlength="8" maxlength="20" #confirmPassword="ngModel"
                                    [(ngModel)]="profile.confirmPassword" required="required"
                                    [ngClass]="{'is-invalid': (!confirmPassword.valid && onClickValidation) || profile.password !== profile.confirmPassword}">
                                <label for="confirmPassword">{{'CONFIRM NEW PASSWORD' | translate}}*</label>
                                @if(profile.confirmPassword && profile.confirmPassword.length>0){
                                <i class="eye-icon bi bi-eye-slash"
                                    [ngClass]="{'eye-icon-left':(!confirmPassword.valid && onClickValidation) || profile.password !== profile.confirmPassword}"
                                    appTogglePasswordVisibility></i>
                                }
                            </div>
                            <app-validation-message [field]="confirmPassword" [onClickValidation]="onClickValidation"
                                [comparableField]="newPassword">
                            </app-validation-message>
                        </div>
                        <!-- Change password Button -->
                        <div class="col-md-12 no-padding text-center mt-2">
                            <button
                                class="btn btn-login custom-medium-button overflow-hidden btn-login w-100 custom-change-password"
                                (click)="updatePassword(changePasswordForm.form)" appRippleEffect>
                                {{"CHANGE PASSWORD" |translate}}
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>