{"version": 3, "sources": ["../../../../../node_modules/angular-datatables/fesm2022/angular-datatables.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport 'rxjs';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\nclass DataTableDirective {\n  constructor(el, vcr, renderer) {\n    this.el = el;\n    this.vcr = vcr;\n    this.renderer = renderer;\n    /**\n     * The DataTable option you pass to configure your table.\n     */\n    this.dtOptions = {};\n  }\n  ngOnInit() {\n    if (this.dtTrigger) {\n      this.dtTrigger.subscribe(options => {\n        this.displayTable(options);\n      });\n    } else {\n      this.displayTable(null);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dtTrigger) {\n      this.dtTrigger.unsubscribe();\n    }\n    if (this.dt) {\n      this.dt.destroy(true);\n    }\n  }\n  displayTable(dtOptions) {\n    // assign new options if provided\n    if (dtOptions) {\n      this.dtOptions = dtOptions;\n    }\n    this.dtInstance = new Promise((resolve, reject) => {\n      Promise.resolve(this.dtOptions).then(resolvedDTOptions => {\n        // validate object\n        const isTableEmpty = Object.keys(resolvedDTOptions).length === 0 && $('tbody tr', this.el.nativeElement).length === 0;\n        if (isTableEmpty) {\n          reject('Both the table and dtOptions cannot be empty');\n          return;\n        }\n        // Set a column unique\n        if (resolvedDTOptions.columns) {\n          resolvedDTOptions.columns.forEach(col => {\n            if ((col.id ?? '').trim() === '') {\n              col.id = this.getColumnUniqueId();\n            }\n          });\n        }\n        // Using setTimeout as a \"hack\" to be \"part\" of NgZone\n        setTimeout(() => {\n          // Assign DT properties here\n          let options = {\n            rowCallback: (row, data, index) => {\n              if (resolvedDTOptions.columns) {\n                const columns = resolvedDTOptions.columns;\n                this.applyNgPipeTransform(row, columns);\n                this.applyNgRefTemplate(row, columns, data);\n              }\n              // run user specified row callback if provided.\n              if (resolvedDTOptions.rowCallback) {\n                resolvedDTOptions.rowCallback(row, data, index);\n              }\n            }\n          };\n          // merge user's config with ours\n          options = Object.assign({}, resolvedDTOptions, options);\n          this.dt = $(this.el.nativeElement).DataTable(options);\n          resolve(this.dt);\n        });\n      });\n    });\n  }\n  applyNgPipeTransform(row, columns) {\n    // Filter columns with pipe declared\n    const colsWithPipe = columns.filter(x => x.ngPipeInstance && !x.ngTemplateRef);\n    colsWithPipe.forEach(el => {\n      const pipe = el.ngPipeInstance;\n      const pipeArgs = el.ngPipeArgs || [];\n      // find index of column using `data` attr\n      const i = columns.filter(c => c.visible !== false).findIndex(e => e.id === el.id);\n      // get <td> element which holds data using index\n      const rowFromCol = row.childNodes.item(i);\n      // Transform data with Pipe and PipeArgs\n      const rowVal = $(rowFromCol).text();\n      const rowValAfter = pipe.transform(rowVal, ...pipeArgs);\n      // Apply transformed string to <td>\n      $(rowFromCol).text(rowValAfter);\n    });\n  }\n  applyNgRefTemplate(row, columns, data) {\n    // Filter columns using `ngTemplateRef`\n    const colsWithTemplate = columns.filter(x => x.ngTemplateRef && !x.ngPipeInstance);\n    colsWithTemplate.forEach(el => {\n      const {\n        ref,\n        context\n      } = el.ngTemplateRef;\n      // get <td> element which holds data using index\n      const i = columns.filter(c => c.visible !== false).findIndex(e => e.id === el.id);\n      const cellFromIndex = row.childNodes.item(i);\n      // reset cell before applying transform\n      $(cellFromIndex).html('');\n      // render onto DOM\n      // finalize context to be sent to user\n      const _context = Object.assign({}, context, context?.userData, {\n        adtData: data\n      });\n      const instance = this.vcr.createEmbeddedView(ref, _context);\n      this.renderer.appendChild(cellFromIndex, instance.rootNodes[0]);\n    });\n  }\n  getColumnUniqueId() {\n    let result = '';\n    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    for (let i = 0; i < 6; i++) {\n      const randomIndex = Math.floor(Math.random() * characters.length);\n      result += characters.charAt(randomIndex);\n    }\n    return result.trim();\n  }\n  static {\n    this.ɵfac = function DataTableDirective_Factory(t) {\n      return new (t || DataTableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DataTableDirective,\n      selectors: [[\"\", \"datatable\", \"\"]],\n      inputs: {\n        dtOptions: \"dtOptions\",\n        dtTrigger: \"dtTrigger\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[datatable]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    dtOptions: [{\n      type: Input\n    }],\n    dtTrigger: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\nclass DataTablesModule {\n  static {\n    this.ɵfac = function DataTablesModule_Factory(t) {\n      return new (t || DataTablesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DataTablesModule,\n      declarations: [DataTableDirective],\n      imports: [CommonModule],\n      exports: [DataTableDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataTablesModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [DataTableDirective],\n      exports: [DataTableDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\n/**\n * @module\n * @description\n * Entry point from which you should import all public library APIs.\n */\n\n/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DataTableDirective, DataTablesModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,IAAI,KAAK,UAAU;AAC7B,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,WAAW;AAIhB,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU,aAAW;AAClC,aAAK,aAAa,OAAO;AAAA,MAC3B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,aAAa,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,YAAY;AAAA,IAC7B;AACA,QAAI,KAAK,IAAI;AACX,WAAK,GAAG,QAAQ,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EACA,aAAa,WAAW;AAEtB,QAAI,WAAW;AACb,WAAK,YAAY;AAAA,IACnB;AACA,SAAK,aAAa,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,cAAQ,QAAQ,KAAK,SAAS,EAAE,KAAK,uBAAqB;AAExD,cAAM,eAAe,OAAO,KAAK,iBAAiB,EAAE,WAAW,KAAK,EAAE,YAAY,KAAK,GAAG,aAAa,EAAE,WAAW;AACpH,YAAI,cAAc;AAChB,iBAAO,8CAA8C;AACrD;AAAA,QACF;AAEA,YAAI,kBAAkB,SAAS;AAC7B,4BAAkB,QAAQ,QAAQ,SAAO;AACvC,iBAAK,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI;AAChC,kBAAI,KAAK,KAAK,kBAAkB;AAAA,YAClC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,mBAAW,MAAM;AAEf,cAAI,UAAU;AAAA,YACZ,aAAa,CAAC,KAAK,MAAM,UAAU;AACjC,kBAAI,kBAAkB,SAAS;AAC7B,sBAAM,UAAU,kBAAkB;AAClC,qBAAK,qBAAqB,KAAK,OAAO;AACtC,qBAAK,mBAAmB,KAAK,SAAS,IAAI;AAAA,cAC5C;AAEA,kBAAI,kBAAkB,aAAa;AACjC,kCAAkB,YAAY,KAAK,MAAM,KAAK;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAEA,oBAAU,OAAO,OAAO,CAAC,GAAG,mBAAmB,OAAO;AACtD,eAAK,KAAK,EAAE,KAAK,GAAG,aAAa,EAAE,UAAU,OAAO;AACpD,kBAAQ,KAAK,EAAE;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,KAAK,SAAS;AAEjC,UAAM,eAAe,QAAQ,OAAO,OAAK,EAAE,kBAAkB,CAAC,EAAE,aAAa;AAC7E,iBAAa,QAAQ,QAAM;AACzB,YAAM,OAAO,GAAG;AAChB,YAAM,WAAW,GAAG,cAAc,CAAC;AAEnC,YAAM,IAAI,QAAQ,OAAO,OAAK,EAAE,YAAY,KAAK,EAAE,UAAU,OAAK,EAAE,OAAO,GAAG,EAAE;AAEhF,YAAM,aAAa,IAAI,WAAW,KAAK,CAAC;AAExC,YAAM,SAAS,EAAE,UAAU,EAAE,KAAK;AAClC,YAAM,cAAc,KAAK,UAAU,QAAQ,GAAG,QAAQ;AAEtD,QAAE,UAAU,EAAE,KAAK,WAAW;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,KAAK,SAAS,MAAM;AAErC,UAAM,mBAAmB,QAAQ,OAAO,OAAK,EAAE,iBAAiB,CAAC,EAAE,cAAc;AACjF,qBAAiB,QAAQ,QAAM;AAC7B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,GAAG;AAEP,YAAM,IAAI,QAAQ,OAAO,OAAK,EAAE,YAAY,KAAK,EAAE,UAAU,OAAK,EAAE,OAAO,GAAG,EAAE;AAChF,YAAM,gBAAgB,IAAI,WAAW,KAAK,CAAC;AAE3C,QAAE,aAAa,EAAE,KAAK,EAAE;AAGxB,YAAM,WAAW,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,UAAU;AAAA,QAC7D,SAAS;AAAA,MACX,CAAC;AACD,YAAM,WAAW,KAAK,IAAI,mBAAmB,KAAK,QAAQ;AAC1D,WAAK,SAAS,YAAY,eAAe,SAAS,UAAU,CAAC,CAAC;AAAA,IAChE,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,QAAI,SAAS;AACb,UAAM,aAAa;AACnB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM;AAChE,gBAAU,WAAW,OAAO,WAAW;AAAA,IACzC;AACA,WAAO,OAAO,KAAK;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACzJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACjC,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,kBAAkB;AAAA,MACjC,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,kBAAkB;AAAA,MACjC,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}