<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>
            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Filter Button -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="accordion.toggle('filter');">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button>

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>

                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [routerLink]="['/dashboard/customer/edit/0']" [state]="{ step:1 , firstLoad :true}">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>
        <!-- Accordion -->
        <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
            <div ngbAccordionItem="filter" class="border-0">
                <div ngbAccordionCollapse>
                    <div ngbAccordionBody class="filter-container p-4 w-100">
                        <ng-template>
                            <div class="row gx-0">
                                <!-- Status  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" [items]="statusList"
                                            [(ngModel)]="filterParam.filtering.status" bindValue="id"
                                            name="StatusFilter" #StatusFilter="ngModel">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "COMMON.STATUS" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Created on  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <app-ng-custom-date-range-picker ngDefaultControl name="selectDateRange"
                                        [labelName]="'COMMON.CREATED_ON'"
                                        [inputFromDate]="filterParam.filtering.fromDate"
                                        [inputToDate]="filterParam.filtering.toDate"
                                        (setFromDate)="filterParam.filtering.fromDate = $event"
                                        (setToDate)="filterParam.filtering.toDate = $event"
                                        [customZIndex]="'z-index-3'"></app-ng-custom-date-range-picker>
                                </div>

                                <!-- City  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-0 pe-xl-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select [items]="cities" bindLabel="city"
                                            [(ngModel)]="filterParam.filtering.city" bindValue="city"
                                            name="StatusFilter" #StatusFilter="ngModel" [typeahead]="searchCitySubject"
                                            [loading]="loadingCityNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "Address.city" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Province  -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 pe-xl-0 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select [items]="provinces" bindLabel="state"
                                            [(ngModel)]="filterParam.filtering.province" bindValue="state"
                                            name="StatusFilter" #StatusFilter="ngModel"
                                            [typeahead]="searchProvinceSubject" [loading]="loadingProvinceNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "Address.province" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Duration Filter -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="durationFilter"
                                            #DurationFilter="ngModel"
                                            [(ngModel)]="filterParam.filtering.createdWithinDays"
                                            [items]="durationFilter">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "DashboardFilter.durationFilter" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2 mt-12">
                                    <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                        appRippleEffect>{{"COMMON.APPLY" |
                                        translate}}</button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="onClearFilter(searchInput)" appRippleEffect>{{"COMMON.CLEAR" |
                                        translate}}</button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': customers.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th class="width-150">{{ 'CUSTOMER.companyName' | translate }} </th>
                            <th class="width-200">{{ 'USERS.Email' | translate }}</th>
                            <th class="width-200">{{ 'CUSTOMER.accountsPayableEmail' | translate }}</th>
                            <th class="width-200">{{ 'USERS.PhoneNumber' | translate }}</th>
                            <th class="width-300">{{ 'CUSTOMER.Address' | translate }}</th>
                            <th class="text-start width-50">{{ 'Address.city' | translate }}</th>
                            <th class="text-start width-150">{{ 'Address.province' | translate }}</th>
                            <th class="width-100">{{ 'COMMON.STATUS' | translate }}</th>
                            <th class="width-150">{{ 'COMMON.CREATED_ON' | translate }}</th>
                            <th class="th-action text-center width-150">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="table-container-body">
            <ng-template #name let-data="adtData">
                <a class="action-button text-capitalize ellipsis-2-line"
                    [routerLink]="['/dashboard/customer/edit/' + data.id]"
                    [state]="{ step:data?.customerDetail?.step , firstLoad :true}"
                    [appTooltipEllipsis]="data?.firstName + ' ' + data?.lastName" ngbTooltip>
                    <strong>{{ data?.firstName }} {{ data?.lastName }}</strong>
                </a>
            </ng-template>

            <ng-template #email let-data="adtData">
                <div class="ellipsis-2-line word-break-all" [appTooltipEllipsis]="data.email" ngbTooltip>{{
                    data.email }}</div>
            </ng-template>

            <ng-template #accountsPayableEmail let-data="adtData">
                <div class="ellipsis-2-line word-break-all"
                    [appTooltipEllipsis]="data?.customerDetail?.accountsPayableEmail" ngbTooltip>{{
                    data?.customerDetail?.accountsPayableEmail }}</div>
            </ng-template>

            <ng-template #phoneNumber let-data="adtData">
                <span>{{ data.phoneNumber | mask:phoneMaskPattern}}</span>
            </ng-template>

            <ng-template #address let-data="adtData">
                <ng-container *ngIf="data?.addressDetail?.address">
                    <span class="display-align-center">
                        <i class="bi bi-geo-alt-fill pe-1 font-size-16 primary-color"></i>
                        <span class="ellipsis-2-line" [appTooltipEllipsis]="data.addressDetail.address" ngbTooltip>
                            {{ data.addressDetail.address }}
                        </span>
                    </span>
                </ng-container>
            </ng-template>

            <ng-template #city let-data="adtData">
                <div class="text-start">{{ data?.addressDetail?.city }}</div>
            </ng-template>

            <ng-template #state let-data="adtData">
                <div class="text-start">{{ data?.addressDetail?.state }}</div>
            </ng-template>

            <ng-template #status let-data="adtData">
                <div class="d-flex" (click)="updateCustomerStatus(data)">
                    <span [editable]="true" [appStatusBadge]="data?.isActive ? 'active' : 'inactive'"></span>
                </div>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <div class="text-center">{{ data.createdOn | dateFormat }} </div>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons">
                    <button class="edit-btn" [routerLink]="['/dashboard/customer/edit/' + data.id]"
                        [state]="{ step:data?.customerDetail?.step , firstLoad :true}">
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>
                    <button class="delete-btn " (click)="remove(data.id,resourceType)">
                        <img width="22" src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>