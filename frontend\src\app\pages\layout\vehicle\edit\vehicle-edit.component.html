<div class="site-page-container" [ngClass]="isOpenedInModal ? 'mt-0' : 'mt-3 mt-md-4'">
    <div class="site-card">
        <form #vehicleForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div [ngClass]="isOpenedInModal ? 'col-md-12 px-0' : 'col-md-9'">
                    <div class="custom-responsive-row row">
                        <!-- Name Field -->
                        <div class="col-md-12 px-0">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="vehicleName" #VehicleName="ngModel"
                                    required="required" [(ngModel)]="vehicle.name"
                                    placeholder="{{'USERS.Name' | translate}}" [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !VehicleName.valid && onClickValidation }" />
                                <label for="vehicleName">{{"USERS.Name" | translate}}</label>
                                <app-validation-message [field]="VehicleName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>
                    </div>

                    <div class="clearfix"></div>
                    <div class="col-md-12 custom-buttons-container">
                        <button class="btn cancel-button" appRippleEffect type="button" (click)="handleCancelClick()">
                            {{ "COMMON.CANCEL" | translate }}
                        </button>
                        <button class="btn save-button" appRippleEffect type="button"
                            (click)="save(vehicleForm.form , { hasResponse: true })">
                            <div class="site-button-inner">
                                {{ "COMMON.SAVE" | translate }}
                            </div>
                        </button>
                        <div class="clearfix"></div>
                    </div>

                </div>
            </div>
        </form>
    </div>
</div>