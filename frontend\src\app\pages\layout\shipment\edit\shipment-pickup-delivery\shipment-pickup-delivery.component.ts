// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Shipment } from '../../../../../models/shipment/shipment';

// Custom components
import { CustomAddressComponent } from '../../../../../shared/common-component/custom-address/custom-address.component';
import { DialCodeInputComponent } from '../../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { NoWhitespaceValidatorDirective } from '../../../../../shared/directives/no-whitespace-validator.directive';
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';

@Component({
    selector: 'app-shipment-pickup-delivery',
    standalone: true,
    imports: [
        CommonModule,
        CustomAddressComponent,
        DialCodeInputComponent,
        FormsModule,
        NgSelectModule,
        NgSelectModule,
        NoWhitespaceValidatorDirective,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './shipment-pickup-delivery.component.html',
    styleUrl: './shipment-pickup-delivery.component.scss',
})
export class ShipmentPickupDeliveryComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() filterParam!: FilterParam;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    customerStatus = Constant.CUSTOMER_STATUS;
    numberVisibility: boolean = true;
    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
    ) { }

    handleCancelClick() {
        this.router.navigate(['/dashboard/customers']);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['shipment']?.currentValue?.id) {
            this.numberVisibility = false;
            setTimeout(() => {
                this.numberVisibility = true;
            });
        }
    }

    onNext(form: any) {
        if (!this.isFormValid(form)) return;
        this.onNextClick.emit(3);
    }

    onBack() {
        this.onNextOrBackClick.emit(1);
    }

    save(form: any) {
        if (!this.isFormValid(form)) return;
        this.saveButtonClicked.emit();
    }

    private isFormValid(form: any): boolean {
        const isFormValid = form.valid;

        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return false;
        }

        const pickup = this.shipment?.pickupAddressDetail;
        const delivery = this.shipment?.deliveryAddressDetail;

        if (
            !pickup?.address ||
            !pickup.city ||
            !pickup.pin ||
            !pickup.state ||
            !pickup.country ||
            !delivery?.address ||
            !delivery.city ||
            !delivery.pin ||
            !delivery.state ||
            !delivery.country
        ) {
            this.onClickValidation = true;
            return false;
        }

        return true;
    }
}
