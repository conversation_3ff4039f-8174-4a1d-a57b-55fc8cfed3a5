import { Injectable } from '@angular/core';
import { NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

@Injectable({
  providedIn: 'root'
})
export class CustomDateParserFormatter extends NgbDateParserFormatter {

  parse(value: string): NgbDateStruct | null {
    if (value) {
      const [year, month, day] = value.split('/').map(Number);
      return { day, month, year };
    }
    return null;
  }

  format(date: NgbDateStruct | null): string {
    if (!date) {
      return '';
    }

    const pad = (digit: number) => digit < 10 ? `0${digit}` : digit; // Helper function to pad single-digit numbers
    const year = date.year;
    const month = pad(date.month); // Ensure two digits for month
    const day = pad(date.day);     // Ensure two digits for day

    return `${month}/${day}/${year}`;
  }
}
