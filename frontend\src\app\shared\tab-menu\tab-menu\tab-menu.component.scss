@import '../../../../assets/scss/mixins.scss';
@import '../../../../variables.scss';

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
    max-width: calc(100% - 0px);

    &::-webkit-scrollbar {
        display: none;
    }

    .step-container {
        background-color: transparent;
        border: 2px solid $tab-background;
        border-radius: 12px;
        margin-right: 8px;
        @include flex-column-center;
        gap: 4px;
        padding: 4px;
        min-width: 180px;
        &:last-child {
            margin-right: none;
        }

        outline: none !important;
        &:focus,
        &:focus-visible {
            outline: none !important;
        }

        &:hover {
            background: $active-tab-container-background !important;
            border-color: var(--primary-color) !important;

            .step-logo {
                background: var(--primary-color) !important;

                img {
                    filter: invert(100%) sepia(100%) saturate(1%) hue-rotate(125deg) brightness(106%) contrast(102%);
                }
            }
        }

        .step-logo {
            border-radius: 12px;
            width: fit-content;
            height: fit-content;
            padding: 8px;
            border: 1px solid $tab-background;
            background-color: $tab-background;
        }

        .step-text {
            .step-number {
                margin-bottom: 0;
                font-size: 10px;
                font-weight: $font-weight-700 !important;
                width: fit-content;
            }

            .step-title {
                margin-bottom: 0;
                width: 120px;
                font-size: 12px;
                font-weight: bold;
                @include text-ellipsis;
                text-wrap: nowrap;
            }
        }
    }
}

.step-background {
    background: var(--primary-color) !important;
}

.step-container-background {
    background: $active-tab-container-background !important;
    border-color: var(--primary-color) !important;
}

.svg-image {
    filter: invert(100%) sepia(100%) saturate(1%) hue-rotate(125deg) brightness(106%) contrast(102%);
}

.filter-section-text-custom {
    font-size: 24px;
}
