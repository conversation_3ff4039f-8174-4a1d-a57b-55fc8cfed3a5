<div class="site-page-container mt-3">
    <div class="site-card">
        <form #CustomerCalculationForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <!-- Total Items field -->
                        <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="totalItems" #TotalItems="ngModel"
                                    [(ngModel)]="quotation.totalItems" [maxlength]="10" mask="separator.2"
                                    [allowedValue]="'.'" appAllowNumberOnly [disabled]="true" [thousandSeparator]="''"
                                    [dropSpecialCharacters]="true" />
                                <label for="totalItems">{{
                                    "SHIPMENT.totalItems" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- Total Weight -->
                        <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="totalWeight" #TotalWeight="ngModel"
                                    [(ngModel)]="quotation.totalWeight" placeholder="Customer Name" [maxlength]="10"
                                    appAllowNumberOnly mask="separator.2" [allowedValue]="'.'" appAllowNumberOnly
                                    [thousandSeparator]="''" [dropSpecialCharacters]="true" [disabled]="true" />
                                <label for="totalWeight">{{
                                    "SHIPMENT.totalWeight" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- Total Volume -->
                        <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="totalVolume" #TotalVolume="ngModel"
                                    [(ngModel)]="quotation.totalVolume" mask="separator.2" [allowedValue]="'.'"
                                    appAllowNumberOnly [thousandSeparator]="''" [dropSpecialCharacters]="true"
                                    [maxlength]="10" appAllowNumberOnly [disabled]="true" />
                                <label for="totalVolume">{{
                                    "SHIPMENT.totalVolume" | translate
                                    }} {{"COMMON.cubicInches" | translate}}</label>
                            </div>
                        </div>

                        <!-- Total Fright  field -->
                        <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="subTotal" #SubTotal="ngModel"
                                    [(ngModel)]="quotation.subTotal" placeholder="Customer Name" [maxlength]="10"
                                    [disabled]="true" appCurrencyFormatter />
                                <label for="subTotal">{{
                                    "SHIPMENT.subTotal" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- Total Fuel Charges field -->
                        <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="fuelChargesTotal"
                                    #FuelChargesTotal="ngModel" [(ngModel)]="quotation.fuelChargesTotal"
                                    placeholder="Customer Name" [maxlength]="10" [disabled]="true"
                                    appCurrencyFormatter />
                                <label for="fuelChargesTotal">{{
                                    "SHIPMENT.fuelCharges" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!--Total Gst field -->
                        <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="gstTotal" #GstTotal="ngModel"
                                    [(ngModel)]="quotation.gstTotal" placeholder="Customer Name" [maxlength]="10"
                                    [disabled]="true" appCurrencyFormatter />
                                <label for="gstTotal">{{
                                    "SHIPMENT.gstTotal" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!--Total Special Charges field  -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="specialTotal" #SpecialTotal="ngModel"
                                    [(ngModel)]="quotation.specialTotal" placeholder="Customer Name" [maxlength]="10"
                                    [disabled]="true" appCurrencyFormatter />
                                <label for="specialTotal">{{
                                    "SHIPMENT.SpecialChargesTotal" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- Grand Total field  -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-18">
                                <input class="form-control" type="text" name="grandTotal" #GrandTotal="ngModel"
                                    [(ngModel)]="quotation.grandTotal" placeholder="Customer Name" [maxlength]="10"
                                    [disabled]="true" appCurrencyFormatter />
                                <label for="grandTotal">{{
                                    "SHIPMENT.grandTotal" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(CustomerCalculationForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(CustomerCalculationForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>

                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>