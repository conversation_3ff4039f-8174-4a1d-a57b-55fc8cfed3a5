import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';
import { Address } from '../common/address';
import { Attachment } from '../common/attachment';
import { Customer } from '../customer/customer';
import { RateSheet } from '../rate-sheet';
import { QuotationCargoDetail } from './quotation-cargo-details';

export class Quotation extends BaseModel {
    tenantId!: number;
    slug!: string;
    customerUserDetail!: Customer;
    customer!: string;
    companyName!: string;
    refID!: string;
    rateSheetDetail!: RateSheet;
    rateSheet!: string;
    contactPersonName!: string;
    contactPersonPhone!: string;
    contactPersonEmail!: string;
    contactPersonCountryCode: string | null = '+1-CA';
    summary!: string;
    isRateSheetChanged: boolean = false;
    isCargoAdded: boolean = false;
    isQuickQuote: boolean = false;

    pickupCompanyName!: string;
    pickupContactPersonName!: string;
    pickupContactPersonPhone!: string;
    pickupContactCountryCode: string | null = '+1-CA';
    deliveryContactCountryCode: string | null = '+1-CA';
    deliveryCompanyName!: string;
    deliveryContactPersonName!: string;
    deliveryContactPersonPhone!: string;

    //Special Request fields
    isOversize: boolean = false;
    oversizeRate!: number | null;
    isRushRequest: boolean = false;
    rushRequestRate!: number | null;
    isEnclosed: boolean = false;
    enclosedRate!: number | null;
    isFragile: boolean = false;
    fragileRate!: number | null;
    isPerishable: boolean = false;
    perishableRate!: number | null;
    isDangerousGoods: boolean = false;
    dangerousGoodsRate!: number | null;

    totalItems!: number;
    totalWeight!: number;
    totalVolume!: number;
    subTotal!: number;
    specialTotal!: number;
    fuelChargesTotal!: number;
    gstTotal!: number;
    grandTotal!: number;
    status: string = 'REQUESTED';

    pickupAddressDetail!: Address;
    pickupAddress!: string;
    deliveryAddressDetail!: Address;
    deliveryAddress!: string;
    step!: number;
    quotationCargoDetail!: QuotationCargoDetail;

    // Documents
    documents!: Attachment[];

    constructor(data?: Partial<Quotation>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        this.deliveryAddressDetail = new Address();
        this.pickupAddressDetail = new Address();
        this.documents = new Array<Attachment>();
        this.customerUserDetail = new Customer();
        this.rateSheetDetail = new RateSheet();
        this.quotationCargoDetail = new QuotationCargoDetail();

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): Quotation {
        return new Quotation(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.refID = this.trimMe(this.refID);
        this.contactPersonName = this.trimMe(this.contactPersonName);
        this.contactPersonPhone = this.trimMe(this.contactPersonPhone);
        this.contactPersonEmail = this.trimMe(this.contactPersonEmail);
        this.summary = this.trimMe(this.summary);
        this.pickupCompanyName = this.trimMe(this.pickupCompanyName);
        this.pickupContactPersonName = this.trimMe(this.pickupContactPersonName);
        this.pickupContactPersonPhone = this.trimMe(this.pickupContactPersonPhone);
        this.deliveryCompanyName = this.trimMe(this.deliveryCompanyName);
        this.deliveryContactPersonName = this.trimMe(this.deliveryContactPersonName);
        this.deliveryContactPersonPhone = this.trimMe(this.deliveryContactPersonPhone);
        this.status = this.trimMe(this.status);
        return this;
    }
}
