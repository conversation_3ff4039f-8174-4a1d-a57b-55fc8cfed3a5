import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
    selector: 'app-dashboard-card',
    standalone: true,
    imports: [RouterModule, NgClass],
    templateUrl: './dashboard-card.component.html',
    styleUrl: './dashboard-card.component.scss',
})
export class DashboardCardComponent {
    @Input() bottomContent!: string;
    @Input() cardHeader!: string;
    @Input() cardSubHeader!: string;
    @Input() imageSrc!: string;
    @Input() routerLink!: string[];

    constructor() {}

    ngOnInit(): void {}
}
