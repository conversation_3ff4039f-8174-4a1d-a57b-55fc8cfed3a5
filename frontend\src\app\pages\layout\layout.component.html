<div class="wrapper">
  <!-- sidebar-wrapper -->
  <nav id="sidebar" #mySideBar class="sidebar-layout-container" data-simplebar>
    <div class="close-sidebar sidebar-content-title d-block d-lg-none" (click)="toggleSidebar()">
      <i class="bi bi-x-circle-fill"></i>
    </div>

    <div class="sidebar-header  ">
      <div class="logo">
        <div class="logo-img">
          <img class="img-responsive cursor-pointer" [routerLink]="['/dashboard']" src="/assets/images/logo.png"
            width="160" alt="beesExpress" />
        </div>
      </div>
    </div>

    <ul class="components margin-top-20 sidebar-unordered-list accordion accordion-flush sidebar-accordion"
      id="parentSideBar">
      <ng-container *ngFor="let item of sidebarItems">
        @if (!item.permission || authService.isAccessible(item.permission.entity, item.permission.type)) {

        @if (visibilityData[item.key]) {
        <li [ngClass]="{
                  'active-menu-item':
                    item.matchType === 'exact'
                      ? isRouteActiveExact(item.route)
                      : isRouteActiveContainsAny(item?.matchRoutes ?? [])
                }">
          <a [routerLink]="[item.route]" class="sidebar-item">
            <div class="setting-menu-container display-align-center">
              <div class="icon-container">
                <div class="sidebar-icon">
                  <img [src]="'/assets/images/svg/menu/' + item.icon" [alt]="item.title" class="sidebar-svg">
                </div>
              </div>
              <span class="sidebar-content-title">{{ item.title | translate }}</span>
            </div>
          </a>
        </li>
        }

        }
      </ng-container>
    </ul>
  </nav>
  <!-- sidebar-wrapper  -->

  <!-- Start Page Content -->
  <div id="content" #myContentElement>
    <div class="page-info-top-bar desktop" #breadCrumbHeader>
      <div class="display-align-center">
        <a id="close-sidebar" class="toggle-menu-icon" (click)="toggleSidebar()">
          <img src="/assets/images/svg/menu.svg" class="img-fluid menu-toggled" alt="menu-bar" width="40" />
        </a>

        <div class="page-name-container">
          <h3 class="page-heading-name" #title placement="bottom"
            [ngbTooltip]="title.scrollWidth > title.offsetWidth ? pageTitle : ''">
            {{pageTitle}}
          </h3>
          <div>
            <ol class="breadcrumb breadcrumb-mobile" #breadCrumb>
              <li class="breadcrumb-item" *ngFor="let breadcrumb of breadcrumbs"
                [ngClass]="{'active': breadcrumb.active}">
                <a *ngIf="!breadcrumb.active" [routerLink]="[breadcrumb.link]">
                  <span [ngClass]="{'text-ellipsis': breadCrumbNumber}">{{getFormKeyword(breadcrumb)}}
                    {{breadcrumb.title}}</span>
                </a>
                <a *ngIf="breadcrumb.active" #edit>
                  {{getFormKeyword(breadcrumb)}} {{breadCrumbNumber ?? breadcrumb.title}}
                </a>
              </li>
            </ol>
          </div>
        </div>
      </div>

      <div class="display-align-center">
        <div class="me-2">
          <app-edit-notification-detail></app-edit-notification-detail>
        </div>

        <div ngbDropdown class="profile-container" #dropdown="ngbDropdown">
          <!-- Profile Avatar (Triggers the Dropdown) -->
          <div class="profile-avatar" ngbDropdownToggle>
            <span>{{ fullName | nameInitial }}</span>
            <div class="status-indicator"></div>
          </div>

          <!-- Dropdown Menu -->
          <div ngbDropdownMenu class="dropdown-menu">
            <div class="display-align-center rounded-2 px-2 py-1">
              <div class="flex-shrink-0 me-2">
                <div class="user-logo-image">
                  {{ fullName | nameInitial }}
                </div>
              </div>
              <div class="flex-grow-1">
                <div class="user-name-text" placement="top" ngbTooltip="{{fullName | titlecase}}">
                  {{fullName | titlecase }}</div>
                <small class="text-body-secondary">{{userData.roles[0] | roleTransform}}</small>
              </div>
            </div>

            <div class="dropdown-divider mt-2 mb-1 mx-negative-2"></div>
            <div class="dropdown-item dropdown-item-content dropdown-clickable-item rounded-2 cursor-pointer"
              appRippleEffect [routerLink]="['/dashboard/my/profile']">
              <i class="bi bi-person menu-icon-size"></i> {{"DASHBOARD.profile" | translate}}
            </div>

            <button class="logout-btn my-2" (click)="onLogout()" appRippleEffect>
              <i class="bi bi-box-arrow-right"></i> {{"LOGIN.logout" | translate}}
            </button>
          </div>
        </div>
      </div>

    </div>

    <div class="clearfix"></div>
    <router-outlet></router-outlet>
  </div>
</div>