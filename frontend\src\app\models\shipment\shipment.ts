import { TranslateService } from '@ngx-translate/core';

import { BaseModel } from '../../config/base.model';

import moment from 'moment';
import { ToastService } from '../../shared/services/toast.service';
import { User } from '../access/user';
import { Barcode } from '../barcode';
import { Address } from '../common/address';
import { Attachment } from '../common/attachment';
import { Customer } from '../customer/customer';
import { RateSheet } from '../rate-sheet';
import { Vehicle } from '../vehicle';
import { ShipmentCargoDetail } from './shipment-cargo-detail';

export class Shipment extends BaseModel {
    barcode!: number;
    contactPersonEmail: string | null = null;
    contactPersonName: string | null = null;
    contactPersonPhone: string | null = null;
    contactPersonCountryCode: string | null = '+1-CA';
    customer!: string;
    customerUserDetail!: Customer;
    rateSheetDetail!: RateSheet;
    loadingDelay: number | null = null;
    deliveryCompanyName: string | null = null;
    deliveryContactPersonName: string | null = null;
    deliveryContactPersonPhone: string | null = null;
    deliveryContactCountryCode: string | null = '+1-CA';
    deliveryAddressDetail: Address = new Address();
    driver!: string;
    driverUserDetail!: User;
    endDate: string | null = null;
    endTime: string = '';
    etd!: string | null;
    name!: string;
    paymentStatus: string | null = 'PENDING';
    paymentType: string | null = 'PREPAID';
    pickupContactPersonName: string | null = null;
    pickupCompanyName: string | null = null;
    pickupContactPersonPhone: string | null = null;
    pickupContactCountryCode: string | null = '+1-CA';
    pickupAddressDetail: Address = new Address();
    rateSheet: string | null = null;
    rateSheetName: string | null = null;
    refID!: string;
    isSecured: boolean = false;
    shipmentCargoDetail: ShipmentCargoDetail = new ShipmentCargoDetail();
    status: string | null = 'NEW';
    shipmentType: string | null = 'REGULAR';
    slug!: string;
    startDate: string | null = null;
    startTime: string = '';
    summary: string | null = null;
    tenantId!: number;
    vehicle: string | null = null;
    vehicleDetail!: Vehicle;
    barcodeDetail!: Barcode;
    documents!: Attachment[];
    step!: number;
    isRateSheetChanged: boolean = false;
    isCargoAdded: boolean = false;

    //Special Request fields
    isOversize: boolean = false;
    oversizeRate!: number | null;
    isRushRequest: boolean = false;
    rushRequestRate!: number | null;
    isEnclosed: boolean = false;
    enclosedRate!: number | null;
    isFragile: boolean = false;
    fragileRate!: number | null;
    isPerishable: boolean = false;
    perishableRate!: number | null;
    isDangerousGoods: boolean = false;
    dangerousGoodsRate!: number | null;

    //Calculation fields
    totalItems!: number | null;
    totalWeight!: number | null;
    totalVolume!: number | null;
    subTotal!: number | null;
    fuelChargesTotal!: number | null;
    gstTotal!: number | null;
    grandTotal!: number | null;
    specialTotal!: number | null;

    //POD Fields
    podAdded: boolean = false;
    podNotes!: string | null;
    podSignatures!: string;
    podImages!: Attachment[];

    constructor(data?: Partial<Shipment>) {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.documents = new Array<Attachment>();
        this.podImages = new Array<Attachment>();

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): Shipment {
        return new Shipment(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService): boolean {
        return true;
    }

    setCurrentTime(): string {
        return moment().format('HH:mm');
    }

    forRequest(): this {
        return this;
    }
}
