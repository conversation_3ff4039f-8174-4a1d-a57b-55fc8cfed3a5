// src/app/utils/maps-loader.ts
export function loadGoogleMaps(apiKey: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const w = window as any;

        // Already loaded?
        if (w.google?.maps) {
            resolve();
            return;
        }

        // If there is an in-flight loader, reuse it
        if (w.__googleMapsLoadPromise) {
            (w.__googleMapsLoadPromise as Promise<void>).then(resolve).catch(reject);
            return;
        }

        // Create loader
        w.__googleMapsLoadPromise = new Promise<void>((res, rej) => {
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry`;
            script.async = true;
            script.defer = true;
            script.onload = () => res();
            script.onerror = (err) => rej(err);
            document.head.appendChild(script);
        });

        w.__googleMapsLoadPromise.then(resolve).catch(reject);
    });
}
