// Angular core modules
import { CommonModule, CurrencyPipe, NgClass, TitleCasePipe } from '@angular/common';
import { Component, EventEmitter, Input, Output, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router, RouterModule } from '@angular/router';

// Third-party UI libraries
import { NgbAccordionModule, NgbModal, NgbModalRef, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';

// Shared/common modules

// Shared services
import { LoadingService } from '../../../../../services/loading.service';
import { CommonService } from '../../../../../shared/services/common.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Base component and configuration
import { BaseListServerSideComponent } from '../../../../../config/base.list.server.side.component';

// Application models
import { Constant } from '../../../../../config/constants';
import { Shipment } from '../../../../../models/shipment/shipment';
import { ShipmentCargoDetail } from '../../../../../models/shipment/shipment-cargo-detail';

// Feature-specific components and services
import { Subscription } from 'rxjs';
import { DelayedInputDirective } from '../../../../../shared/directives/delayed-input.directive';
import { TooltipEllipsisDirective } from '../../../../../shared/directives/tooltip-ellipsis.directive';
import { RemoveUnderscorePipe } from '../../../../../shared/pipes/remove-underscore.pipe';
import { EditShipmentCargoDetailsComponent } from './edit-shipment-cargo-details/edit-shipment-cargo-details.component';
import { ShipmentCargoManager } from './shipment-cargo.manager';
@Component({
    selector: 'app-shipment-cargo-details',
    standalone: true,
    imports: [
        TranslateModule,
        NgSelectModule,
        NgbAccordionModule,
        DelayedInputDirective,
        RouterModule,
        DataTablesModule,
        EditShipmentCargoDetailsComponent,
        TooltipEllipsisDirective,
        NgbTooltip,
        NgClass,
        CurrencyPipe,
        TitleCasePipe,
        CommonModule,
        RemoveUnderscorePipe
    ],
    templateUrl: './shipment-cargo-details.component.html',
    styleUrl: './shipment-cargo-details.component.scss',
})
export class CargoDetailsComponent extends BaseListServerSideComponent {
    @ViewChild('description') description!: TemplateRef<string>;
    @ViewChild('cargoType') cargoType!: TemplateRef<string>;
    @ViewChild('WeightInPounds') WeightInPounds!: TemplateRef<string>;
    @ViewChild('volume') volume!: TemplateRef<string>;
    @ViewChild('freight') freight!: TemplateRef<string>;
    @ViewChild('FuelCharges') FuelCharges!: TemplateRef<string>;
    @ViewChild('gst') gst!: TemplateRef<string>;
    @ViewChild('total') total!: TemplateRef<string>;
    @ViewChild('action') action!: TemplateRef<any>;

    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() override request!: any;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    modalRef!: NgbModalRef;
    title!: string;
    shipmentCargoDetails!: ShipmentCargoDetail[];
    shipmentCargoDetail!: ShipmentCargoDetail;

    routerSubscription?: Subscription;

    resourceType: string = Constant.RESOURCE_TYPE.SHIPMENT_CARGO;

    constructor(
        protected shipmentCargoManager: ShipmentCargoManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected override router: Router,
        protected typeAheadService: TypeAheadService,
        public modalService: NgbModal,
        protected route: ActivatedRoute,
    ) {
        super(shipmentCargoManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.shipmentCargoDetails = new Array<ShipmentCargoDetail>();
        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.SHIPMENT_ITEMS;
        this.filterParam.fileName = this.resourceType;
        this.filterParam.filtering.shipmentId = this.route.snapshot.paramMap.get('id') as string;
        this.init();
        this.routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart && this.modalRef) {
                this.modalRef.close();
            }
        });
    }

    ngAfterViewInit() {
        const templates = {
            description: this.description,
            cargoType: this.cargoType,
            WeightInPounds: this.WeightInPounds,
            volume: this.volume,
            freight: this.freight,
            FuelCharges: this.FuelCharges,
            gst: this.gst,
            total: this.total,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
        super.ngOnDestroy();
    }

    openModal(content: TemplateRef<any>, title: string, data?: any) {
        this.modalRef = this.modalService.open(content, { centered: true, size: 'lg', backdrop: 'static' });
        this.title = title;
        this.shipmentCargoDetail = data;
    }

    onNext() {
        this.onNextOrBackClick.emit(5);
    }

    onBack() {
        this.onNextOrBackClick.emit(3);
    }

    onModalClose(eventData: string) {
        this.refreshRecord();
    }

    override onFetchCompleted() {
        this.shipmentCargoDetails = this.records.map((data) => ShipmentCargoDetail.fromResponse(data));
        super.onFetchCompleted();
    }
}
