// Angular core imports
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

// Project-specific models

// Project-specific services
import { BehaviorSubject } from 'rxjs';
import { VIEW_USER_MAPPING } from '../../config/role-permissions';
import { AuthToken } from '../../models/access/auth-token';
import { User } from '../../models/access/user';
import { IResourceWithId } from '../../models/common/auth.model';
import { LoginService } from '../../services/login.service';
import { CommonUtil } from '../common.util';
import { HttpServiceRequests } from './http.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
    providedIn: 'root',
})
export class AuthService extends HttpServiceRequests<IResourceWithId> {
    protected userSubject = new BehaviorSubject<any>({});
    user$ = this.userSubject.asObservable();

    constructor(
        public override http: HttpClient,
        protected localStorageService: LocalStorageService,
        protected loginService: LoginService,
        protected router: Router,
    ) {
        super(http);
    }

    getToken(): AuthToken {
        return JSON.parse(this.localStorageService.get('token') ?? '{}') as AuthToken;
    }

    getUser(): User {
        return JSON.parse(this.localStorageService.get('user') ?? '{}') as User;
    }

    getRoles() {
        const user = JSON.parse(this.localStorageService.get('user') ?? '{}');
        if (CommonUtil.isNullOrUndefined(user)) {
            return '';
        }
        return user.roles;
    }

    hasRole(roles: any[]): boolean {
        return roles.indexOf(this.getRoles()) !== -1;
    }

    hasValidToken(): boolean {
        const token: any = this.getToken();
        return (
            !CommonUtil.isNullOrUndefined(token) &&
            token.accessToken &&
            token.expires_at &&
            token.expires_at > new Date().getTime()
        );
    }

    isAuthorizedUser(roles: Array<string>) {
        const promise = new Promise((resolve) => {
            if (!this.hasValidToken()) {
                this.localStorageService.remove('token');
                this.localStorageService.remove('user');
            }

            const hasToken = this.hasValidToken();
            const userRoles = this.getRoles() ?? []; // <-- fix here
            const hasRoleAccess = Array.isArray(roles) && roles.some((x) => userRoles.indexOf(x) !== -1);

            resolve({ hasAccess: hasToken, hasRoleAccess });
        });
        return promise;
    }

    isAccessible(entity: string, view: string) {
        const userRoles = this.getUser().roles;
        if (!entity || !view) {
            return false;
        }
        const viewUserMapping: any = VIEW_USER_MAPPING;
        const allowedRoles = viewUserMapping[entity + '_ACCESS'][view];
        if (!allowedRoles) {
            return false;
        }
        return allowedRoles.SHOW_TO_ROLE.some((ele: any) => userRoles.includes(ele));
    }

    isDisabled(entity: string, view: string) {
        const userRoles = this.getUser().roles;
        if (!entity || !view) {
            return false;
        }
        const viewUserMapping: any = VIEW_USER_MAPPING;
        const allowedRoles = viewUserMapping[entity + '_ACCESS'][view];
        if (!allowedRoles) {
            return true;
        }
        return !allowedRoles.ENABLED_FOR_ROLE.some((ele: any) => userRoles.includes(ele));
    }

    logout() {
        // call logout api
        this.loginService.logout(null).subscribe({
            next: (resp) => {},
            error: (error: any) => {},
        });

        this.localStorageService.remove('token');
        this.localStorageService.remove('user');
        this.localStorageService.remove('project');
        this.router.navigate(['login']);
    }

    processSuccessfulResponse(response: any) {
        if (response?.token) {
            this.updateToken(response.token);
        }
        if (response?.user) {
            this.updateUser(response.user);
        }

        this.localStorageService.remove('tempAuthKey');
        this.localStorageService.remove('tempVerifyKey');
    }

    setUser(user: any) {
        this.userSubject.next(user);
    }

    updateToken(token: AuthToken) {
        if (token?.expires) {
            token.expires_at = new Date(token.expires).getTime();
            this.localStorageService.set('token', JSON.stringify(token));
        }
    }

    updateUser(user: User) {
        if (user) {
            this.localStorageService.set('user', JSON.stringify(user));
            this.setUser(user);
        }
    }
}
