export const ROLES = {
    ADMIN: 'ROLE_OFFICE_ADMIN',
    CUSTOMER: 'ROLE_CUSTOMER',
    DRIVER: 'ROLE_DRIVER',
    SUPER_ADMIN: 'ROLE_SUPER_ADMIN',
};

export const ROLE_NAMES = {
    admin: 'admin',
    customer: 'customer',
    driver: 'driver',
    superAdmin: 'superAdmin',
};

export const ALL_ROLE_NAMES = Object.values(ROLE_NAMES);

// customRoles param if we send extra param instead of roles in other field
export const generateRolesAccess = (roles: string[], customRoles: { addButtonRoles?: string[] } = {}) => {
    const addButtonRoles = customRoles.addButtonRoles ?? roles;

    return {
        Page: createAccess(roles),
        AddButton: createAccess(addButtonRoles),
        EditButton: createAccess(roles),
        DeleteButton: createAccess(addButtonRoles),
    };
};

export const createAccess = (showRoles: string[]) => ({
    SHOW_TO_ROLE: showRoles,
    ENABLED_FOR_ROLE: showRoles,
});
