import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { ContactsService } from './contacts.service';

@Injectable({
    providedIn: 'root',
})
export class ContactsManager extends BaseManager {
    constructor(
        protected contactsService: ContactsService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(contactsService, loadingService, toastService);
    }

    updateContactUsStatus(data: any): Observable<RestResponse> {
        return this.contactsService.updateContactUsStatus(data).pipe(map((response: RestResponse) => response));
    }

}