@import '../../variables.scss';
@import './mixins.scss';

// Applying mixin to buttons

.custom-small-button {
    @include button-style(40px, 16px, 100px);
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
    max-width: 120px;
}

.custom-medium-button {
    @include button-style(48px, 16px, 48px);
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
    max-width: 48px;
}

.custom-large-button {
    @include button-style(52px, 16px, 48px);
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
    max-width: 48px;
}

.btn-login {
    background: var(--login-color) !important;
    border-color: var(--login-color) !important;
}

.filter-btn {
    min-width: 64px;

    &:hover,
    &:active {
        border: 1px solid var(--primary-color) !important;
    }

    @media (max-width: 576px) {
        min-height: 48px !important;
    }
}

.add-button-inner {
    width: 100%;
    height: 100%;
    min-width: 64px;

    &:active {
        border: 1px solid transparent !important;
    }
}

.custom-buttons-container {
    text-align: center;
    margin: 16px 0;
    gap: 8px;
    padding: 0 !important;

    @include flex-column-center-vertical;

    .cancel-button {
        @include button-style(48px, 20px, 130px);
        background-color: $white-color;
        color: $black-color;
        border-color: $black-color;

        &:active,
        &:focus {
            background-color: $white-color !important;
        }

        &:hover {
            background-color: var(--button-pre-active-color) !important;
            color: var(--primary-color) !important;
        }
    }

    .save-button {
        @include button-style(48px, 20px);
        max-width: 100%;
    }
}

.action-icons {
    display: flex;
    align-items: center;

    button {
        @include flex-center;
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.2s ease-in-out;

        svg {
            width: 20px;
            height: 20px;
            color: $black-color;
            transition:
                color 0.2s,
                transform 0.2s;
        }

        img {
            width: 22px;
            height: 22px;
        }

        &:enabled:hover {
            background: rgba(0, 0, 0, 0.05);

            svg {
                color: var(--primary-color); // Use your primary color on hover
                transform: scale(1.1);
            }
        }
    }
}