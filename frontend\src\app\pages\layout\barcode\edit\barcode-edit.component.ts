// Angular Core Modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-Party Modules
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Application Modules and Components
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { ValidationMessageComponent } from '../../../../shared/common-component/validation-message/validation-message.component';

// Shared Components and Directives
import { AllowNumberOnlyDirective } from '../../../../shared/directives/allow-number-only.directive';

//Models
import { Barcode } from '../../../../models/barcode';
import { RestResponse } from '../../../../models/common/auth.model';

//Services and Managers
import { LoadingService } from '../../../../services/loading.service';
import { CommonUtil } from '../../../../shared/common.util';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';
import { BarcodeManager } from '../barcode.manager';
@Component({
    selector: 'app-barcode-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TranslateModule,
        NgSelectModule,
        AllowNumberOnlyDirective,
        ValidationMessageComponent,
    ],
    templateUrl: './barcode-edit.component.html',
    styleUrls: ['./barcode-edit.component.scss'],
})
export class BarcodeEditComponent extends BaseEditComponent implements OnInit {
    @Input() modalRef!: NgbModalRef;
    @Input() rateSheet!: string | null;
    @Input() isOpenedInModal!: boolean;
    @Input() disabled!: boolean;

    @Output() closeEvent = new EventEmitter<string>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    barcode!: Barcode;

    constructor(
        protected override route: ActivatedRoute,
        protected barcodeManager: BarcodeManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        public authService: AuthService,
        protected override translateService: TranslateService,
        public commonUtil: CommonUtil,
    ) {
        super(barcodeManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.barcode = new Barcode();
        this.barcode.isActive = true;
        this.setRecord(this.barcode);

        this.init();
    }

    override onFetchCompleted() {
        this.barcode = Barcode.fromResponse(this.record);
        this.setRecord(this.barcode);
    }

    saveBarCode(form: any): void {
        this.onClickValidation = !form.valid;
        if (!form.valid) {
            this.focusInvalidField();
            return;
        }
        this.loadingService.show();

        this.barcodeManager.getBarCodes(this.barcode).subscribe({
            next: (response: RestResponse) => {
                this.loadingService.hide();
                this.toastService.success(response?.message);
                this.onSaveSuccess(response.data);
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    override onSaveSuccess(data: any) {
        this.saveButtonClicked.emit();
    }

    cancel() {
        this.modalRef.close();
    }
}
