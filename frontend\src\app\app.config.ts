// Angular core modules
import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';

// Angular browser modules
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Angular forms modules
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular common modules
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

// Third-party modules
import { NgbToastModule } from '@ng-bootstrap/ng-bootstrap'; // Bootstrap toast module
import { TranslateLoader, TranslateModule } from '@ngx-translate/core'; // i18n module
import { TranslateHttpLoader } from '@ngx-translate/http-loader'; // Loader for translation files
import { DataTablesModule } from 'angular-datatables'; // jQuery-style DataTables integration

// Application-specific modules and services
import { routes } from './app.routes'; // Application routes
import { HttpAuthInterceptor } from './shared/http.interceptor'; // Custom HTTP interceptor
import { ToastService } from './shared/services/toast.service'; // Global toast service

export const appConfig: ApplicationConfig = {
    providers: [
        provideRouter(routes),
        provideHttpClient(),
        importProvidersFrom(
            BrowserModule,
            BrowserAnimationsModule,
            FormsModule,
            CommonModule,
            DataTablesModule,
            ToastService,
            NgbToastModule,
            ReactiveFormsModule,
            TranslateModule.forRoot({
                loader: {
                    provide: TranslateLoader,
                    useFactory: HttpLoaderFactory,
                    deps: [HttpClient],
                },
            }),
        ),
        provideHttpClient(withInterceptorsFromDi()),
        {
            provide: HTTP_INTERCEPTORS,
            useClass: HttpAuthInterceptor,
            multi: true,
        },
    ],
};
export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
    return new TranslateHttpLoader(http);
}
