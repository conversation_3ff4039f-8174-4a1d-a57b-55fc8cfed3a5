// Angular Core Modules
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

// Third-Party Modules
import { FormsModule } from '@angular/forms';
import {
    NgbAccordionModule,
    NgbDropdownModule,
    NgbModal,
    NgbModalRef,
    NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { Subject } from 'rxjs';

// Application Modules and Components
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { BarcodeEditComponent } from './edit/barcode-edit.component';

// Shared Components and Directives
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { RippleEffectDirective } from '../../../shared/directives/ripple-effect.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';

// Pipes
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { RemoveUnderscorePipe } from '../../../shared/pipes/remove-underscore.pipe';

//Models
import { User } from '../../../models/access/user';
import { Barcode } from '../../../models/barcode';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

//Constant
import { Constant } from '../../../config/constants';

// Services and Managers
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';
import { TypeAheadFilterService } from '../../../shared/services/typeahead.service';
import { EmployeesManager } from '../employees/employees.manager';
import { BarcodeManager } from './barcode.manager';

@Component({
    selector: 'app-barcode',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        DateFormatPipe,
        NgbAccordionModule,
        NgbTooltipModule,
        NgSelectModule,
        RippleEffectDirective,
        TranslateModule,
        BarcodeEditComponent,
        RemoveUnderscorePipe,
        DelayedInputDirective,
        StatusBadgeDirective,
        FormsModule,
        NgbDropdownModule,
    ],
    templateUrl: './barcode.component.html',
    styleUrls: ['./barcode.component.scss'],
})
export class BarcodeComponent extends BaseListServerSideComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('refID') refID!: TemplateRef<any>;
    @ViewChild('driver') driverUserDetail!: TemplateRef<any>;
    @ViewChild('barcodeNo') barcodeNo!: TemplateRef<any>;
    @ViewChild('status') status!: TemplateRef<any>;
    @ViewChild('printStatus') printStatus!: TemplateRef<any>;
    @ViewChild('createdOn') createdOn!: TemplateRef<any>;

    resourceType: string = Constant.RESOURCE_TYPE.BARCODES;
    statusOptions = Constant.BARCODE_STATUS;
    barcodePrintedOptions = Constant.BARCODE_PRINTED_STATUS;

    modalRef!: NgbModalRef;
    title!: string;

    drivers: User[] = [];
    barcodes!: Barcode[];

    barcodeDetail!: Barcode;
    isBarcodeGenerationModal!: boolean

    rangeFilters: { id: number; value: string }[] = [];

    startBarcodes: Barcode[] = []
    endBarcodes: Barcode[] = []

    searchDriverSubject: Subject<string> = new Subject<string>();
    loadingDriverNgSelect!: boolean;

    searchStartBarcodeSubject: Subject<string> = new Subject<string>();
    loadingStartBarcodeNgSelect!: boolean;

    searchLastBarcodeSubject: Subject<string> = new Subject<string>();
    loadingLastBarcodeNgSelect!: boolean;

    isFilterApplied: boolean = false;
    printedStatus: string = 'NON_PRINTED';

    searchConfigs = [
        {
            subject: this.searchDriverSubject,
            fetchFunction: (param: FilterParam) => this.employeesManager.fetchForDriverDropdown(param),
            updateResults: (results: any[]) => (this.drivers = results),
            updateLoading: (isLoading: boolean) => (this.loadingDriverNgSelect = isLoading),
            selectedItemGetter: () =>
                this.drivers.find(driver => driver.id === this.filterParam.filtering.driverId),
        },
        {
            subject: this.searchStartBarcodeSubject,
            fetchFunction: this.barcodeManager.fetchForDropdownData.bind(this.barcodeManager),
            updateResults: (results: any) => (this.startBarcodes = results),
            updateLoading: (isLoading: boolean) => (this.loadingStartBarcodeNgSelect = isLoading),
        },
        {
            subject: this.searchLastBarcodeSubject,
            fetchFunction: this.barcodeManager.fetchForDropdownData.bind(this.barcodeManager),
            updateResults: (results: any) => (this.endBarcodes = results),
            updateLoading: (isLoading: boolean) => (this.loadingLastBarcodeNgSelect = isLoading),
        },
    ];

    constructor(
        public modalService: NgbModal,
        protected barcodeManager: BarcodeManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected typeAheadService: TypeAheadFilterService,
        protected employeesManager: EmployeesManager,
        protected override router: Router,
    ) {
        super(barcodeManager, commonService, toastService, loadingService, router);
        this.setupAllSearches();
    }

    ngOnInit() {
        this.barcodes = new Array<Barcode>();
        this.filterParam.fileName = this.resourceType;

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.BARCODE;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.BARCODE;

        this.filterParam.filtering.barcodePrintedStatus = 'NON_PRINTED';

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            barcodeNo: this.barcodeNo,
            refID: this.refID,
            driver: this.driverUserDetail,
            status: this.status,
            printStatus: this.printStatus,
            createdOn: this.createdOn,
        };

        this.setupColumns(templates, true, ['barcodeNo', 'status', 'printStatus', 'createdOn']);
    }

    override onFetchCompleted() {
        this.barcodes = this.records.map((data) => Barcode.fromResponse(data));
        super.onFetchCompleted();
    }

    async barcodeExportToPDF(): Promise<void> {
        this.loadingService.show();
        const contentType = Constant.EXPORT_TO_PDF.CONTENT_TYPE;

        const filters = JSON.parse(JSON.stringify(this.filterParam));
        filters.pagination.next = undefined;
        filters.pagination.offset = undefined;
        filters.filtering.barcodePrintedStatus = this.printedStatus;

        this.barcodeManager.downloadBarcodePDfFile(filters).subscribe({
            next: (response: RestResponse) => {
                if (!response.data) {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                }

                if (response.data) {
                    const blob = new Blob(
                        [
                            new Uint8Array(
                                atob(response.data[0].base64String)
                                    .split('')
                                    .map((char) => char.charCodeAt(0)),
                            ),
                        ],
                        { type: contentType },
                    );
                    const url = window.URL.createObjectURL(blob);
                    const anchorTag = document.createElement('a');
                    anchorTag.href = url;
                    anchorTag.download = response.data[0].fileName;
                    document.body.appendChild(anchorTag);
                    anchorTag.click();
                    window.URL.revokeObjectURL(url);
                    anchorTag.remove();

                    this.loadingService.hide();
                }
            },
        });
    }

    async updateBarCodeToExcel() {
        this.loadingService.show();

        const filters = JSON.parse(JSON.stringify(this.filterParam));
        filters.pagination.next = undefined;
        filters.pagination.offset = undefined;
        filters.filtering.barcodePrintedStatus = this.printedStatus;
        this.exportToExcel(filters);
    }

    applyFilter() {
        this.isFilterApplied = true;

        this.printedStatus = this.filterParam.filtering.barcodePrintedStatus;

        this.onApplyFilter();
    }

    clearFilter(searchInput: HTMLInputElement) {
        this.isFilterApplied = false;
        this.onClearFilter(searchInput, ['barcodePrintedStatus']);
    }

    generateDocument(documentType: 'CSV' | 'PDF') {
        if (documentType === 'CSV') {
            this.updateBarCodeToExcel();
        } else {
            this.barcodeExportToPDF();
        }
    }

    openModal(content: TemplateRef<any>, title: string, isBarcodeGenerationModal: boolean, data?: any) {
        this.modalRef = this.modalService.open(content, { centered: true, size: 'lg', backdrop: 'static' });
        this.title = title;
        this.isBarcodeGenerationModal = isBarcodeGenerationModal
        if (isBarcodeGenerationModal) {
            this.filterParam.filtering.start = null;
            this.filterParam.filtering.end = null
        }
    }

    onSaveButtonClicked() {
        this.modalRef.close();
        this.refreshRecord();
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadFilter = new FilterParam();

        this.searchConfigs.forEach(config => {
            this.typeAheadService.setupSearchSubscription<any>(
                config.subject,
                typeHeadFilter,
                config.fetchFunction,
                config.updateResults,
                config.updateLoading,
                config.selectedItemGetter,
            );
        });
    }
    // end of typeahead search

    markBarcodePrinted() {
        const start = this.filterParam.filtering.start;
        const end = this.filterParam.filtering.end;

        if (!start || !end) {
            this.toastService.error('Both Start and End Barcode must be selected.');
            return;
        }

        const startNum = parseInt(start.replace(/\D/g, ''));
        const endNum = parseInt(end.replace(/\D/g, ''));

        if (startNum > endNum) {
            this.toastService.error('Start Barcode cannot be greater than End Barcode.');
            return;
        }
        this.commonService.confirmation(
            'Are you sure you want to send the selected barcodes for printing?',
            this.markBarcodePrintedCallBack.bind(this),
        );
    }

    markBarcodePrintedCallBack() {
        const start = this.filterParam.filtering.start;
        const end = this.filterParam.filtering.end;

        let printedBarcodeParam = new FilterParam()

        printedBarcodeParam.filtering.start = start
        printedBarcodeParam.filtering.end = end

        this.loadingService.show();
        this.barcodeManager.markBarcodesPrinted(printedBarcodeParam).subscribe({
            next: (response: RestResponse) => {
                this.loadingService.hide();
                this.modalRef.close()
                this.refreshRecord()
                this.toastService.success(response?.message);
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });

    }
}
