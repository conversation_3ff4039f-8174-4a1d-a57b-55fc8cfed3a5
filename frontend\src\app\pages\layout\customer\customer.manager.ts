import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { CustomerService } from './customer.service';

@Injectable({
    providedIn: 'root',
})
export class CustomerManager extends BaseManager {
    constructor(
        protected customerService: CustomerService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(customerService, loadingService, toastService);
    }

    stepperList: any[] = [
        {
            stepNumber: 1,
            stepTitle: 'Basic Information',
            stepPath: '/assets/images/icons/information-sign.svg',
            selected: true,
        },
        {
            stepNumber: 2,
            stepTitle: 'Company Contacts',
            stepPath: '/assets/images/icons/team-leader.svg',
            selected: false,
        },
        {
            stepNumber: 3,
            stepTitle: 'Documents',
            stepPath: '/assets/images/icons/file.svg',
            selected: false,
        },
        {
            stepNumber: 4,
            stepTitle: 'Shipments',
            stepPath: '/assets/images/icons/delivery_truck_speed.svg',
            selected: false,
        },
    ];

    // fetchForCitiesDropdownData(param: FilterParam) {
    //     return this.fetchDropdownData(() => this.customerService.fetchForCitiesDropdown(param));
    // }

    fetchForCitiesDropdownData(param: FilterParam) {
        return this.customerService.fetchForCitiesDropdown(param).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
        );
    }

    fetchForProvinceDropdownData(param: FilterParam) {
        return this.customerService.fetchForProvinceDropdown(param).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
        );
    }

    updateCustomerStatus(id: string, data: any): Observable<RestResponse> {
        return this.customerService.updateCustomerStatus(id, data).pipe(map((response: RestResponse) => response));
    }
}
