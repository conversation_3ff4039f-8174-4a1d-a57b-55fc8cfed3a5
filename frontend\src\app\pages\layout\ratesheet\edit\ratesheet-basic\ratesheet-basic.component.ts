// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';

// Custom components
import { CustomAddressComponent } from '../../../../../shared/common-component/custom-address/custom-address.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { RateSheet } from '../../../../../models/rate-sheet';
import { NgCustomDatePickerComponent } from '../../../../../shared/common-component/ng-custom-date-picker/ng-custom-date-picker.component';
import { NoWhitespaceValidatorDirective } from '../../../../../shared/directives/no-whitespace-validator.directive';
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';

@Component({
    selector: 'app-rate-sheet-basic',
    standalone: true,
    imports: [
        CommonModule,
        CustomAddressComponent,
        NgCustomDatePickerComponent,
        FormsModule,
        NgSelectModule,
        NoWhitespaceValidatorDirective,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './ratesheet-basic.component.html',
    styleUrl: './ratesheet-basic.component.scss',
})
export class RateSheetBasicComponent {
    @Input() rateSheet!: RateSheet;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    customerStatus = Constant.CUSTOMER_STATUS;

    openDatePicker: 'startDate' | 'endDate' | null = null;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
    ) { }

    ngOnChanges(changes: SimpleChanges) {
        if (this.rateSheet.startDate && this.rateSheet.id) {
            this.updateToEndDateField = false;
            this.showToEndDateField = true;
            setTimeout(() => {
                this.updateToEndDateField = true;
            });
        }
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/rate-sheets']);
    }

    setActiveDatePicker(picker: 'startDate' | 'endDate' | null) {
        this.openDatePicker = picker;
    }

    setStartDate(selectedDate: string | null) {
        this.rateSheet.startDate = selectedDate;

        this.updateToEndDateField = false;
        this.showToEndDateField = true;

        setTimeout(() => {
            if (this.rateSheet.endDate) {
                this.rateSheet.endDate = null;
            }

            this.updateToEndDateField = true;
        });
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        if (!this.rateSheet?.pickupAddressDetail?.address || !this.rateSheet?.deliveryAddressDetail?.address) {
            this.onClickValidation = true;
            return;
        }
        this.onNextClick.emit(2);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        if (!this.rateSheet?.pickupAddressDetail?.address || !this.rateSheet?.deliveryAddressDetail?.address) {
            this.onClickValidation = true;
            return;
        }
        this.saveButtonClicked.emit();
    }
}
