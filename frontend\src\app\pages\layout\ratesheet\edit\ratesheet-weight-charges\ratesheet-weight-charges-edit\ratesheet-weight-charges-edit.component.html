<div class="site-page-container mt-3">
    <div class="site-card">
        <form #rateSheetWeightChargeForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-12">
                    <div class="custom-responsive-row row">

                        <!-- RateType field -->
                        <div class="col-md-12 px-0">
                            <div class="form-group form-floating mb-14 custom-ng-select">
                                <ng-select bindLabel="name" bindValue="id" [items]="rateTypes"
                                    [(ngModel)]="rateSheetWeightCharge.rateType" #RateType="ngModel" name="rateType"
                                    required="required" [ngClass]="{
                                        'is-invalid': !RateType.valid && onClickValidation
                                      }" [clearable]="false">
                                </ng-select>
                                <app-validation-message [field]="RateType" [onClickValidation]="onClickValidation">
                                </app-validation-message>

                                <label for="rateType" class="ng-select-label">{{
                                    "RateSheetWeightCharge.rateType" | translate
                                    }}</label>
                            </div>
                        </div>

                        <!-- From Weight field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="fromWeight" #FromWeight="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.fromWeight" required="required"
                                    placeholder="'RateSheetWeightCharge.fromWeight | translate'" [ngClass]="{
                      'is-invalid': !FromWeight.valid && onClickValidation
                    }" appAllowNumberOnly [maxlength]="10" />
                                <label for="fromWeight">{{
                                    "RateSheetWeightCharge.fromWeight" | translate
                                    }}</label>
                                <app-validation-message [field]="FromWeight" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- To Weight field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="toWeight" #ToWeight="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.toWeight" required="required"
                                    placeholder="Customer Name" [ngClass]="{
                      'is-invalid': !ToWeight.valid && onClickValidation
                    }" [maxlength]="10" appAllowNumberOnly />
                                <label for="toWeight">{{
                                    "RateSheetWeightCharge.toWeight" | translate
                                    }}</label>
                                <app-validation-message [field]="ToWeight" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Freight field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="freight" #Freight="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.freight" required="required" placeholder=""
                                    appCurrencyFormatter [maxlength]="10" (input)="calculateChanges()"
                                    [ngClass]="{'is-invalid': !Freight.valid && onClickValidation}" />
                                <label for="freight">{{
                                    "RateSheetWeightCharge.freight" | translate
                                    }}</label>
                                <app-validation-message [field]="Freight" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Fuel Charges field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="fuelCharges" #FuelCharges="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.fuelCharges" required="required"
                                    placeholder="Customer Name" [ngClass]="{
                      'is-invalid': !FuelCharges.valid && !isCalculationDisabled && onClickValidation
                    }" [maxlength]="10" appCurrencyFormatter [disabled]="isCalculationDisabled" />
                                <label for="fuelCharges">{{
                                    "RateSheetWeightCharge.fuelCharges" | translate
                                    }}</label>
                                <app-validation-message [field]="FuelCharges" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- GST field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="gst" #Gst="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.gst" required="required"
                                    placeholder="Customer Name" [ngClass]="{
                      'is-invalid': !Gst.valid && !isCalculationDisabled && onClickValidation
                    }" [maxlength]="10" appCurrencyFormatter [disabled]="isCalculationDisabled" />
                                <label for="gst">{{
                                    "RateSheetWeightCharge.gst" | translate
                                    }}</label>
                                <app-validation-message [field]="Gst" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Total field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="total" #Total="ngModel"
                                    [(ngModel)]="rateSheetWeightCharge.total" required="required"
                                    placeholder="Customer Name" [ngClass]="{
                      'is-invalid': !Total.valid && !isCalculationDisabled && onClickValidation
                    }" [maxlength]="10" appCurrencyFormatter [disabled]="isCalculationDisabled" />
                                <label for="total">{{
                                    "RateSheetWeightCharge.total" | translate
                                    }}</label>
                                <app-validation-message [field]="Total" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-12 custom-buttons-container justify-content-end">
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(rateSheetWeightChargeForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>