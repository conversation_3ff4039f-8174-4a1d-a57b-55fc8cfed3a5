// Angular core and common modules
import { CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third Party modules
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Base class and shared utilities
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { Constant } from '../../../../config/constants';
import { CommonUtil } from '../../../../shared/common.util';

// Services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

// Data models
import { Address } from '../../../../models/common/address';
import { Customer } from '../../../../models/customer/customer';
import { Employees } from '../../../../models/customer/employees';
import { RateSheet } from '../../../../models/rate-sheet';

// Managers (feature-level logic or services)
import { QuotationManager } from '../quotation.manager';

// Custom and shared components
import { NgSelectModule } from '@ng-select/ng-select';
import { TabMenuComponent } from '../../../../shared/tab-menu/tab-menu/tab-menu.component';

// Quotation feature subcomponents
import { RestResponse } from '../../../../models/common/auth.model';
import { Quotation } from '../../../../models/quotation/quotation';
import { BreadcrumbService } from '../../../../shared/services/breadcrumb.service';
import { QuotationBasicInfoComponent } from './quotation-basic-info/quotation-basic-info.component';
import { QuotationCargoDetailsComponent } from './quotation-cargo-details/quotation-cargo-details.component';
import { QuotationCustomerCalculationsComponent } from './quotation-customer-calculations/quotation-customer-calculations.component';
import { QuotationDocumentsComponent } from './quotation-documents/quotation-documents.component';
import { QuotationPickupDeliveryComponent } from './quotation-pickup-delivery/quotation-pickup-delivery.component';
import { QuotationSpecialRequestComponent } from './quotation-special-request/quotation-special-request.component';

@Component({
    selector: 'app-quotation-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TabMenuComponent,
        TranslateModule,
        NgbDatepickerModule,
        NgSelectModule,
        QuotationBasicInfoComponent,
        QuotationPickupDeliveryComponent,
        QuotationCargoDetailsComponent,
        QuotationSpecialRequestComponent,
        QuotationCustomerCalculationsComponent,
        QuotationDocumentsComponent,
    ],
    templateUrl: './quotation-edit.component.html',
    styleUrls: ['./quotation-edit.component.scss'],
})
export class QuotationEditComponent extends BaseEditComponent implements OnInit {
    quotation!: Quotation;
    // quotation: Quotation = new Quotation();
    customers: Array<Customer> = new Array<Customer>();
    rateSheets: Array<RateSheet> = new Array<RateSheet>();
    users: Array<Employees> = new Array<Employees>();
    addresses: Array<Address> = new Array<Address>();
    statusOptions: Array<any> = new Array<any>();

    selectedStep = 1;
    customerStatus = Constant.CUSTOMER_STATUS;
    stepperList = this.quotationManager.stepperList;
    isOnFetchDropDownCompleted: boolean = true;

    userRole!: string;
    intialRateSheetValue!: string | null;

    constructor(
        public authService: AuthService,
        public commonUtil: CommonUtil,
        protected location: Location,
        protected quotationManager: QuotationManager,
        protected override route: ActivatedRoute,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
        protected breadcrumbService: BreadcrumbService
    ) {
        super(quotationManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.userRole = this.authService.getRoles()[0];
        this.initializeQuotation();

        const encoded = this.route.snapshot.queryParamMap.get('q');
        if (encoded) {
            const decoded = JSON.parse(atob(encoded));
            history.replaceState({ ...history.state, step: decoded.step, firstLoad: decoded.firstLoad }, '');
        }

        this.selectedStep = history.state.step <= this.stepperList.length ? history.state.step : 1;
        this.quotation.step = history.state.step <= this.stepperList.length ? this.selectedStep : this.quotation.step;

        if (history.state.firstLoad) {
            this.selectedStep = 1;
        }
        this.onStepSelection();
    }

    ngOnDestroy() {
        this.breadcrumbService.clearNumber();
    }

    override onFetchCompleted() {
        this.quotation = Quotation.fromResponse(this.record);
        this.setRecord(this.quotation);
        this.setPickupDefaults();

        if (this.quotation?.refID) {
            this.breadcrumbService.setNumber(this.quotation?.refID);
        }

        if (!this.isOnFetchDropDownCompleted) {
            this.loadingService.show();
        }
    }

    override onSaveSuccess(data: any): void {
        this.router.navigate(['/dashboard/quotations']);
    }

    onSelectionCallBack(newSelection: number): void {
        const lastStoredStep = history.state.step ?? 1;
        if (lastStoredStep <= 2) {
            if (newSelection <= lastStoredStep) {
                this.onClickValidation = false;
                this.selectedStep = newSelection;
                this.init();
                this.onStepSelection();
                return;
            }
        } else {
            this.selectedStep = newSelection;
            this.init();
            this.onStepSelection();
            return;
        }

        this.onClickValidation = true;
    }

    onStepSelection(): void {
        this.stepperList.forEach((step: any) => {
            step.selected = step.stepNumber === this.selectedStep;
        });
    }

    tabMenuUpdate(step: number, isFormType: boolean): void {
        if (step > this.selectedStep) {
            if (isFormType) {
                this.saveQuotationStep(step);
            } else {
                this.selectedStep = step;
                this.init();
                this.onStepSelection();
            }
        } else {
            this.onSelectionCallBack(step);
        }
    }

    async saveQuotationStep(step?: number): Promise<void> {
        if (this.quotation.step <= this.stepperList.length) {
            if (step) {
                this.quotation.step = Math.max(step, this.quotation.step);
            } else if (this.selectedStep === history.state.step) {
                this.quotation.step += 1;
            }
        }

        const forRequestData = JSON.parse(JSON.stringify(this.quotation));

        this.loadingService.show();
        const method = !this.quotation?.id ? 'save' : 'update';
        this.quotationManager[method](forRequestData, method == 'save').subscribe({
            next: (response: RestResponse) => {
                const isSave = method === 'save';
                if (step) {
                    if (isSave) {
                        this.quotation = Quotation.fromResponse(response.data);
                    } else {
                        this.quotation.id = response.data;
                    }

                    this.setRecord(this.quotation);
                    this.setPickupDefaults();

                    this.request = {
                        isNewRecord: !this.quotation.id,
                        recordId: this.quotation.id || 0,
                    };

                    if (this.quotation.step <= this.stepperList.length) {
                        isSave
                            ? this.updateUrlWithNewId(this.quotation.id, this.quotation.step)
                            : this.updateUrlWithNewState(this.quotation.id, this.quotation.step);
                    }
                    this.loadingService.hide();
                    this.tabMenuUpdate(step, false);
                } else {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                    this.onSaveSuccess(response.data);
                }
            },
            error: (error: { message: string }) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    onfetchDropdownDataCompleted(data: any) {
        this.isOnFetchDropDownCompleted = data;
    }

    private setPickupDefaults(): void {
        const userDetail = this.quotation?.customerUserDetail;
        const customer = userDetail?.customerDetail;

        this.quotation.pickupAddressDetail = this.quotation?.pickupAddressDetail?.id
            ? this.quotation.pickupAddressDetail
            : userDetail?.addressDetail;

        this.quotation.pickupCompanyName ??= customer?.companyName;
        this.quotation.pickupContactPersonName ??= customer?.keyContact;
        this.quotation.pickupContactCountryCode ??= customer?.keyContactCountryCode;
        this.quotation.pickupContactPersonPhone ??= customer?.keyContactPhone;

        this.intialRateSheetValue = this.quotation.rateSheet;
    }

    private updateUrlWithNewState(id: string, step: number) {
        const url = `/dashboard/quotation/edit/${id}`;
        const newState = { step, firstLoad: true };
        this.location?.replaceState(url, '', newState);
    }

    private updateUrlWithNewId(newId: string, step: number) {
        this.router.navigateByUrl('/').then(() => {
            this.router.navigate(['/dashboard/quotation/edit', newId], {
                state: { step: step, firstLoad: false },
            });
        });
    }

    private initializeQuotation(): void {
        this.quotation = new Quotation();

        this.setRecord(this.quotation);

        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;
        this.init();
    }
}
