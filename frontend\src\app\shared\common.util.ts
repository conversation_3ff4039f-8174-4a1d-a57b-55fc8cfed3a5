import { Injectable } from '@angular/core';
import moment from 'moment';
import { environment } from '../../environments/environment';

declare const $: any;

@Injectable({
    providedIn: 'root',
})
export class CommonUtil {
    logging(type: string, message: any, functionName: string, fileName: string) {
        if (!environment.production) {
            console.log(
                'type:=>' +
                type +
                ' message:=>' +
                message +
                ' functionName:=>' +
                functionName +
                ' fileName:=>' +
                fileName,
            );
        }
    }

    toggleMenu() {
        if ($('#nav-sidebar').hasClass('active')) {
            $('#nav-sidebar').addClass('content-body-active');
            $('#nav-sidebar').removeClass('active');
            $('#content').addClass('window-content-body');
            $('#content').removeClass('mobile-content-body');
            $('.admin-page-container').addClass('toggled');
        } else {
            $('#nav-sidebar').addClass('active');
            $('#nav-sidebar').removeClass('content-body-active');
            $('#content').removeClass('window-content-body');
            $('#content').addClass('mobile-content-body');
            $('.admin-page-container').removeClass('toggled');
        }
    }

    static isNullOrUndefined(value: string | null | undefined | number) {
        return value === undefined || value === null;
    }

    static formatDecimalMinutes(value: number | null): number | null {
        if (value === null || value === undefined || isNaN(value)) {
            return null;
        }

        const whole = Math.floor(value);
        const decimal = value - whole;
        const decimalAsMinutes = Math.round(decimal * 100);

        const extraHours = Math.floor(decimalAsMinutes / 60);
        const remainingMinutes = decimalAsMinutes % 60;

        const normalizedValue = whole + extraHours + remainingMinutes / 100;

        return parseFloat(normalizedValue.toFixed(2));
    }

    static setTodayDate(): string {
        return moment().format('YYYY-MM-DD');
    }

}
