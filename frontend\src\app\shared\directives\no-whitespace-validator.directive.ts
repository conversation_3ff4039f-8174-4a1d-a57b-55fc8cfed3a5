import { Directive, Input } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';

@Directive({
  selector: '[appNoWhitespaceValidator]',
  standalone: true,
  providers: [{ provide: NG_VALIDATORS, useExisting: NoWhitespaceValidatorDirective, multi: true }]
})
export class NoWhitespaceValidatorDirective implements Validator {
  @Input() appNoWhitespaceValidator: boolean = true; // Allow the directive to be toggled

  validate(control: AbstractControl): ValidationErrors | null {
    if (this.appNoWhitespaceValidator) {
      const isWhitespace = (control.value || '').trim().length === 0;
      const isValid = !isWhitespace;
      return isValid ? null : { 'whitespace': true };
    }
    return null;
  }
}
