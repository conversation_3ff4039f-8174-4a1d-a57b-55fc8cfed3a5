<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0 invisible">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">

                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="accordion.toggle('filter');">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button>

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>


                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="openModal(content,'Rate sheet weight charges')">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>
    </div>

    <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
        <div ngbAccordionItem="filter" class="border-0">
            <div ngbAccordionCollapse>
                <div ngbAccordionBody class="filter-container p-4 w-100">
                    <ng-template>
                        <div class="row">
                            <!-- Delivery Address  -->
                            <div class="col-md-4 col-xl-3 px-md-2">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="rateTypes"
                                        [(ngModel)]="filterParam.filtering.rateType" #RateType="ngModel" name="rateType"
                                        required="required">
                                    </ng-select>
                                    <label for="rateType" class="ng-select-label">{{
                                        "RateSheetWeightCharge.rateType" | translate
                                        }}</label>
                                </div>
                            </div>

                            <div
                                class="col-md-4 col-xl-3 px-md-2 d-flex align-items-center justify-content-md-start justify-content-end">
                                <div class="d-flex gap-2 mt-md-0 mt-2">
                                    <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                        appRippleEffect>{{"COMMON.APPLY" |
                                        translate}}</button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="onClearFilter(searchInput, ['rateSheetId'])"
                                        appRippleEffect>{{"COMMON.CLEAR" |
                                        translate}}</button>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': rateSheetWeightCharges.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'RateSheetWeightCharge.rateType' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.fromWeight' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.toWeight' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.freight' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.fuelCharges' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.gst' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.total' | translate }}</th>
                            <th class="th-action text-center">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #rateType let-data="adtData">
                <a class="action-button text-capitalize ellipsis-2-line" [appTooltipEllipsis]="data?.rareType"
                    ngbTooltip (click)="openModal(content,'Edit Rate sheet weight charges', data)">
                    <strong>{{ data?.rateType | removeUnderscore }}</strong>
                </a>
            </ng-template>

            <ng-template #fromWeight let-data="adtData">
                <div class="td-align-right">{{ data?.fromWeight }}</div>
            </ng-template>

            <ng-template #toWeight let-data="adtData">
                <div class="td-align-right">{{ data?.toWeight }} </div>
            </ng-template>

            <ng-template #freight let-data="adtData">
                <div class="td-align-right">{{ data?.freight | currency }} </div>
            </ng-template>

            <ng-template #fuelCharges let-data="adtData">
                <div class="td-align-right">{{ data.fuelCharges | currency }} </div>
            </ng-template>

            <ng-template #gst let-data="adtData">
                <div class="td-align-right">{{ data?.gst | currency }} </div>
            </ng-template>

            <ng-template #total let-data="adtData">
                <div class="td-align-right">{{ data?.total | currency }} </div>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons justify-content-center">
                    <button class="edit-btn" (click)="openModal(content,'Edit Rate sheet weight charges', data)">
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>

                    <button class="delete-btn" (click)="remove(data.id,resourceType)">
                        <img src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>

<ng-container>
    <div class="col-md-12 custom-buttons-container">
        <button class="btn cancel-button" appRippleEffect type="button" [routerLink]="['/dashboard/rate-sheets']">
            {{ "COMMON.CANCEL" | translate }}
        </button>

        <button class="btn custom-medium-button save-button" appRippleEffect type="button" (click)="onBack()">
            <div class="site-button-inner">
                {{ "COMMON.BACK" | translate }}
            </div>
        </button>

        <button class="btn custom-medium-button save-button" appRippleEffect type="button" (click)="onNext()">
            <div class="site-button-inner">
                {{ "COMMON.SAVEANDNEXT" | translate }}
            </div>
        </button>
    </div>
</ng-container>

<ng-template #content let-modal>
    <div class="modal-header mx-2 mx-md-4">
        <h4 class="modal-title">{{title}}</h4>
        <button type="button" class="btn-close modal-close" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>

    <div class="modal-body pt-0 mx-2 mx-md-4">
        <app-rate-sheet-weight-charges-edit [rateSheetWeightCharge]="rateSheetWeightCharge" [modalRef]="modalRef"
            (saveButtonClicked)="refreshRecord()">
        </app-rate-sheet-weight-charges-edit>
    </div>
</ng-template>