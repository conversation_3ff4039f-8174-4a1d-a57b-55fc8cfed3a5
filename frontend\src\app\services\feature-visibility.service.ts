import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class FeatureVisibilityService {
    private jsonDataUrl = '/assets/feature-visibility.json'; // Path to your JSON file

    constructor(private http: HttpClient) {}

    getVisibilityData(): Observable<any> {
        return this.http.get<any>(this.jsonDataUrl);
    }
}
