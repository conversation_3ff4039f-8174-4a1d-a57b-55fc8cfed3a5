<div class="site-page-container mt-3">
    <div class="site-card">
        <form #customerForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <div class="label-wrap-box">
                            <span>{{ 'USERS.BasicInfo' | translate }}</span>
                        </div>

                        <!-- First Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="registerCompany"
                                    #FirstName="ngModel"
                                    required="required"
                                    [(ngModel)]="customer.firstName"
                                    placeholder="{{ 'USERS.FirstName' | translate }}"
                                    [appNoWhitespaceValidator]="true"
                                    [ngClass]="{ 'is-invalid': !FirstName.valid && onClickValidation }"
                                />
                                <label for="registerCompany">{{ 'CUSTOMER.companyName' | translate }}</label>
                                <app-validation-message [field]="FirstName" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Phone Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <app-dial-code-input
                                    [(countryCode)]="customer.countryCode"
                                    [(number)]="customer.phoneNumber"
                                    [required]="true"
                                    [onClickValidation]="onClickValidation"
                                    fieldName="phoneNumber"
                                    nameCode="CountryCode"
                                    [labelName]="'CUSTOMER.PhoneNumber' | translate"
                                ></app-dial-code-input>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="email"
                                    #Email="ngModel"
                                    [(ngModel)]="customer.email"
                                    required="required"
                                    placeholder="{{ 'USERS.Email' | translate }}"
                                    [ngClass]="{
                                        'is-invalid': !Email.valid && !customer.id && onClickValidation,
                                    }"
                                    appEmailValidator
                                    [disabled]="customer.id"
                                />
                                <label for="email">{{ 'USERS.Email' | translate }}</label>
                                <app-validation-message [field]="Email" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Company Phone Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <app-dial-code-input
                                [(countryCode)]="customer.customerDetail.companyCountryCode"
                                [(number)]="customer.customerDetail.companyPhone"
                                [onClickValidation]="onClickValidation"
                                fieldName="CompanyPhone"
                                nameCode="CompanyCountryCode"
                                [labelName]="'CUSTOMER.companyPhone' | translate"
                            ></app-dial-code-input>
                        </div>

                        <!--Accounts Payable Email -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input
                                    class="form-control"
                                    type="text"
                                    name="accountsPayableEmail"
                                    #AccountsPayableEmail="ngModel"
                                    [(ngModel)]="customer.customerDetail.accountsPayableEmail"
                                    placeholder="{{ 'CUSTOMER.accountsPayableEmail' | translate }}"
                                    [ngClass]="{
                                        'is-invalid': !AccountsPayableEmail.valid && onClickValidation,
                                    }"
                                    appEmailValidator
                                    [allowEmpty]="true"
                                />
                                <label for="accountsPayableEmail">{{
                                    'CUSTOMER.accountsPayableEmail' | translate
                                }}</label>
                                <app-validation-message
                                    [field]="AccountsPayableEmail"
                                    [onClickValidation]="onClickValidation"
                                >
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-group form-floating mb-14 custom-ng-select">
                                <ng-select
                                    bindLabel="name"
                                    bindValue="id"
                                    [items]="customerStatus"
                                    [(ngModel)]="customer.isActive"
                                    #Status="ngModel"
                                    name="status"
                                    required="required"
                                    [ngClass]="{
                                        'is-invalid': !Status.valid && onClickValidation,
                                    }"
                                    [clearable]="false"
                                >
                                </ng-select>
                                <app-validation-message [field]="Status" [onClickValidation]="onClickValidation">
                                </app-validation-message>

                                <label for="Status" class="ng-select-label">{{ 'COMMON.STATUS' | translate }}</label>
                            </div>
                        </div>

                        <div class="label-wrap-box">
                            <span>{{ 'CUSTOMER.AddressInfo' | translate }}</span>
                        </div>

                        <!-- Address Detail -->
                        <app-custom-address
                            [isRequired]="true"
                            [(address)]="customer.addressDetail"
                            [onClickValidation]="onClickValidation"
                        ></app-custom-address>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button
                                class="btn cancel-button"
                                appRippleEffect
                                type="button"
                                (click)="handleCancelClick()"
                            >
                                {{ 'COMMON.CANCEL' | translate }}
                            </button>
                            <button
                                class="btn custom-medium-button save-button"
                                appRippleEffect
                                type="button"
                                (click)="save(customerForm.form)"
                            >
                                <div class="site-button-inner">
                                    {{ 'COMMON.SAVE' | translate }}
                                </div>
                            </button>
                            <button
                                class="btn custom-medium-button save-button"
                                appRippleEffect
                                type="button"
                                (click)="onNext(customerForm.form)"
                            >
                                <div class="site-button-inner">
                                    {{ 'COMMON.SAVEANDNEXT' | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
