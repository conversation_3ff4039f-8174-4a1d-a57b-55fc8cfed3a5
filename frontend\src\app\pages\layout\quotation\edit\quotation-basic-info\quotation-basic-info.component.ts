// Angular core and common modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, lastValueFrom } from 'rxjs';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Customer } from '../../../../../models/customer/customer';
import { Quotation } from '../../../../../models/quotation/quotation';
import { RateSheet } from '../../../../../models/rate-sheet';

// Services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared and custom components
import { DialCodeInputComponent } from '../../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Managers
import { Constant } from '../../../../../config/constants';
import { CustomerManager } from '../../../customer/customer.manager';
import { QuotationManager } from '../../../quotation/quotation.manager';
import { RateSheetManager } from '../../../ratesheet/rate-sheet.manager';
import { VehicleManager } from '../../../vehicle/vehicle.manager';

@Component({
    selector: 'app-quotation-basic-info',
    standalone: true,
    imports: [
        CommonModule,
        DialCodeInputComponent,
        FormsModule,
        NgSelectModule,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './quotation-basic-info.component.html',
    styleUrl: './quotation-basic-info.component.scss',
})
export class QuotationBasicInfoComponent {
    @Input() quotation!: Quotation;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() intialRateSheetValue!: string | null;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();
    @Output() onfetchDropdownDataCompleted = new EventEmitter<boolean>();

    searchCustomerSubject: Subject<string> = new Subject<string>();
    searchRateSheetSubject: Subject<string> = new Subject<string>();

    loadingCustomerNgSelect!: boolean;
    loadingRateSheetNgSelect!: boolean;

    hasTriggered: boolean = false;

    title!: string;

    quotationStatus = Constant.QUOTATION_STATUS;

    searchConfigs = [
        {
            subject: this.searchCustomerSubject,
            endpoint: 'shipment/selection',
            fetchFunction: this.customerManager.fetchForDropdownData.bind(this.customerManager),
            updateResults: (results: any) => (this.customers = results),
            updateLoading: (isLoading: boolean) => (this.loadingCustomerNgSelect = isLoading),
        },
        {
            subject: this.searchRateSheetSubject,
            fetchFunction: this.rateSheetManager.fetchForDropdownData.bind(this.rateSheetManager),
            updateResults: (results: any) => (this.rateSheets = results),
            updateLoading: (isLoading: boolean) => (this.loadingRateSheetNgSelect = isLoading),
        },
    ];

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    customers: Customer[] = [];
    rateSheets: RateSheet[] = [];

    openDatePicker: 'startDate' | 'endDate' | 'etd' | null = null;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected customerManager: CustomerManager,
        protected quotationManager: QuotationManager,
        protected vehicleManager: VehicleManager,
        protected rateSheetManager: RateSheetManager,
        protected typeAheadService: TypeAheadService,
        protected modalService: NgbModal,
    ) {
        this.setupAllSearches();
    }

    ngOnInit(): void {
        this.fetchDropdownData();

        if (this.request.recordId === '0') {
            this.quotationManager.fetchQuotationRefId().subscribe({
                next: (refId) => {
                    if (refId) {
                        this.quotation.refID = refId;
                    }
                },
            });
        }
    }

    onRateSheetChange(data: any): void {
        if (this.intialRateSheetValue !== data?.id && this.quotation.isCargoAdded) {
            this.commonService.confirmation(
                'Would you like to change the quotation calculations?',
                this.rateSheetChangeCallBack.bind(this),
            );
        }
    }

    rateSheetChangeCallBack(): void {
        this.quotation.isRateSheetChanged = true;
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/quotations']);
    }

    async fetchDropdownData() {
        this.loadingService.show();

        this.filterParam.pagination.next = 10;
        this.filterParam.pagination.offset = 1;

        const [customers, rateSheets] = await Promise.all([
            lastValueFrom(this.customerManager.fetchForDropdownData(this.filterParam, 'shipment/selection')),
            lastValueFrom(this.rateSheetManager.fetchForDropdownData(this.filterParam)),
        ]);

        this.customers = customers;
        this.rateSheets = rateSheets;

        this.addObjectToExistingDropdown();
        this.loadingService.hide();
        this.onfetchDropdownDataCompleted.emit(true);
    }

    async ngDoCheck(): Promise<void> {
        if (!this.hasTriggered && this.quotation?.customer) {
            this.hasTriggered = true;
            this.addObjectToExistingDropdown();
        }
    }

    addUniqueItem(list: any[] = [], item: any) {
        if (item?.id && !this.isDuplicate(list, item)) {
            return [...list, item];
        }
        return list;
    }

    isDuplicate(array: any, item: any) {
        return array?.some((existingItem: { id: any }) => existingItem.id === item.id);
    }

    addObjectToExistingDropdown() {
        const { customerUserDetail, rateSheetDetail } = this.quotation;

        this.customers = this.addUniqueItem(this.customers, customerUserDetail);
        this.rateSheets = this.addUniqueItem(this.rateSheets, rateSheetDetail);
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.onNextClick.emit(2);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.saveButtonClicked.emit();
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadParam = new FilterParam();
        this.searchConfigs.forEach((config) => {
            this.typeAheadService.setupSearchSubscription(
                config.subject,
                typeHeadParam,
                (filterParam) => lastValueFrom(config.fetchFunction(filterParam, config.endpoint)),
                config.updateResults,
                config.updateLoading,
            );
        });
    }
}
