import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { User } from './access/user';
import { Attachment } from './common/attachment';
import { Vehicle } from './vehicle';
export class FuelReceipt extends BaseModel {
    tenantId!: number;
    slug!: string;
    driverDetail!: User;
    driver!: string;
    vehicleDetail!: Vehicle;
    vehicle!: string;
    meterReading!: number;
    fuelInLiters!: number;
    fuelCost!: number;
    fuelType!: string;
    fuelReceiptImages!: Attachment[];

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.fuelReceiptImages = new Array<Attachment>();
    }

    static fromResponse(data: any): FuelReceipt {
        const fuelReceipt = { ...data };

        return fuelReceipt;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.fuelType = this.trimMe(this.fuelType);
        return this;
    }
}
