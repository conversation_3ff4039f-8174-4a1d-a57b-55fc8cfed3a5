import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class RateSheetService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/rateSheet', '/api/rateSheets');
    }

    fetchForPickupCityDropdown(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/address/city/selection', filterParam);
    }

    getCopiedRateSheet(id: string) {
        return this.getRecord('/api/copy/ratesheet/' + id);
    }
}
