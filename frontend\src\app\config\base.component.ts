// Angular imports
import { Router } from '@angular/router';

// Third-party or shared service imports
import { LoadingService } from '../services/loading.service';
import { CommonService } from '../shared/services/common.service';
import { ToastService } from '../shared/services/toast.service';

// Base class import
import { catchError, finalize, map, throwError } from 'rxjs';
import { BaseManager } from './base.manager';
import { Constant } from './constants';

export class BaseComponent {
    request: any;
    alertMessages = Constant.ALERT_MESSAGES;

    constructor(
        protected manager: BaseManager,
        protected commonService: CommonService,
        protected toastService: ToastService,
        protected loadingService: LoadingService,
        protected router: Router,
    ) {
        this.request = {} as any;
    }

    init() { }

    remove(id: any, path: any) {
        this.commonService.confirmation(this.alertMessages.deleteMsg, this.removeCallback.bind(this), id, path);
    }

    removeCallback(id: string, resourceType?: string, path?: string): void {
        this.loadingService.show();

        this.manager
            .remove(id, resourceType)
            .pipe(
                map((response) => {
                    if (!response) {
                        this.removeSuccess();
                    }
                    if (path) {
                        this.router.navigateByUrl(path);
                    }

                    return response;
                }),
                catchError((error) => {
                    this.toastService.error(error?.message);
                    return throwError(() => error);
                }),
                finalize(() => this.loadingService.hide()),
            )
            .subscribe();
    }

    isImage(mime: string) {
        return mime.toLowerCase() === 'jpg' || mime.toLowerCase() === 'png' || mime.toLowerCase() === 'jpeg';
    }

    clean() { }

    removeSuccess() { }

    isNullOrUndefined(value: any) {
        return value === undefined || value === null;
    }
}
