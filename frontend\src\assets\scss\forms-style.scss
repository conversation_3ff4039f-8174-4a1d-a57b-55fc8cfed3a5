// Start of classes related to form control
@import '../../variables.scss';

// create  the section for the form control
.label-wrap-box {
    background: linear-gradient(135deg, #ffeb8c, #fcd616, #fff8d4);
    border-left: 3px solid #1c1c1c;
    padding: 6px;
    margin-bottom: 15px;
    @include flex-space-between;

    span {
        text-transform: uppercase;
        font-size: $font-size-14;
        font-weight: $font-weight-600;
        color: #1c1c1c;
        line-height: normal;
        padding-left: 5px;
    }
}

.custom-responsive-row {
    margin-left: 0px;
    width: 100%;

    .no-expiry-label {
        font-size: $font-size-14 !important;
        font-weight: $font-weight-500 !important;
    }
}

.form-control:not(.question-form-control),
.form-select {
    border-radius: $border-radius-12;
    font-size: $font-size-14;
    font-weight: $font-weight-500;
    padding-top: 1rem !important;
    box-shadow: none;
    padding-left: 1rem !important;

    &:focus {
        border: 2px solid var(--primary-color);
    }
}

// Floating Label Form Field (Material-Like)
.form-floating {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* Input field container with outlined effect */
.form-control {
    height: 48px !important;
    min-height: 48px !important;
    border: 2px solid #e2e0e0;
    border-radius: $border-radius-12;
    padding: 12px 14px;
    font-size: $font-size-14;
    font-weight: $font-weight-500;
    transition: 0.3s border-color;
    background: $white-color;

    &.custom-detail-label {
        line-height: 27px !important;
    }

    &:disabled {
        background-color: #f9f9f9;
    }

    &:focus {
        border: 2px solid var(--primary-color);
    }
}

/* Floating Label */
.form-floating>label {
    position: absolute;
    top: 24px;
    left: 14px;
    height: fit-content;
    transform: translateY(-50%);
    font-size: $font-size-14;
    color: #666;
    background: $white-color;
    padding: 0 4px;
    font-weight: $font-weight-500;
    transition: 0.2s ease all;
    pointer-events: none;
    max-width: calc(100% - 25px);
    width: fit-content;
}

.form-floating.custom-range-date-picker>label {
    right: 24px;
    left: 14px;
    width: auto;
    max-width: calc(100% - 66px);
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

input.is-invalid~label,
ng-select.is-invalid~label {
    max-width: calc(100% - 40px) !important;
    width: fit-content;
}

/* Floating Label Animation */
/* Floating Label Animation */
// Floating Label Moves Up When Focused or Value Exists
.form-floating>.form-control:focus~label,
.form-floating>.form-control:not(:placeholder-shown)~label {
    top: -2px;
    left: 10px;
    width: fit-content;
    font-weight: $font-weight-600;
    font-size: $font-size-14;
    color: $black-color;
}

// Label Background + Color on Focus
.form-floating>.form-control:focus~label {
    background: var(--primary-color);
    color: $black-color;
    border-radius: $border-radius-12;
    padding: 2px 8px !important;
}

// Error + Focused = red background and white label text
.form-floating>.form-control.is-invalid:focus~label {
    background: $form-border-color;
    color: $white-color !important;
}

// Error state label color (not focused)
.form-floating>.form-control.is-invalid~label {
    color: $form-border-color !important;
}

// Floating label icon shift in error state
.form-floating>.form-control.is-invalid~.input-right-icon {
    right: 2rem !important;
}

// Remove default pseudo-elements
.form-floating>.form-control:focus~label::after,
.form-floating>.form-control:not(:placeholder-shown)~label::after,
.form-floating>.form-select~label::after {
    content: unset !important;
}

/* Suffix Icon Styling */
.form-control-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: #888;
}

// Read-only input styling
.form-control[readonly] {
    background-color: $white-color !important;
}

// Adjustments for large input fields
.form-control.large-form-control {
    width: 100%;
    font-size: $font-size-16;
    height: calc(4.5rem + 2px) !important;
}

// Color Picker Input
.form-control.form-control-color {
    width: 100%;
    font-size: 13px;

    &.large {
        font-size: 24px;
        height: calc(4.5rem + 2px) !important;
    }
}

.form-floating input:disabled~label,
.form-floating select:disabled~label,
.form-floating textarea:disabled~label {
    background-color: #f9f9f9;
    border-radius: $border-radius-12;
    width: calc(100% - 40%);
}

// Expiry Checkbox Adjustments
.expiry-check-box div:first-child {
    margin-bottom: 6px !important;
}

// End of classes related to form control

// CSS for Ng-Select Form Floating
.custom-ng-select {
    .ng-select-label {
        position: absolute;
        top: 26px;
        transform: translate(0, -50%);
        padding: 0 4px 4px !important;
        font-size: $font-size-14;
    }

    .ng-select {
        input {
            position: relative;
            left: 5px;
            color: #3f3f3f !important;
            font-size: $font-size-16 !important;
            font-weight: $font-weight-500 !important;
        }

        &.ng-select-opened .ng-select-container,
        &.ng-select-filtered .ng-select-container,
        &.ng-has-value .ng-select-container {
            border-color: var(--primary-color) !important;
        }

        &.is-invalid .ng-select-container {
            border-color: $form-border-color !important;
        }

        &.ng-select-label .is-invalid:focus {
            background-color: $form-border-color !important;
            color: $white-color !important;
        }
    }

    .ng-select-opened {
        z-index: 4;
    }

    .ng-select-container .ng-value .ng-value-label {
        font-weight: $font-weight-500 !important;
        font-size: $font-size-14 !important;
    }

    .ng-select-opened~.ng-select-label,
    .ng-select-filtered~.ng-select-label {
        top: -2px !important;
        left: 10px;
        font-weight: $font-weight-600;
        z-index: 5;
        background-color: var(--primary-color);
        color: $black-color !important;
        padding: 2px 8px !important;
        border-radius: $border-radius-12;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .ng-select:has(.ng-has-value)~.ng-select-label {
        top: -2px !important;
        left: 10px;
        font-weight: $font-weight-600;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .is-invalid~.ng-select-label {
        color: $form-border-color !important;
    }

    .custom-ng-select .is-invalid~.ng-select-label {
        color: $form-border-color !important;
    }

    .ng-select.ng-select-disabled~.ng-select-label {
        background-color: #f9f9f9;
        border-radius: $border-radius-12;
    }

    // 🔴 Field is active (opened or filtering) AND invalid
    .is-invalid.ng-select-opened~.ng-select-label,
    .is-invalid.ng-select-filtered~.ng-select-label {
        background-color: $form-border-color;
        color: $white-color !important;
        padding: 2px 8px !important;
        border-radius: $border-radius-12;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    // 🔴 Field has value but NOT active
    .is-invalid.ng-select:has(.ng-has-value):not(.ng-select-opened):not(.ng-select-filtered)~.ng-select-label {
        color: $form-border-color !important;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }
}

.ng-dropdown-footer {
    padding: 0 !important;

    div {
        padding: 8px 10px !important;
        font-size: $font-size-14;
        cursor: pointer;
        @include text-ellipsis;
        font-weight: $font-weight-500;
        color: $black-color;
        background-color: var(--primary-color);

        i {
            &::before {
                font-size: $font-size-13 !important;
                font-weight: bold !important;
            }
        }
    }
}

.ng-dropdown-panel-items {

    .ng-option-selected,
    .ng-option-marked {
        background-color: $active-tab-container-background !important;
    }

    .ng-option {
        font-size: $font-size-13;
        font-weight: $font-weight-500;
    }
}

.ng-dropdown-panel.ng-select-bottom {
    margin-top: 0;
}

.ng-dropdown-panel.ng-select-top {
    margin-bottom: 8px;
}

// CSS for Ng-Select Form Floating ends

// Custom class for the text area
.textarea-custom {
    border: 2px solid #ced4da;
    border-radius: $border-radius-12;
    min-height: 60px !important;

    &:focus-within {
        border: 2px solid var(--primary-color);
    }

    .form-control:not(:placeholder-shown)~label {
        top: -2px !important;
    }

    .form-control:focus~label {
        top: -2px !important;
    }

    .form-control {
        min-height: 60px !important;
        border: none;
        resize: none;
        margin-top: 14px;
        padding-top: 0 !important;

        &:focus {
            padding-top: 0 !important;
            border: 0;
            box-shadow: none;
        }

        &::-webkit-scrollbar {
            display: none;
        }

        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    label {
        top: 24px !important;
    }
}

// It for giving error class to  the textarea
.has-error-textarea {
    border-width: 2px !important;
    border-color: $form-border-color !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;

    label {
        color: $form-border-color;
    }

    .form-control:not(:placeholder-shown)~label {
        color: $form-border-color;
    }

    .form-control:focus~label {
        color: $form-border-color;
    }

    .form-control {
        &:focus {
            box-shadow: none !important;
        }
    }
}

// Ng-select Classes

.input-ng-select {
    &.ng-select {
        .ng-select-container {
            color: $body-color;
            font-size: $font-size-14;
            font-weight: $font-weight-500;
            border: 1px solid $input-border;
            background: $white-color;
            border-radius: $border-radius-12;
            min-height: 60px !important;
            position: relative;
        }

        .ng-dropdown-panel {
            border: 1px solid $input-border;
            box-shadow: unset !important;
        }
    }
}

.ng-select .ng-select-container .ng-value-container {
    padding-left: 15px;
    box-shadow: none !important;
}

.ng-select .ng-select-container .ng-value-container .ng-placeholder {
    font-size: $font-size-14 !important;
    font-family: 'Montserrat', sans-serif !important;
    color: #71828a;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
    top: 50% !important;
    margin-top: -10.5px !important;
    font-size: $font-size-14 !important;
    font-family: 'Montserrat', sans-serif !important;
    color: #3f3f3f;
}

.input-ng-select .form-floating .ng-select.ng-select-focused .ng-select-container {
    box-shadow: none !important;
}

.ng-select .ng-select-container {
    height: 48px !important;
    border-radius: $border-radius-12;
    border: 2px solid #e2e0e0 !important;
    box-shadow: unset !important;

    input {
        height: 38px !important;
    }

    .ng-value {
        .ng-value-label {
            color: #3f3f3f !important;
            font-size: $font-size-14 !important;
            font-weight: $font-weight-600 !important;
        }
    }
}

.td-align-right {
    width: 100%;
    text-align: end;
    padding-right: 20px;
}

.form-check-input {
    &:checked {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
    }

    &:focus {
        box-shadow: none !important;
        border-color: var(--primary-color);
    }
}

.form-check-label {
    font-weight: $font-weight-500 !important;
    margin-top: 1px !important;
}