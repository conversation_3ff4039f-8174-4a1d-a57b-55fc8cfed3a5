import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { COMMON_ROLES } from './config/role-permissions';
import { ForgotPasswordComponent } from './pages/auth/forgot-password/forgot-password.component';
import { LoginComponent } from './pages/auth/login/login.component';
import { RecoverPasswordComponent } from './pages/auth/recover-password/recover-password.component';
import { ForbiddenComponent } from './pages/forbidden/forbidden.component';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import { PrivacyPolicyComponent } from './pages/privacy-policy/privacy-policy.component';
import { TermsOfUseComponent } from './pages/terms-of-use/terms-of-use.component';
import { AuthGuard } from './shared/auth.guard';

export const routes: Routes = [
    {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full',
    },
    {
        path: 'login',
        title: 'Login',
        canActivate: [AuthGuard.canActivate()],
        component: LoginComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    {
        path: 'forgot/password',
        title: 'Forgot Password',
        canActivate: [AuthGuard.canActivate()],
        component: ForgotPasswordComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    {
        path: 'recover/:ucode',
        title: 'Reset Password',
        canActivate: [AuthGuard.canActivate()],
        component: RecoverPasswordComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    {
        path: 'create/password/:ucode',
        title: 'Create Password',
        canActivate: [AuthGuard.canActivate()],
        component: RecoverPasswordComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    {
        path: 'dashboard',
        loadChildren: () => import('./pages/layout/layout.module').then((m) => m.LayoutModule),
        canActivate: [AuthGuard.canActivate()],
        data: {
            roles: COMMON_ROLES,
        },
    },
    {
        path: 'privacy-policy',
        component: PrivacyPolicyComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    {
        path: 'terms-of-use',
        component: TermsOfUseComponent,
        data: { roles: ['ROLE_ANONYMOUS'] },
    },
    { path: '403', component: ForbiddenComponent },
    { path: '404', component: NotFoundComponent },
    { path: '**', redirectTo: '/404' },
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule],
})
export class AppRoutingModule { }
