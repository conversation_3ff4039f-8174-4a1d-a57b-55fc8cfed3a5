@import '../../variables.scss';
@import '../../assets/scss/mixins.scss';

@media screen and (max-width: 1400px) {
    .right-section {
        .right-content {
            .blur-content {
                padding: 60px !important;
                height: 90% !important;

                h1 {
                    font-size: 28px !important;
                }

                p {
                    font-size: 12px !important;
                }

                .auth-layout__bottom-text {
                    font-size: 10px !important;
                }

                .reserved-rights {
                    font-size: 10px !important;
                }
            }
        }
    }

    .left-section {
        .form-section {
            padding-top: 2rem !important;

            h2 {
                font-size: 26px !important;
            }

            p {
                font-size: 12px !important;
            }

            .auth-layout__forgot-password {
                font-size: 12px !important;
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    .right-section {
        .right-content {
            .blur-content {
                h1 {
                    font-size: 24px !important;
                }

                p {
                    font-size: 10px !important;
                }

                .auth-layout__bottom-text {
                    font-size: 8px !important;
                }
            }
        }
    }

    .left-section {
        .form-section {
            padding-top: 1rem !important;

            h2 {
                font-size: 24px !important;
            }

            p {
                font-size: 12px !important;
            }

            .auth-layout__forgot-password {
                font-size: 12px !important;
            }
        }
    }
}

@media screen and (min-width: 992px) and (max-width: 1400px) {
    .action-button {
        flex-direction: column;
    }
}

@media screen and (max-width: 992px) {
    .auth-layout {
        .left-section {
            .form-section {
                max-width: 500px;

                h2 {
                    font-size: 28px !important;
                }

                p {
                    font-size: $font-size-14;
                }
            }
        }
    }

    .wrapper {
        #sidebar {
            width: 0;
            display: none;
            margin-left: 0;
            transition: width 0.5s ease;

            &.final-active {
                display: block;
                width: 250px;
                left: 0;
                z-index: 1050;
                transition: width 0.5s ease;
            }
        }

        #content {
            width: 100%;
            left: 0;
            transition: width 0.5s ease;

            &.active {
                left: 0;
                right: 0;
                width: 100%;
                transition: width 0.5s ease;
            }

            .page-info-top-bar {
                .page-name-container {
                    text-align: left;

                    h3 {
                        font-size: 26px !important;
                    }
                }
            }
        }

        .sidebarCollapse span {
            display: none;
        }
    }

    .main-site-container {
        padding: 20px 25px;

        .dashboard-content-container {
            &.section-edit-form {
                .no-padding-left {
                    padding-right: 0px !important;
                }

                .no-padding-right {
                    padding-left: 0px !important;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    #content {
        &.window-content-body {
            width: 100%;
        }
    }

    .breadcrumb-container {
        position: relative;
        padding: 15px !important;

        .breadcrumb-detail-container {
            padding: 0px !important;
        }

        .project-name-container {
            h3 {
                font-size: $font-size-14;
            }
        }

        .menu-icon-button {
            img {
                width: 15px;
            }
        }

        .breadcrumb {
            li {
                font-size: 8px;
            }
        }
    }

    .project-name-container {
        width: calc(100% - 87px);

        .project-name {
            width: 100%;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    }
}

@media screen and (max-width: 576px) {
    .custom-btn {
        min-height: 52px;
        max-width: 52px;
    }

    .filter-btn {
        min-width: 52px;
        min-height: 52px;
    }

    .text-fit {
        white-space: nowrap;
        font-size: 4.6vw;
    }

    .custom-search-bar-outer {
        max-width: unset !important;
        min-width: unset !important;
        width: fit-content !important;

        .custom-search-bar {
            padding-right: unset !important;
            width: 250px !important;
        }
    }

    .custom-text-button {
        display: none !important;
        max-height: 40px;
    }

    .terms-privacy-page {
        .address {
            font-size: $font-size-14 !important;
        }
    }
}

@media screen and (max-width: 480px) {
    .auth-layout {
        min-height: unset;
        height: 100% !important;

        .left-section {
            min-height: 600px;
            height: 100% !important;

            .form-section {
                min-height: unset;
                height: 100% !important;
                padding: 3rem 1.5rem;
                padding-bottom: 0px !important;

                .auth-input-wrap {
                    display: flex;
                    justify-content: center;
                    margin-bottom: 0;

                    input {
                        max-width: 42px;
                        max-height: 42px;
                        border-radius: 12px;
                    }
                }

                h2 {
                    font-size: 22px !important;
                    line-height: 35px !important;
                }

                .reserved-rights {
                    font-size: 12px;
                    left: 50%;
                    white-space: nowrap;
                    transform: translateX(-50%);
                }
            }

            .qr-code-scanner-outer-container {
                h2 {
                    font-size: 18px !important;
                    line-height: 20px !important;
                }

                .qr-scanner {
                    img {
                        max-width: 120px;
                    }
                }

                .copy-link-container {
                    height: 50px;
                }
            }
        }
    }

    .clear-btn {
        min-width: 120px !important;
    }

    .apply-btn {
        min-width: 120px !important;
    }

    .tab-change-container {
        .tab-button {
            font-size: $font-size-14;
        }
    }

    .wrapper {
        #sidebar {
            display: none;

            &.active {
                display: block;
                border: 1px solid black;
                min-width: unset;
                width: 100px;
            }
        }

        #content {
            &.mobile-content-body {
                width: 50%;
            }

            .page-info-top-bar {
                padding: 10px;

                .page-name-container {
                    text-align: left;

                    h3 {
                        font-size: 18px !important;
                        line-height: 30px;
                        font-weight: $font-weight-600;
                    }

                    .breadcrumb {
                        margin-top: -5px !important;

                        .breadcrumb-item {
                            a {
                                font-size: 12px;
                            }

                            &::before {
                                height: 12px;
                                overflow: hidden;
                                display: inline;
                            }
                        }
                    }
                }
            }
        }
    }

    .main-site-container {
        padding: 15px 10px;

        .dashboard-content-container {
            padding: 20px 10px;
        }

        .padding-0 {
            padding: 0px !important;
        }
    }

    .dashboard-main-filter-section {
        flex-direction: column;

        .custom-search-bar-outer {
            width: 100% !important;

            .custom-search-bar {
                padding-right: unset !important;
                width: 100% !important;
            }
        }
    }

    .site-cancel-button {
        min-width: 100%;
    }
}

@media (min-width: 576px) {
    .terms-privacy-page {
        p {
            font-size: $font-size-14 !important;
            font-weight: $font-weight-500 !important;
        }

        li {
            font-size: $font-size-14 !important;
        }
    }

    .custom-medium-button {
        @include button-style(48px, 18px, 64px);
        max-width: 64px;
    }

    .custom-large-button {
        @include button-style(48px, 18px, 64px);
        max-width: 64px;
    }
}

@media (min-width: 767px) {
    .custom-buttons-container {
        flex-direction: row;

        .save-button {
            max-width: 130px;
            @include button-style(48px, 18px, 130px);
        }
    }

    .custom-medium-button {
        @include button-style(48px, 18px, 140px);
        max-width: 140px;
        white-space: nowrap;
    }
}

@media (min-width: 992px) {
    .custom-medium-button {
        @include button-style(48px, 18px, 140px);
        max-width: 160px;
    }

    .custom-large-button {
        @include button-style(48px, 18px, 140px);
        max-width: 160px;
    }

    .custom-buttons-container {
        .save-button {
            @include button-style(48px, 18px, 130px);
            max-width: 200px;
        }
    }
}