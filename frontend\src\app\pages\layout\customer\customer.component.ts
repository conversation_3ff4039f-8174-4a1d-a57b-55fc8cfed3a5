// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-party modules
import { NgbAccordionModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { NgxMaskPipe, provideNgxMask } from 'ngx-mask';

// Application-Specific Imports
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { CommonService } from '../../../shared/services/common.service';

// Managers
import { CustomerManager } from './customer.manager';

// Custom services
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';

//Constant
import { Constant } from '../../../config/constants';

//Directive
import { RippleEffectDirective } from '../../../shared/directives/ripple-effect.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';

//custom Pipes
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';

//Models
import { RestResponse } from '../../../models/common/auth.model';
import { Customer } from '../../../models/customer/customer';

//custom Component
import { Subject } from 'rxjs';
import { FilterParam } from '../../../models/common/filter-param';
import { NgCustomDateRangePickerComponent } from '../../../shared/common-component/ng-custom-date-range-picker/ng-custom-date-range-picker.component';
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { TypeAheadFilterService, TypeaheadSearchConfig } from '../../../shared/services/typeahead.service';

@Component({
    selector: 'app-customer',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        DateFormatPipe,
        DelayedInputDirective,
        FormsModule,
        NgbAccordionModule,
        NgbTooltipModule,
        NgCustomDateRangePickerComponent,
        NgSelectModule,
        NgxMaskPipe,
        RippleEffectDirective,
        RouterLink,
        StatusBadgeDirective,
        TooltipEllipsisDirective,
        TranslateModule,
    ],
    templateUrl: './customer.component.html',
    styleUrls: ['./customer.component.scss'],
    providers: [provideNgxMask()],
})
export class CustomerComponent extends BaseListServerSideComponent implements OnInit {
    @ViewChild('action') action!: TemplateRef<any>;
    @ViewChild('accountsPayableEmail') accountsPayableEmail!: TemplateRef<string>;
    @ViewChild('address') address!: TemplateRef<string>;
    @ViewChild('city') city!: TemplateRef<string>;
    @ViewChild('createdOn') createdOn!: TemplateRef<string>;
    @ViewChild('email') email!: TemplateRef<string>;
    @ViewChild('name') name!: TemplateRef<string>;
    @ViewChild('phoneNumber') phoneNumber!: TemplateRef<number>;
    @ViewChild('state') state!: TemplateRef<string>;
    @ViewChild('status') status!: TemplateRef<string>;

    customers!: Array<Customer>;

    alertMessage = Constant.ALERT_MESSAGES;
    durationFilter = Constant.DURATION_FILTERS;
    phoneMaskPattern = Constant.MASKS.PHONE_MASK;
    statusList = Constant.STATUS;
    resourceType: string = Constant.RESOURCE_TYPE.CUSTOMER;

    searchCitySubject: Subject<string> = new Subject<string>();
    searchProvinceSubject: Subject<string> = new Subject<string>();

    loadingCityNgSelect!: boolean;
    loadingProvinceNgSelect!: boolean;

    cities: any[] = [];
    provinces: any[] = [];

    private searchConfigs: TypeaheadSearchConfig<any>[] = [
        {
            subject: this.searchCitySubject,
            fetchFunction: this.customerManager.fetchForCitiesDropdownData.bind(this.customerManager),
            updateResults: (results: any[]) => (this.cities = results),
            updateLoading: (isLoading: boolean) => (this.loadingCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.cities.find(city => city.id === this.filterParam.filtering.pickUpCity)
        },
        {
            subject: this.searchProvinceSubject,
            fetchFunction: this.customerManager.fetchForProvinceDropdownData.bind(this.customerManager),
            updateResults: (results: any[]) => (this.provinces = results),
            updateLoading: (isLoading: boolean) => (this.loadingProvinceNgSelect = isLoading),
            selectedItemGetter: () =>
                this.provinces.find(province => province.id === this.filterParam.filtering.deliveryCity)
        }
    ];

    constructor(
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected override router: Router,
        protected customerManager: CustomerManager,
        protected route: ActivatedRoute,
        protected typeAheadService: TypeAheadFilterService
    ) {
        super(customerManager, commonService, toastService, loadingService, router);
        this.setupAllSearches();
    }

    ngOnInit() {
        this.customers = new Array<Customer>();

        this.filterParam.fileName = this.resourceType;

        // --- Bind navigation state params to filter ---
        if (history.state.Filtering) {
            this.filterParam.filtering.createdWithinDays = history.state.Filtering;
        }

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.CUSTOMER;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.CUSTOMER;

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            name: this.name,
            email: this.email,
            accountsPayableEmail: this.accountsPayableEmail,
            phoneNumber: this.phoneNumber,
            address: this.address,
            city: this.city,
            state: this.state,
            status: this.status,
            createdOn: this.createdOn,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override onFetchCompleted() {
        this.customers = this.records.map((data) => Customer.fromResponse(data));
        super.onFetchCompleted();
    }

    updateCustomerStatus(userRecord: any) {
        const message = `${userRecord?.isActive ? this.alertMessage.deActivateMsg : this.alertMessage.activateMsg} customer?`;

        const data = {
            id: userRecord.id,
            isActive: userRecord.isActive,
        };

        this.commonService.confirmActionDialog(
            message,
            this.updateCustomerStatusCallback.bind(this),
            data,
            !userRecord.isActive,
        );
    }

    async updateCustomerStatusCallback(data: { id: string; isActive: boolean }) {
        let customers = new Customer();
        customers.isActive = !data.isActive;

        this.loadingService.show();
        this.customerManager.updateCustomerStatus(data.id, customers).subscribe({
            next: (response: RestResponse) => {
                this.toastService.success(response?.message);
                this.refreshRecord();
            },
            error: (error: any) => {
                this.toastService.error(error?.message);
            },
        });
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadFilter = new FilterParam();

        this.searchConfigs.forEach(config => {
            this.typeAheadService.setupSearchSubscription<any>(
                config.subject,
                typeHeadFilter,
                config.fetchFunction,
                config.updateResults,
                config.updateLoading,
                config.selectedItemGetter
            );
        });
    }
    // end of typeahead search
}