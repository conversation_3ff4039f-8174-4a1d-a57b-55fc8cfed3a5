import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'removeUnderscore',
    standalone: true
})
export class RemoveUnderscorePipe implements PipeTransform {

    transform(value: string): string {
        if (!value) return value;

        // Replace underscores with spaces and capitalize first letter of each word
        return value.toLowerCase().replace(/_/g, ' ')
            .replace(/\b\w/g, char => char.toUpperCase());
    }

}