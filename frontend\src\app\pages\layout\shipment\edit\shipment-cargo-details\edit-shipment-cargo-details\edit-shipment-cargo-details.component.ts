// Angular Core & Common Modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-Party Libraries
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Application Configuration & Constants
import { BaseEditComponent } from '../../../../../../config/base.edit.component';
import { Constant } from '../../../../../../config/constants';

// Services
import { LoadingService } from '../../../../../../services/loading.service';
import { AuthService } from '../../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../../shared/services/common.service';
import { ToastService } from '../../../../../../shared/services/toast.service';

// Reusable / Shared Components & Directives
import { ValidationMessageComponent } from '../../../../../../shared/common-component/validation-message/validation-message.component';
import { AllowNumberOnlyDirective } from '../../../../../../shared/directives/allow-number-only.directive';

// Managers

// Models
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { RestResponse } from '../../../../../../models/common/auth.model';
import { ShipmentCargoDetail } from '../../../../../../models/shipment/shipment-cargo-detail';
import { CurrencyFormatterDirective } from '../../../../../../shared/directives/custom-currency.directive';
import { ShipmentCargoManager } from '../shipment-cargo.manager';

@Component({
    selector: 'app-edit-shipment-cargo-details',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        NgSelectModule,
        TranslateModule,
        ValidationMessageComponent,
        AllowNumberOnlyDirective,
        CurrencyFormatterDirective,
        NgxMaskDirective,
    ],
    templateUrl: './edit-shipment-cargo-details.component.html',
    styleUrl: './edit-shipment-cargo-details.component.scss',
    providers: [provideNgxMask()],
})
export class EditShipmentCargoDetailsComponent extends BaseEditComponent {
    @Input() modalRef!: NgbModalRef;
    @Input() shipmentCargoDetail!: ShipmentCargoDetail;
    @Input() rateSheet!: string | null;
    @Input() isOpenedInModal!: boolean;
    @Input() disabled!: boolean;

    @Output() closeEvent = new EventEmitter<string>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    cargoTypeOptions = Constant.CARGO_TYPE_OPTIONS;
    rateTypes = Constant.RATE_TYPES;
    weightTypeOptions = Constant.WEIGHT_TYPE_OPTIONS;

    freightAmount: any;

    constructor(
        public override toastService: ToastService,
        protected authService: AuthService,
        protected override commonService: CommonService,
        protected override loadingService: LoadingService,
        protected override route: ActivatedRoute,
        protected override router: Router,
        protected override translateService: TranslateService,
        protected shipmentCargoManager: ShipmentCargoManager,
    ) {
        super(shipmentCargoManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        if (!this.shipmentCargoDetail?.id) {
            this.shipmentCargoDetail = new ShipmentCargoDetail();
            this.setId();
            this.setRecord(this.shipmentCargoDetail);
            this.request.isNewRecord = true;
        } else {
            this.request.isNewRecord = false;
            this.shipmentCargoDetail = ShipmentCargoDetail.fromResponse(this.shipmentCargoDetail);
            const qty = this.shipmentCargoDetail.quantity || 0;

            this.freightAmount = {
                freight: qty > 0 ? Number((this.shipmentCargoDetail.freight / qty).toFixed(2)) : 0,
                fuelCharges: qty > 0 ? Number((this.shipmentCargoDetail.fuelCharges / qty).toFixed(2)) : 0,
                gst: qty > 0 ? Number((this.shipmentCargoDetail.gst / qty).toFixed(2)) : 0,
                total: qty > 0 ? Number((this.shipmentCargoDetail.total / qty).toFixed(2)) : 0,
            };
            this.setId();
            this.setRecord(this.shipmentCargoDetail);
        }
    }

    getVolume() {
        const { width = 0, height = 0, length = 0 } = this.shipmentCargoDetail;
        const volume = length * width * height;
        this.shipmentCargoDetail.volume = parseFloat(volume.toFixed(2));
    }

    onQuantityChange() {
        if (!this.freightAmount || !this.shipmentCargoDetail) {
            return; // Exit safely if data is missing
        }

        const {
            freight = 0,
            fuelCharges = 0,
            gst = 0,
            total = 0
        } = this.freightAmount ?? {};

        const quantity = this.shipmentCargoDetail?.quantity ?? 0;

        Object.assign(this.shipmentCargoDetail, {
            freight: parseFloat((freight * quantity).toFixed(2)),
            fuelCharges: parseFloat((fuelCharges * quantity).toFixed(2)),
            gst: parseFloat((gst * quantity).toFixed(2)),
            total: parseFloat((total * quantity).toFixed(2)),
        });
    }

    getFreightValues() {
        const filter = this.filterParam.filtering;

        filter.shipmentId = this.route.snapshot.paramMap.get('id') as string;

        filter.rateType = this.shipmentCargoDetail.rateType ?? null;
        filter.weight = this.shipmentCargoDetail.weightInPounds ?? null;

        this.shipmentCargoManager.getFreightAmount(this.filterParam).then((response: RestResponse) => {
            this.freightAmount = response;

            const { freight = 0, fuelCharges = 0, gst = 0, total = 0 } = this.freightAmount;

            const quantity = this.shipmentCargoDetail?.quantity ?? 0;

            Object.assign(this.shipmentCargoDetail, {
                freight: parseFloat((freight * quantity).toFixed(2)),
                fuelCharges: parseFloat((fuelCharges * quantity).toFixed(2)),
                gst: parseFloat((gst * quantity).toFixed(2)),
                total: parseFloat((total * quantity).toFixed(2)),
            });
        });
    }

    setWeightToLbs() {
        const { weight, weightType } = this.shipmentCargoDetail;
        if (weight == null || undefined) {   // covers both null and undefined
            return;
        }

        this.shipmentCargoDetail.weightInPounds =
            weightType?.toUpperCase() === 'LBS' ? weight : this.shipmentCargoDetail.weight * 2.20462;
    }

    override onSaveSuccess(data: any) {
        if (this.isOpenedInModal) {
            this.saveButtonClicked.emit();
            this.modalRef?.close();
        }
    }

    private setId() {
        this.shipmentCargoDetail.shipment = this.route.snapshot.paramMap.get('id') as string;
    }

    generateTotalAmount() {
        const { freight = 0, fuelCharges = 0, gst = 0 } = this.shipmentCargoDetail;

        const total = (freight + fuelCharges + gst);
        this.shipmentCargoDetail.total = parseFloat(total.toFixed(2));
    }
}
