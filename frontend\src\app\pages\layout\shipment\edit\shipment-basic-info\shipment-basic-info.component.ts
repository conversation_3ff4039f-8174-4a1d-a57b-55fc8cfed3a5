// Angular core
import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';

// Angular common and forms
import { CommonModule, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Angular router
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// RxJS
import { lastValueFrom, Subject, Subscription } from 'rxjs';

// App constants and models
import { Constant } from '../../../../../config/constants';
import { FilterParam } from '../../../../../models/common/filter-param';
import { Customer } from '../../../../../models/customer/customer';
import { Shipment } from '../../../../../models/shipment/shipment';

// Shared services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared components
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Feature managers
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { provideNgxMask } from 'ngx-mask';
import { User } from '../../../../../models/access/user';
import { DialCodeInputComponent } from '../../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { CustomerManager } from '../../../customer/customer.manager';
import { EmployeesManager } from '../../../employees/employees.manager';
import { RateSheetManager } from '../../../ratesheet/rate-sheet.manager';
import { VehicleManager } from '../../../vehicle/vehicle.manager';
import { ShipmentManager } from '../../shipment.manager';

@Component({
    selector: 'app-shipment-basic-info',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        TranslateModule,
        NgClass,
        NgSelectModule,
        ValidationMessageComponent,
        DialCodeInputComponent,
    ],
    templateUrl: './shipment-basic-info.component.html',
    styleUrl: './shipment-basic-info.component.scss',
    providers: [provideNgxMask()],
})
export class ShipmentBasicInfoComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() userRole!: string;
    @Input() disabled!: boolean;
    @Input() shipmentStatusOptions = Constant.SHIPMENT_STATUS_OPTIONS;
    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();
    @Output() onfetchDropdownDataCompleted = new EventEmitter<boolean>();

    searchCustomerSubject: Subject<string> = new Subject<string>();
    searchDriverSubject: Subject<string> = new Subject<string>();

    loadingCustomerNgSelect!: boolean;
    loadingDriverNgSelect!: boolean;

    hasTriggered: boolean = false;

    routerSubscription?: Subscription;

    title!: string;
    searchConfigs = [
        {
            subject: this.searchCustomerSubject,
            endpoint: 'shipment/selection',
            fetchFunction: this.customerManager.fetchForDropdownData.bind(this.customerManager),
            updateResults: (results: any) => (this.customers = results),
            updateLoading: (isLoading: boolean) => (this.loadingCustomerNgSelect = isLoading),
        },
        {
            subject: this.searchDriverSubject,
            fetchFunction: this.employeesManager.fetchForDriverDropdown.bind(this.employeesManager),
            updateResults: (results: any) => (this.drivers = results),
            updateLoading: (isLoading: boolean) => (this.loadingDriverNgSelect = isLoading),
        },
    ];

    showToEndDateField: boolean = true;
    updateToEndDateField: boolean = true;

    shipmentPaymentStatusOptions = Constant.SHIPMENT_PAYMENT_STATUS_OPTIONS;
    shipmentTypeOptions = Constant.SHIPMENT_TYPE_OPTIONS;
    shipmentPaymentTypeOptions = Constant.SHIPMENT_PAYMENT_TYPE_OPTIONS;

    customers: Customer[] = [];
    drivers: User[] = [];

    vehicleModelRef!: NgbModalRef;

    openDatePicker: 'startDate' | 'endDate' | 'etd' | null = null;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected customerManager: CustomerManager,
        protected shipmentManager: ShipmentManager,
        protected vehicleManager: VehicleManager,
        protected employeesManager: EmployeesManager,
        protected rateSheetManager: RateSheetManager,
        protected typeAheadService: TypeAheadService,
        protected modalService: NgbModal,
    ) {
        this.setupAllSearches();
    }

    ngOnInit(): void {
        this.userRole = this.authService.getRoles()[0];
        this.fetchDropdownData();
        if (this.request.recordId === '0') {
            this.shipmentManager.fetchShipmentRefId().subscribe({
                next: (refId) => {
                    if (refId) {
                        this.shipment.refID = refId;
                    }
                },
            });
        }
        this.onfetchDropdownDataCompleted.emit(false);
        this.routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart && this.vehicleModelRef) {
                this.vehicleModelRef.close();
            }
        });
    }

    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }

    async ngDoCheck(): Promise<void> {
        if (!this.hasTriggered && this.shipment?.customer) {
            this.hasTriggered = true;
            this.addObjectToExistingDropdown();
        }
    }

    async fetchDropdownData() {
        this.loadingService.show();

        this.filterParam.pagination.next = 10;
        this.filterParam.pagination.offset = 1;

        const [customers, drivers] = await Promise.all([
            lastValueFrom(this.customerManager.fetchForDropdownData(this.filterParam, 'shipment/selection')),
            lastValueFrom(this.employeesManager.fetchForDriverDropdown(this.filterParam)),
        ]);

        this.customers = customers;
        this.drivers = drivers;
        this.addObjectToExistingDropdown();
        this.loadingService.hide();
        this.onfetchDropdownDataCompleted.emit(true);
    }

    addUniqueItem(list: any[] = [], item: any) {
        if (item?.id && !this.isDuplicate(list, item)) {
            return [...list, item];
        }
        return list;
    }

    addObjectToExistingDropdown() {
        const { customerUserDetail, driverUserDetail } = this.shipment;

        this.customers = this.addUniqueItem(this.customers, customerUserDetail);
        this.drivers = this.addUniqueItem(this.drivers, driverUserDetail);
    }

    isDuplicate(array: any, item: any) {
        return array?.some((existingItem: { id: any }) => existingItem.id === item.id);
    }

    getCustomerDetails({ customerDetail }: any): void {
        if (!customerDetail) return;

        const { keyContact, keyContactEmail, keyContactPhone } = customerDetail;

        Object.assign(this.shipment, {
            contactPersonName: keyContact,
            contactPersonEmail: keyContactEmail,
            contactPersonPhone: keyContactPhone,
        });
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    onDriveSelection(data: any) {
        if (data) {
            this.shipment.status = 'ASSIGNED';
        }
    }

    openModal(content: TemplateRef<any>, title: string, isCertificateTypeModal: boolean): void {
        this.vehicleModelRef = this.modalService.open(content, { centered: true, backdrop: 'static' });
        this.title = title;
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.onNextClick.emit(2);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.saveButtonClicked.emit();
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadParam = new FilterParam();
        this.searchConfigs.forEach((config) => {
            this.typeAheadService.setupSearchSubscription(
                config.subject,
                typeHeadParam,
                (filterParam) => lastValueFrom(config.fetchFunction(filterParam, config.endpoint)),
                config.updateResults,
                config.updateLoading,
            );
        });
    }
}
