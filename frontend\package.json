{"name": "iotasol-frontend", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.10", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.10", "@angular/forms": "^17.0.0", "@angular/google-maps": "^19.2.17", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.10", "@augwit/ng2-file-upload": "^6.0.0", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@google/maps": "^1.1.3", "@googlemaps/js-api-loader": "^1.16.8", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "@ng-select/ng-select": "^12.0.7", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@swimlane/ngx-charts": "^22.0.0", "@types/googlemaps": "^3.43.3", "angular-datatables": "^17.1.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "datatables.net": "^2.0.8", "datatables.net-dt": "^2.0.8", "i": "^0.3.7", "jquery": "^3.7.1", "luxon": "^3.6.1", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "ng2-file-upload": "^7.0.1", "ngx-mask": "^18.0.0", "ngx-material-timepicker": "^13.1.1", "npm": "^10.9.2", "rxjs": "~7.8.0", "signature_pad": "^5.0.7", "sweetalert2": "^11.6.13", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.17", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.0", "@angular/localize": "^17.3.8", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/datatables.net": "^1.12.0", "@types/google.maps": "^3.58.1", "@types/jasmine": "~5.1.0", "@types/jquery": "^3.5.30", "@types/moment": "^2.13.0", "jasmine-core": "~5.1.0", "karma": "^6.3.17", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.5.3", "prettier-plugin-import-sort": "^0.0.7", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-sort-json": "^4.1.1", "typescript": "~5.4.2"}}