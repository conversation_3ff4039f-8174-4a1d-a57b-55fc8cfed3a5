import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';

import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-tab-menu',
    standalone: true,
    imports: [CommonModule, NgbTooltipModule],
    templateUrl: './tab-menu.component.html',
    styleUrl: './tab-menu.component.scss',
})
export class TabMenuComponent {
    @Input() stepperList: any;
    @Input() lastStoredStep!: number;
    @Input() hiddenStepNumber!: number;
    @Output() updatedStep = new EventEmitter<any>();

    constructor(protected changeDetectorRef: ChangeDetectorRef) {}

    ngOnChanges(): void {
        this.changeDetectorRef.detectChanges();
    }

    stepSelection(stepNumber: number) {
        this.updatedStep.emit(stepNumber);
    }
}
