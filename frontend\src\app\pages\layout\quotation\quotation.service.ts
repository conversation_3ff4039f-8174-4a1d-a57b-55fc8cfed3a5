import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class QuotationService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/quotation', '/api/quotations');
    }

    fetchQuotationRefId(): Observable<RestResponse> {
        return this.getRecord('/api/quotation/refId');
    }

    fetchSpecialPrice(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/ratesheet/special/charges', filterParam);
    }
}
