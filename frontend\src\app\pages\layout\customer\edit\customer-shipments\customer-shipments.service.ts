import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../../../config/base.service';
import { RestResponse } from '../../../../../models/common/auth.model';

@Injectable({
    providedIn: 'root',
})
export class CustomerShipmentsService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/account/customer', '/api/Shipments');
    }

    updateUserStatus(id: string, data: any): Observable<RestResponse> {
        return this.updateRecord(`/api/users/${id}/status`, data);
    }
}
