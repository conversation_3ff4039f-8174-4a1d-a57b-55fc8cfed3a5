import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { IResourceWithId, RestResponse } from '../models/common/auth.model';
import { FilterParam } from '../models/common/filter-param';
import { HttpServiceRequests } from '../shared/services/http.service';

export abstract class BaseService extends HttpServiceRequests<IResourceWithId> {
    constructor(
        public override http: HttpClient,
        private entityBaseUrl: string,
        private entityPluralUrl: string,
    ) {
        super(http);
    }

    exportToExcel(filterParam: FilterParam, customURL?: string): Observable<RestResponse> {
        const url = customURL ?? this.entityBaseUrl + '/export/to/excel';
        return this.getRecords(url, filterParam);
    }

    exportToPDF(filterParam: FilterParam, id: string): Observable<RestResponse> {
        return this.getRecord(this.entityBaseUrl + '/pdf/download/' + id);
    }

    fetch(id: string): Observable<RestResponse> {
        return this.getRecord(this.entityBaseUrl + '/' + id);
    }

    fetchAll(filterParam: FilterParam | null): Observable<RestResponse> {
        return this.getRecords(this.entityPluralUrl, filterParam);
    }

    // endpoint is optional if users need to pass their specific endpoint
    fetchForDropdown(filterParam: FilterParam, endpoint: string = 'Selection'): Observable<RestResponse> {
        return this.getRecords(`${this.entityBaseUrl}/${endpoint}`, filterParam);
    }

    remove(id: string): Observable<RestResponse> {
        return this.removeRecord(this.entityBaseUrl + '/' + id);
    }

    save(data: any, hasResponse?: boolean): Observable<RestResponse> {
        const URL = hasResponse ? `${this.entityBaseUrl}?returnDetailedResponse=true` : this.entityBaseUrl;
        return this.saveRecord(URL, data);
    }

    update(data: any, hasResponse?: boolean): Observable<RestResponse> {
        const URL = hasResponse ? `${this.entityBaseUrl}?returnDetailedResponse=true` : this.entityBaseUrl;
        return this.updateRecord(URL, data);
    }
}
