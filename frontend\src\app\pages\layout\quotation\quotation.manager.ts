import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { QuotationService } from './quotation.service';

@Injectable({
    providedIn: 'root',
})
export class QuotationManager extends BaseManager {
    stepperList: any[] = [
        {
            stepNumber: 1,
            stepTitle: 'Basic Details',
            stepPath: '/assets/images/icons/information-sign.svg',
            selected: true,
        },
        {
            stepNumber: 2,
            stepTitle: 'Pickup Delivery',
            stepPath: '/assets/images/icons/delivery_truck_speed.svg',
            selected: false,
        },
        {
            stepNumber: 3,
            stepTitle: 'Cargo Details',
            stepPath: '/assets/images/icons/Cargo_detail_icon.svg',
            selected: false,
        },
        {
            stepNumber: 4,
            stepTitle: 'Special Request',
            stepPath: '/assets/images/svg/Special-request-icon.svg',
            selected: true,
        },
        {
            stepNumber: 5,
            stepTitle: 'Calculations',
            stepPath: '/assets/images/svg/Calculation_icon.svg',
            selected: false,
        },
        {
            stepNumber: 6,
            stepTitle: 'Documents',
            stepPath: '/assets/images/icons/file.svg',
            selected: false,
        },
    ];

    constructor(
        protected quotationService: QuotationService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(quotationService, loadingService, toastService);
    }

    fetchQuotationRefId(): Observable<string | null> {
        return this.quotationService
            .fetchQuotationRefId()
            .pipe(map((response: RestResponse) => response?.data?.refID ?? null));
    }

    fetchSpecialPrice(param: FilterParam): Observable<string | null> {
        return this.quotationService
            .fetchSpecialPrice(param)
            .pipe(map((response: RestResponse) => response?.data ?? null));
    }
}
