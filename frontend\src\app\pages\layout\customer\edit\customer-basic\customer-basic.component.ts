// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Customer } from '../../../../../models/customer/customer';

// Custom components
import { CustomAddressComponent } from '../../../../../shared/common-component/custom-address/custom-address.component';
import { DialCodeInputComponent } from '../../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { EmailValidatorDirective } from '../../../../../shared/directives/email-validator.directive';
import { NoWhitespaceValidatorDirective } from '../../../../../shared/directives/no-whitespace-validator.directive';
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';

@Component({
    selector: 'app-customer-basic',
    standalone: true,
    imports: [
        CommonModule,
        CustomAddressComponent,
        DialCodeInputComponent,
        EmailValidatorDirective,
        FormsModule,
        NgSelectModule,
        NgSelectModule,
        NoWhitespaceValidatorDirective,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './customer-basic.component.html',
    styleUrl: './customer-basic.component.scss',
})
export class CustomerBasicComponent {
    @Input() customer!: Customer;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    customerStatus = Constant.CUSTOMER_STATUS;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
    ) { }

    handleCancelClick() {
        this.router.navigate(['/dashboard/customers']);
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.onNextClick.emit(2);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.saveButtonClicked.emit();
    }
}
