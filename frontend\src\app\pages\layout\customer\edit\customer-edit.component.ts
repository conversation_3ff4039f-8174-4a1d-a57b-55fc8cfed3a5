// Angular core modules
import { CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { provideNgxMask } from 'ngx-mask';

// Utility classes and base components
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { Constant } from '../../../../config/constants';
import { CommonUtil } from '../../../../shared/common.util';

// Models
import { RestResponse } from '../../../../models/common/auth.model';
import { Customer } from '../../../../models/customer/customer';

// Custom services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonEventService } from '../../../../shared/services/common.event.service';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

//Manager
import { CustomerManager } from '../customer.manager';

// Custom components
import { Address } from '../../../../models/common/address';
import { TabMenuComponent } from '../../../../shared/tab-menu/tab-menu/tab-menu.component';
import { CustomerBasicComponent } from './customer-basic/customer-basic.component';
import { CustomerCompanyContactsComponent } from './customer-company-contacts/customer-company-contacts.component';
import { CustomerDocumentsComponent } from './customer-documents/customer-documents.component';
import { CustomerShipmentsComponent } from './customer-shipments/customer-shipments.component';

@Component({
    selector: 'app-customer-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TranslateModule,
        NgSelectModule,
        TabMenuComponent,
        NgSelectModule,
        CustomerBasicComponent,
        CustomerCompanyContactsComponent,
        CustomerDocumentsComponent,
        CustomerShipmentsComponent,
    ],
    templateUrl: './customer-edit.component.html',
    styleUrls: ['./customer-edit.component.scss'],
    providers: [CommonEventService, provideNgxMask()],
})
export class CustomerEditComponent extends BaseEditComponent implements OnInit {
    selectedStep = 1;
    customer!: Customer;
    customerStatus = Constant.CUSTOMER_STATUS;
    stepperList = this.customerManager.stepperList;

    constructor(
        protected override route: ActivatedRoute,
        protected customerManager: CustomerManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        public authService: AuthService,
        protected override translateService: TranslateService,
        public commonUtil: CommonUtil,
        protected location: Location,
    ) {
        super(customerManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.initializeCustomer();

        this.selectedStep = history.state.step <= this.stepperList.length ? history.state.step : 1;
        this.customer.customerDetail.step =
            history.state.step <= this.stepperList.length ? this.selectedStep : this.customer.customerDetail.step;

        if (history.state.firstLoad) {
            this.selectedStep = 1;
        }

        this.onStepSelection();
    }

    onSelectionCallBack(newSelection: number): void {
        const lastStoredStep = history.state.step ?? 1;
        if (newSelection === 3) {
            this.init();
        }
        if (lastStoredStep <= this.stepperList.length) {
            if (newSelection <= lastStoredStep) {
                this.onClickValidation = false;
                this.selectedStep = newSelection;
                this.onStepSelection();
                return;
            }
        } else {
            this.selectedStep = newSelection;
            this.onStepSelection();
            return;
        }

        this.onClickValidation = true;
        this.loadingService.hide();
    }

    onStepSelection(): void {
        this.stepperList.forEach((step: any) => {
            step.selected = step.stepNumber === this.selectedStep;
        });
    }

    tabMenuUpdate(step: number): void {
        if (step > this.selectedStep) {
            this.saveCustomer(step);
        } else {
            this.onSelectionCallBack(step);
        }
    }

    override onFetchCompleted() {
        this.customer = Customer.fromResponse(this.record);
        this.customer.addressDetail = this.customer.addressDetail ?? new Address();
        this.setRecord(this.customer);
    }

    override onSaveSuccess(data: any) {
        this.navigate('/dashboard/customers');
    }

    async saveCustomer(step?: number): Promise<void> {
        if (this.customer.customerDetail.step <= this.stepperList.length) {
            if (step) {
                this.customer.customerDetail.step = Math.max(step, this.customer.customerDetail.step);
            } else if (this.selectedStep === history.state.step) {
                this.customer.customerDetail.step += 1;
            }
        }

        const method = !this.customer?.id ? 'save' : 'update';
        const forRequestData = JSON.parse(JSON.stringify(this.customer));

        if (
            !forRequestData.addressDetail?.address ||
            !forRequestData.addressDetail?.city ||
            !forRequestData.addressDetail?.state ||
            !forRequestData.addressDetail?.pin ||
            !forRequestData.addressDetail?.country
        ) {
            this.onClickValidation = true;
            return;
        }

        this.loadingService.show();

        this.customerManager[method](this.cleanPayload(forRequestData), !!step).subscribe({
            next: (response: RestResponse) => {
                if (step) {
                    this.selectedStep = step;
                    this.customer = response.data;
                    this.setRecord(this.customer);

                    this.request = {
                        isNewRecord: !this.customer.id,
                        recordId: this.customer.id || 0,
                    };

                    if (this.customer.customerDetail.step <= this.stepperList.length) {
                        this.updateUrlWithNewId(this.customer.id, this.customer.customerDetail.step);
                    }

                    this.loadingService.hide();
                    this.onStepSelection();
                } else {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                    this.onSaveSuccess(response.data);
                }
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    private initializeCustomer(): void {
        this.customer = new Customer();
        this.setRecord(this.customer);

        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;
        this.init();
    }

    private updateUrlWithNewId(newId: string, step: number) {
        const url = `/dashboard/customer/edit/${newId}`;
        const newState = { step, firstLoad: true };
        this.location.replaceState(url, '', newState);
    }
}
