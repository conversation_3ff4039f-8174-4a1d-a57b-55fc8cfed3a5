import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { RestResponse } from '../models/common/auth.model';
import { FilterParam } from '../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class AccountService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/userProfile', '/api/userProfiles');
    }

    changePassword(data: any): Observable<RestResponse> {
        return this.saveRecord('/api/account/change/password', data);
    }

    fetchMe(): Observable<RestResponse> {
        return this.getRecord('/api/account/userProfile');
    }

    updateMyProfile(data: any): Observable<RestResponse> {
        return this.updateRecord('/api/account/userProfile', data);
    }
    getDashboardDataByFilter(filterParam: FilterParam) {
        return this.getRecords('/api/account/dashboard/count', filterParam);
    }
}
