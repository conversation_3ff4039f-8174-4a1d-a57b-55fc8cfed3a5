<div class="site-page-container mt-3">
    <div class="site-card">
        <form #barCodeForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row  justify-content-center">

                <div class="custom-responsive-row row modal-border">

                    <!-- start of section for the basic information -->
                    <ng-container title="Basic Information" class="">
                        <!-- Reference ID -->
                        <div class="col-md-12 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="barcodeNo" [maxlength]="3"
                                    #barcodeNo="ngModel" [(ngModel)]="barcode.barCodeCount" appAllowNumberOnly
                                    placeholder="Reference/Load ID" required
                                    [ngClass]="{'is-invalid': !barcodeNo.valid && onClickValidation }">
                                <label for="referenceId">{{"BARCODE.barCodeNumbers" | translate}}</label>
                                <app-validation-message [field]="barcodeNo" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                    </ng-container>
                    <!-- end of section for the basic information -->
                </div>
                <!-- Buttons -->
                <div class="clearfix"></div>
                <div class="col-md-12 custom-buttons-container">
                    <button class="btn cancel-button" appRippleEffect type="button" (click)="cancel()">
                        {{ "COMMON.CANCEL" | translate }}
                    </button>
                    <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                        (click)="saveBarCode(barCodeForm.form)">
                        <div class="site-button-inner">
                            {{ "COMMON.SAVE" | translate }}
                        </div>
                    </button>
                    <div class="clearfix"></div>
                </div>

            </div>
        </form>
    </div>
</div>