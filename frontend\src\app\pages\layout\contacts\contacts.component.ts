// Angular core/common modules
import { CommonModule } from '@angular/common';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgbModal, NgbModalRef, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { NgxMaskPipe, provideNgxMask } from 'ngx-mask';

// Base components and configuration
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';

// Model
import { Constant } from '../../../config/constants';

// Services
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';

// Directives
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';

// Pipes
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { RemoveUnderscorePipe } from '../../../shared/pipes/remove-underscore.pipe';

// Local manager
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Contacts } from '../../../models/access/contacts';
import { RestResponse } from '../../../models/common/auth.model';
import { ValidationMessageComponent } from '../../../shared/common-component/validation-message/validation-message.component';
import { ContactsManager } from './contacts.manager';

@Component({
  selector: 'app-contacts',
  standalone: true,
  imports: [CommonModule, TranslateModule,
    NgbTooltipModule, TooltipEllipsisDirective, FormsModule, StatusBadgeDirective, NgxMaskPipe, NgSelectModule, RemoveUnderscorePipe, DateFormatPipe, DataTablesModule, DelayedInputDirective, DelayedInputDirective, ValidationMessageComponent],
  templateUrl: './contacts.component.html',
  styleUrl: './contacts.component.scss',
  providers: [provideNgxMask()],
})
export class ContactsComponent extends BaseListServerSideComponent {
  @ViewChild('companyName') companyName!: TemplateRef<string>;
  @ViewChild('personName') personName!: TemplateRef<string>;
  @ViewChild('contact') contact!: TemplateRef<string>;
  @ViewChild('email') email!: TemplateRef<string>;
  @ViewChild('description') description!: TemplateRef<string>;
  @ViewChild('subject') subject!: TemplateRef<string>;
  @ViewChild('status') status!: TemplateRef<string>;
  @ViewChild('createdOn') createdOn!: TemplateRef<string>;

  resourceType: string = Constant.RESOURCE_TYPE.CONTACTS;
  contacts!: Array<Contacts>;
  phoneMaskPattern = Constant.MASKS.PHONE_MASK;
  modalRef!: NgbModalRef;
  contactUsStatus = Constant.CONTACT_US_STATUS;
  contactUsDetail!: Contacts;
  newContactUsStatus!: string | null;
  onClickValidation!: boolean

  constructor(
    protected contactsManager: ContactsManager,
    protected override toastService: ToastService,
    protected override loadingService: LoadingService,
    protected override commonService: CommonService,
    protected route: ActivatedRoute,
    protected override router: Router,
    public modalService: NgbModal,
  ) {
    super(contactsManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.contacts = new Array<Contacts>();

    this.filterParam.fileName = this.resourceType;

    this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.CONTACTS;
    this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.CONTACTS;

    this.init();
  }

  ngAfterViewInit() {
    const templates = {
      companyName: this.companyName,
      personName: this.personName,
      contact: this.contact,
      email: this.email,
      subject: this.subject,
      description: this.description,
      status: this.status,
      createdOn: this.createdOn,
    };

    this.setupColumns(templates);
  }

  openModal(content: TemplateRef<any>, data: Contacts) {
    this.onClickValidation = false;
    this.newContactUsStatus = null
    this.modalRef = this.modalService.open(content, { centered: true, backdrop: 'static' });
    this.contactUsDetail = data;
    this.contactUsStatus = Constant.CONTACT_US_STATUS;
    this.contactUsStatus = this.contactUsStatus.filter(item => item.id !== data.status);
  }

  updateStatus() {
    if (!this.newContactUsStatus) {
      this.onClickValidation = true;
      return
    }

    const message = Constant.ALERT_MESSAGES.contactUsStatusUpdate;
    this.commonService.confirmation(message, this.updateStatusCallBack.bind(this));
  }

  updateStatusCallBack() {
    let data = {
      id: this.contactUsDetail.id,
      status: this.newContactUsStatus
    }
    this.loadingService.show();
    this.contactsManager.updateContactUsStatus(data).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.success(response?.message);
        this.modalRef.close();
        this.contactUsDetail.status = this.newContactUsStatus;
        // this.refreshRecord();
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.error(error.message);
      },
    });
  }

  override onFetchCompleted() {
    this.contacts = this.records.map((data) => Contacts.fromResponse(data));
    super.onFetchCompleted();
  }
}
