<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar" placeholder="Search by Cargo Type..."
                    appDelayedInput (delayedInput)="search($event)" [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" *ngIf="!disabled" appRippleEffect
                    (click)="openModal(content,'Cargo Details')">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>
    </div>

    <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
        <div ngbAccordionItem="filter" class="border-0">
            <div ngbAccordionCollapse>
                <div ngbAccordionBody class="filter-container p-4 w-100">
                    <ng-template>
                        <div class="row gx-0 gy-3">
                            <!-- Delivery Address  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" name="rateType" required="required">
                                    </ng-select>
                                    <label for="rateType" class="ng-select-label">{{
                                        "RateSheetWeightCharge.rateType" | translate
                                        }}</label>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': shipmentCargoDetails.length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'SHIPMENT.itemDescription' | translate }}</th>
                            <th>{{ 'QuotationItem.cargoType' | translate }}</th>
                            <th class="text-end">{{ 'QuotationItem.weight' | translate }} {{'COMMON.inLbs' | translate}}
                            </th>
                            <th class="text-end">{{ 'QuotationItem.volume' | translate }}</th>
                            <th class="text-end">{{ 'SHIPMENT.freight' | translate }}</th>
                            <th class="text-end">{{ 'SHIPMENT.fuelCharges' | translate }}</th>
                            <th class="text-end">{{ 'QuotationItem.gstAmount' | translate }}</th>
                            <th class="text-end">{{ 'RateSheetWeightCharge.total' | translate }}</th>
                            <th class="th-action text-center">{{ 'COMMON.ACTION' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #description let-data="adtData">
                <div (click)="openModal(content,'Edit shipment cargo', data)" class="ellipsis-2-line"
                    [appTooltipEllipsis]="data?.description" ngbTooltip><strong>{{ data?.description }}</strong>
                </div>
            </ng-template>

            <ng-template #cargoType let-data="adtData">
                <div class="status-color-badge cargo-type">
                    {{ data?.cargoType | titlecase | removeUnderscore }}
                </div>
            </ng-template>

            <ng-template #WeightInPounds let-data="adtData">
                <div class="td-align-right">{{ data?.weightInPounds }} </div>
            </ng-template>

            <ng-template #volume let-data="adtData">
                <div class="td-align-right">{{ data.volume }} </div>
            </ng-template>

            <ng-template #gst let-data="adtData">
                <div class="td-align-right">{{ data?.gst | currency }} </div>
            </ng-template>

            <ng-template #freight let-data="adtData">
                <div class="td-align-right">{{ data?.freight | currency }} </div>
            </ng-template>

            <ng-template #total let-data="adtData">
                <div class="td-align-right">{{ data?.total | currency }} </div>
            </ng-template>

            <ng-template #FuelCharges let-data="adtData">
                <div class="td-align-right">{{ data?.fuelCharges | currency}} </div>
            </ng-template>

            <ng-template #action let-data="adtData">
                <div class="action-icons justify-content-center">
                    <button class="edit-btn" (click)="openModal(content,'Edit Cargo Details', data)"
                        ngbTooltip="Edit shipment cargo">
                        <img src="/assets/images/icons/edit-icon.svg" alt="Edit" />
                    </button>
                    <button class="delete-btn" (click)="remove(data.id, resourceType)"
                        ngbTooltip="Delete shipment cargo" *ngIf="!disabled && data.isDeletionAllowed">
                        <img src="/assets/images/icons/delete-icon.svg" alt="delete" />
                    </button>
                </div>
            </ng-template>
        </div>
    </div>
</div>

<ng-container>
    <div class="col-md-12 custom-buttons-container mt-0">
        <button class="btn cancel-button" appRippleEffect type="button" [routerLink]="['/dashboard/shipments']">
            {{ "COMMON.CANCEL" | translate }}
        </button>

        <button class="btn custom-medium-button save-button" appRippleEffect type="button" (click)="onBack()">
            <div class="site-button-inner">
                {{ "COMMON.BACK" | translate }}
            </div>
        </button>

        <button class="btn custom-medium-button save-button" appRippleEffect type="button" (click)="onNext()">
            <div class="site-button-inner">
                {{ "COMMON.NEXT" | translate }}
            </div>
        </button>
    </div>
</ng-container>

<ng-template #content let-modal>
    <div class="modal-header mx-2 mx-md-4">
        <h4 class="modal-title">{{title}}</h4>
        <button type="button" class="btn-close modal-close" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>

    <div class="modal-body pt-0 mx-2 mx-md-4">
        <app-edit-shipment-cargo-details [modalRef]="modalRef" [shipmentCargoDetail]="shipmentCargoDetail"
            [isOpenedInModal]="true" (saveButtonClicked)="refreshRecord()" [disabled]="disabled"
            [rateSheet]="shipment.rateSheet">
        </app-edit-shipment-cargo-details>
    </div>
</ng-template>