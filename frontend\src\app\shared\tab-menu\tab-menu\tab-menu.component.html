<div class="display-align-center hide-scrollbar overflow-x-auto mt-4 mx-3">

  @for(item of stepperList; track item){
  <button class="step-container mb-2 flex-row"
    [ngClass]="{'pe-none ': lastStoredStep !== undefined && item.stepNumber > lastStoredStep}"
    (click)="stepSelection(item.stepNumber)" *ngIf="hiddenStepNumber !== item.stepNumber"
    [class.step-container-background]="item.selected">

    <div class="step-logo align-self-center" [class.step-background]="item.selected">
      <img [src]="item.stepPath" alt="info" [class.svg-image]="item.selected" width="26" height="26" />
    </div>

    <div class="step-text d-flex flex-column gap-sm-1">
      <p class="step-number mt-1 align-self-center align-self-sm-start">Step {{item.stepNumber}}</p>
      <p #StepTitle class="step-title text-center text-sm-start fw-medium" placement="auto"
        [ngbTooltip]="StepTitle.scrollWidth > StepTitle.offsetWidth ? item.stepTitle : ''">{{item.stepTitle}}</p>
    </div>
  </button>
  }
</div>