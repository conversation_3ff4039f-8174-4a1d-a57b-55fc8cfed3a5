<div class="site-page-container mt-3">
    <div class="site-card">
        <form #customerForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <div class="label-wrap-box">
                            <span>{{"CustomerShipments.pickupAddress" | translate}} {{"USERS.Information" |
                                translate}}</span>
                        </div>

                        <!-- Company Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="PickupCompanyName"
                                    [required]="!quotation.isQuickQuote" placeholder="{{'USERS.FirstName' | translate}}"
                                    [appNoWhitespaceValidator]="!quotation.isQuickQuote"
                                    [ngClass]="{'is-invalid': !PickupCompanyName.valid && onClickValidation }"
                                    [(ngModel)]="quotation.pickupCompanyName" #PickupCompanyName="ngModel" />
                                <label for="PickupCompanyName">{{"Quotation.pickupCompanyName" | translate}}</label>
                                <app-validation-message [field]="PickupCompanyName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Contact Name field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="pickupContactName"
                                    placeholder="{{'USERS.LastName' | translate}}" [required]="!quotation.isQuickQuote"
                                    [ngClass]="{'is-invalid': !PickupContactPersonName.valid && onClickValidation }"
                                    [appNoWhitespaceValidator]="!quotation.isQuickQuote"
                                    [(ngModel)]="quotation.pickupContactPersonName"
                                    #PickupContactPersonName="ngModel" />
                                <label for="PickupContactPersonName">{{"Quotation.pickupContactPersonName" |
                                    translate}}</label>
                                <app-validation-message [field]="PickupContactPersonName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Phone Number -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14" *ngIf="numberVisibility">
                                <app-dial-code-input [(countryCode)]="quotation.pickupContactCountryCode"
                                    [(number)]="quotation.pickupContactPersonPhone" [required]="!quotation.isQuickQuote"
                                    [onClickValidation]="onClickValidation" fieldName="pickupPhoneNumber"
                                    nameCode="pickupCountryCode"
                                    [labelName]="'Quotation.pickupContactPersonPhone' | translate"></app-dial-code-input>
                            </div>
                        </div>

                        <!-- Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="quotation.pickupAddressDetail"
                            [onClickValidation]="onClickValidation"
                            [addressLabel]="'Quotation.pickupAddress' | translate"></app-custom-address>

                        <!-- below delivery section starts -->
                        <div class="label-wrap-box">
                            <span>{{"CustomerShipments.deliveryAddress" | translate}} {{"USERS.Information" |
                                translate}}</span>
                        </div>

                        <!-- Company Name Field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="deliveryContactPersonName"
                                    #DeliveryContactPersonName="ngModel" [required]="!quotation.isQuickQuote"
                                    [(ngModel)]="quotation.deliveryCompanyName"
                                    placeholder="{{'USERS.FirstName' | translate}}"
                                    [appNoWhitespaceValidator]="!quotation.isQuickQuote"
                                    [ngClass]="{'is-invalid': !DeliveryContactPersonName.valid && onClickValidation }" />
                                <label for="FirstName">{{"Quotation.deliveryCompanyName" | translate}}</label>
                                <app-validation-message [field]="DeliveryContactPersonName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Contact Name field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="lastName"
                                    #DeliveryContactPersonName="ngModel"
                                    [(ngModel)]="quotation.deliveryContactPersonName"
                                    placeholder="{{'USERS.LastName' | translate}}" [required]="!quotation.isQuickQuote"
                                    [appNoWhitespaceValidator]="!quotation.isQuickQuote"
                                    [ngClass]="{'is-invalid': !DeliveryContactPersonName.valid && onClickValidation }" />
                                <label for="LastName">{{"Quotation.deliveryContactPersonName" | translate}}</label>
                                <app-validation-message [field]="DeliveryContactPersonName"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Pickup Phone Number -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating mb-14">
                                <app-dial-code-input [(countryCode)]="quotation.deliveryContactCountryCode"
                                    [(number)]="quotation.deliveryContactPersonPhone"
                                    [required]="!quotation.isQuickQuote" [onClickValidation]="onClickValidation"
                                    fieldName="deliveryPhoneNumber" nameCode="deliveryCountryCode"
                                    [labelName]="'Quotation.deliveryContactPersonPhone' | translate"></app-dial-code-input>
                            </div>
                        </div>

                        <!-- Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="quotation.deliveryAddressDetail"
                            [onClickValidation]="onClickValidation"
                            [addressLabel]="'Quotation.deliveryAddress' | translate"></app-custom-address>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>