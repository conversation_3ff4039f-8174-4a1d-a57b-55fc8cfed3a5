// Angular core imports
import { NgClass } from '@angular/common';
import { Component } from '@angular/core';

// Third-party library imports
import { NgbToastModule } from '@ng-bootstrap/ng-bootstrap';

// Project-specific imports
import { Toast, ToastService } from '../../services/toast.service';

@Component({
    selector: 'app-toast-container',
    standalone: true,
    imports: [NgbToastModule, NgClass],
    templateUrl: './toast-container.component.html',
    styleUrl: './toast-container.component.scss',
})
export class ToastContainerComponent {
    toasts: Toast[] = [];

    constructor(private toastService: ToastService) {}

    ngOnInit(): void {
        this.toastService.toasts$.subscribe((toasts) => {
            this.toasts = toasts;
        });
    }

    removeToast(toast: Toast) {
        this.toastService.remove(toast);
    }

    trackByFn(index: number, toast: Toast): string {
        return toast.message;
    }
}
