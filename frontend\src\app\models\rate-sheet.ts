import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { Address } from './common/address';

export class RateSheet extends BaseModel {
    tenantId!: number;
    slug!: string;
    name!: string;
    driverName!: string;
    startDate!: string | null;
    endDate!: string | null;
    pickupAddressDetail!: Address;
    deliveryAddressDetail!: Address;
    oversizeRate!: number;
    rushRequestRate!: number;
    enclosedRate!: number;
    fragileRate!: number;
    perishableRate!: number;
    dangerousGoodsRate!: number;

    constructor(data?: Partial<RateSheet>) {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.pickupAddressDetail = new Address();
        this.deliveryAddressDetail = new Address();

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): RateSheet {
        return new RateSheet(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        if (this.isNullOrUndefinedAndEmpty(this.name)) {
            form.controls.name.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
        this.name = this.trimMe(this.name);
        return this;
    }
}
