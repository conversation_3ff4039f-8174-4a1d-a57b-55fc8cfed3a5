// Angular core modules
import { CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Utility classes and base components
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { Constant } from '../../../../config/constants';
import { CommonUtil } from '../../../../shared/common.util';

// Models
import { RestResponse } from '../../../../models/common/auth.model';
import { RateSheet } from '../../../../models/rate-sheet';

// Custom services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

//Manager
import { RateSheetManager } from '../rate-sheet.manager';

// Custom components
import { TabMenuComponent } from '../../../../shared/tab-menu/tab-menu/tab-menu.component';
import { RateSheetBasicComponent } from './ratesheet-basic/ratesheet-basic.component';
import { RateSheetSpecialRequestComponent } from './ratesheet-special-request/ratesheet-special-request.component';
import { RateSheetWeightChargesComponent } from './ratesheet-weight-charges/ratesheet-weight-charges.component';

@Component({
    selector: 'app-rate-sheet-edit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TranslateModule,
        NgSelectModule,
        TabMenuComponent,
        NgSelectModule,
        RateSheetBasicComponent,
        RateSheetWeightChargesComponent,
        RateSheetSpecialRequestComponent,
    ],
    templateUrl: './ratesheet-edit.component.html',
    styleUrls: ['./ratesheet-edit.component.scss'],
})
export class RateSheetEditComponent extends BaseEditComponent implements OnInit {
    rateSheet!: RateSheet;

    selectedStep = 1;
    customerStatus = Constant.CUSTOMER_STATUS;
    stepperList = this.rateSheetManager.stepperList;

    constructor(
        public authService: AuthService,
        public commonUtil: CommonUtil,
        protected override route: ActivatedRoute,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
        protected location: Location,
        protected rateSheetManager: RateSheetManager,
    ) {
        super(rateSheetManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.initializeRateSheet();
        this.selectedStep = history.state.step <= this.stepperList.length ? history.state.step : 1;
        if (history.state.firstLoad) {
            this.selectedStep = 1;
        }

        this.onStepSelection();
    }

    override onFetchCompleted() {
        this.rateSheet = RateSheet.fromResponse(this.record);
        this.setRecord(this.rateSheet);
    }

    override onSaveSuccess(data: any): void {
        this.router.navigate(['/dashboard/rate-sheets']);
    }

    onSelectionCallBack(newSelection: number): void {
        if (newSelection === 1) {
            this.init();
        }
        if (!this.rateSheet.id) {
            this.onClickValidation = true;
        } else {
            this.selectedStep = newSelection;
            this.onStepSelection();
        }
    }

    onStepSelection(): void {
        this.stepperList.forEach((step: any) => {
            step.selected = step.stepNumber === this.selectedStep;
        });
    }

    tabMenuUpdate(step: number, isFormType: boolean): void {
        if (step > this.selectedStep) {
            if (isFormType) {
                this.saveRateSheetStep(step);
            } else {
                this.selectedStep = step;
                if (step === 1) {
                    this.init();
                }
                this.onStepSelection();
            }
        } else {
            this.onSelectionCallBack(step);
        }
    }

    async saveRateSheetStep(step?: number): Promise<void> {
        const forRequestData = JSON.parse(JSON.stringify(this.rateSheet));

        this.loadingService.show();

        const method = this.request.isNewRecord ? 'save' : 'update';
        this.rateSheetManager[method](forRequestData).subscribe({
            next: (response: RestResponse) => {
                if (step) {
                    this.selectedStep = step;

                    this.rateSheet.id = response.data;
                    this.setRecord(this.rateSheet);

                    this.request = {
                        isNewRecord: !this.rateSheet.id,
                        recordId: this.rateSheet.id || 0,
                    };
                    this.updateUrlWithNewId(this.rateSheet.id, step);
                    this.loadingService.hide();
                    this.onStepSelection();
                } else {
                    this.loadingService.hide();
                    this.toastService.success(response.message);
                    this.onSaveSuccess(response.data);
                }
            },
            error: (error: { message: string }) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    private updateUrlWithNewId(id: string, step: number) {
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
            this.router.navigate(['/dashboard/rate-sheet/edit', id], {
                state: { step: step, firstLoad: false },
            });
        });
    }

    private initializeRateSheet(): void {
        this.rateSheet = new RateSheet();
        this.setRecord(this.rateSheet);

        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;
        this.init();
    }
}
