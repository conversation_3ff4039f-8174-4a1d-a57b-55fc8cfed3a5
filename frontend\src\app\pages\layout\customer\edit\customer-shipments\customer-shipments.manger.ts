import { Injectable } from '@angular/core';
import { BaseManager } from '../../../../../config/base.manager';
import { LoadingService } from '../../../../../services/loading.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { CustomerShipmentsService } from './customer-shipments.service';

@Injectable({
    providedIn: 'root',
})
export class CustomerShipmentsManager extends BaseManager {
    constructor(
        protected customerShipmentsService: CustomerShipmentsService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(customerShipmentsService, loadingService, toastService);
    }
}
