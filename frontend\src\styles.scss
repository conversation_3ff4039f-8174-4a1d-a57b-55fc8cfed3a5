/* You can add global styles to this file, and also import other style files */
@import './variables.scss';

@import 'bootstrap-icons/font/bootstrap-icons.css';
@import 'assets/scss/common';
@import 'assets/scss/login';
@import 'assets/scss/mixins.scss';
@import 'assets/scss/style-theme.scss';
@import 'assets/scss/table-style.scss';
@import 'assets/scss/buttons.scss';
@import 'assets/scss/forms-style.scss';
@import 'assets/scss/responsive.scss';

@font-face {
    font-family: 'Montserrat';
    src: url('/assets/fonts/montserrat/Montserrat-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Montserrat';
    src: url('/assets/fonts/montserrat/Montserrat-Medium.woff2') format('woff2');
    font-weight: $font-weight-500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Montserrat';
    src: url('/assets/fonts/montserrat/Montserrat-SemiBold.woff2') format('woff2');
    font-weight: $font-weight-600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Montserrat';
    src: url('/assets/fonts/montserrat/Montserrat-Bold.woff2') format('woff2');
    font-weight: $font-weight-700;
    font-style: normal;
    font-display: swap;
}

input:-internal-autofill-selected,
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0px 1000px $white-color inset !important;
    box-shadow: 0 0 0px 1000px $white-color inset !important;
    background-color: $white-color !important;
    -webkit-text-fill-color: $black-color !important;
    color: $black-color !important;
    caret-color: $black-color !important;
    /* Ensures cursor visibility */
    transition: background-color 5000s ease-in-out 0s !important;
}

// start of section manage to change theme color
$theme-map: (
    'us': 'US',
    'canada': 'CANADA',
    // Add more countries here as needed
);

@each $key, $value in $theme-map {
    [data-theme='#{$value}'] {
        @include apply-theme($key);
    }
}

// end of section manage to change theme color

body {
    font-family: 'Montserrat', sans-serif;
    min-height: 100%;
}

p {
    font-family: 'Montserrat', sans-serif;
    font-weight: 300;
    color: #222222;
    word-break: break-all;
}

table.dataTable {
    max-width: 100% !important;
    margin: unset !important;
    width: 100% !important;
}

.error-message {
    width: 100%;
    margin-top: 4px;
    font-size: 0.875em;
    text-align: left;
    color: $form-border-color;
}

.is-invalid {
    &.form-control {
        border-width: 2px !important;
        border-color: $form-border-color !important;

        &:focus {
            box-shadow: none;
        }
    }

    &.form-select {
        border-width: 2px !important;
        border-color: $form-border-color !important;
    }

    &:not(.ng-select-opened) {
        .ng-select-container {
            border-color: $form-border-color !important;
            border-width: 2px !important;
            -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
        }
    }
}

.has-error-textarea {
    border-width: 2px !important;
    border-color: $form-border-color !important;
}

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    position: fixed;
    z-index: 999999999;
    top: 0;
    @include d-flex-align-center;
    justify-content: center;
}

/* Loader css */
.loader {
    width: fit-content;
    height: fit-content;
    @include flex-center;
}

.truckWrapper {
    width: 200px;
    height: 100px;
    display: flex;
    align-items: center;
    flex-direction: column;
    position: relative;
    justify-content: flex-end;
    overflow-x: hidden;
}

/* truck upper body */
.truckBody {
    width: 130px;
    height: fit-content;
    margin-bottom: 6px;
    animation: motion 1s linear infinite;
}

/* truck suspension animation*/
@keyframes motion {
    0% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(3px);
    }

    100% {
        transform: translateY(0px);
    }
}

/* truck's tires */
.truckTires {
    width: 130px;
    height: fit-content;
    @include flex-space-between;

    padding: 0px 10px 0px 15px;
    position: absolute;
    bottom: 0;
}

.truckTires svg {
    width: 24px;
}

.road {
    width: 100%;
    height: 1.5px;
    background-color: #282828;
    position: relative;
    bottom: 0;
    align-self: flex-end;
    border-radius: 3px;
}

.road::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 100%;
    background-color: #282828;
    right: -50%;
    border-radius: 3px;
    animation: roadAnimation 1.4s linear infinite;
    border-left: 10px solid white;
}

.road::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 100%;
    background-color: #282828;
    right: -65%;
    border-radius: 3px;
    animation: roadAnimation 1.4s linear infinite;
    border-left: 4px solid white;
}

.lampPost {
    position: absolute;
    bottom: 0;
    right: -90%;
    height: 90px;
    animation: roadAnimation 1.4s linear infinite;
}

@keyframes roadAnimation {
    0% {
        transform: translateX(0px);
    }

    100% {
        transform: translateX(-350px);
    }
}

// change anchor tag color
a {
    cursor: $cursor-pointer !important;
    text-decoration: none !important;
    color: $black-color;

    &:hover {
        color: inherit;
        text-decoration: none;
        transition: all 0.3s;
        cursor: $cursor-pointer !important;
    }

    &:focus {
        color: inherit;
        text-decoration: none;
        transition: all 0.3s;
        cursor: $cursor-pointer !important;
    }
}

//for scrollbar width and color
::-webkit-scrollbar-track {
    -webkit-box-shadow: $scroll-bar-box-shadow;
    box-shadow: $scroll-bar-box-shadow;
    border-radius: 10px;
    background-color: $white-smoke-color;
}

::-webkit-scrollbar {
    width: 5px;
    background-color: $white-smoke-color;
    height: 5px;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: $scroll-bar-box-shadow;
    box-shadow: $scroll-bar-box-shadow;
    background-color: #d3d3d3;
}

// for remove background color and label in case of autofill form
input:-internal-autofill-selected,
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    background-image: none !important;
    background-color: transparent !important;
    color: inherit !important;
    transition: background-color 5000s ease-in-out 0s !important;
}

/* Hide the eye icon in Microsoft Edge */
input[type='password']::-ms-reveal {
    display: none;
}

/* Prevent default appearance in Firefox (just in case, but usually not needed) */
input[type='password'] {
    -moz-appearance: none;
    appearance: none;
    /* Disable default styling for Mozilla */
}

.terms-privacy-page {
    p {
        font-size: 12px;
        word-break: keep-all;
        margin-bottom: 18px;
        font-weight: $font-weight-500;
    }

    .terms-listing {
        margin-bottom: 1rem;

        li {
            font-size: 12px;
            margin-bottom: 4px;
        }
    }

    .address {
        font-size: $font-size-16;
        font-weight: $font-weight-600;
    }

    @media (min-width: 576px) {
        p {
            font-size: $font-size-14;
            font-weight: $font-weight-500;
        }

        .terms-listing {
            li {
                font-size: $font-size-14 !important;
            }
        }
    }
}