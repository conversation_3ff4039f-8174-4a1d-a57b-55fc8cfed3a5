// Angular core imports
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

// Third-party library imports
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

// services import
import { LocalStorageService } from './services/local-storage.service';

@Injectable()
export class HttpAuthInterceptor implements HttpInterceptor {
    constructor(
        private localStorageService: LocalStorageService,
        private router: Router,
    ) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        const rawToken = this.localStorageService.get('token');
        let tokenObj: any = null;

        if (rawToken) {
            try {
                tokenObj = JSON.parse(rawToken);
            } catch (error) {
                console.warn('Invalid admin token JSON detected. Removing it from localStorage.', error);
                this.localStorageService.remove('token');
            }
        }

        if (tokenObj?.accessToken && tokenObj.expires_at > Date.now()) {
            req = req.clone({
                setHeaders: {
                    Authorization: `Bearer ${tokenObj.accessToken}`
                }
            });
        }

        return next.handle(req).pipe(
            catchError((error: HttpErrorResponse) => {
                if (error.status === 401 || error.status === 403) {
                    this.localStorageService.remove('token');
                    this.localStorageService.remove('user');
                    this.router.navigate(['/login']);
                }

                return throwError(() => error);
            })
        );
    }
}