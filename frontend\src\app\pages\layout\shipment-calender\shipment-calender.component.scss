@import '../../../../variables.scss';
@import '../../../../assets/scss/mixins.scss';

/* Calendar Container */
::ng-deep .fc {
    font-family: 'Segoe UI', sans-serif;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
}

/* Header Title */
::ng-deep .fc-toolbar-title {
    font-size: 1.5rem;
    font-weight: $font-weight-600;
    color: #333;
    letter-spacing: 0.5px;
}

/* Navigation Buttons */
::ng-deep .fc-button {
    background-color: #f0f2f5;
    border: none;
    color: #333;
    font-weight: bold;
    padding: 6px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

::ng-deep .fc-button:hover {
    background-color: #e1e5eb;
    transform: scale(1.05);
}

/* Grid Styling */
::ng-deep .fc-daygrid-day-frame {
    padding: 6px;
    transition: background 0.3s ease;
}

::ng-deep .fc-daygrid-day:hover {
    background-color: #f7faff;
}

/* Day Numbers */
::ng-deep .fc-daygrid-day-number {
    font-weight: $font-weight-500;
    font-size: $font-size-14;
    color: #555;
}

/* Event Tags */
::ng-deep .fc-event {
    border: none;
    border-radius: 20px;
    font-size: 12px;
    font-weight: $font-weight-500;
    padding: 2px 8px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Event Hover */
::ng-deep .fc-event:hover {
    opacity: 0.9;
    transform: scale(1.02);
}

/* More Link */
::ng-deep .fc-daygrid-more-link {
    color: #3f51b5;
    font-size: 12px;
    font-weight: $font-weight-600;
    text-decoration: underline;
    cursor: pointer;
}

/* Responsive Touch Support */
@media (max-width: 768px) {
    ::ng-deep .fc-daygrid-day-frame {
        padding: 4px;
    }

    ::ng-deep .fc-toolbar-title {
        font-size: 1.125rem !important;
        font-weight: $font-weight-500;
    }
}

/* Optional: Smooth scrollbars for WebKit */
::ng-deep .fc-popover::-webkit-scrollbar {
    width: 6px;
}

::ng-deep .fc-popover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 3px;
}

::ng-deep .fc-popover {
    max-height: 250px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 18px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border: none;
    display: flex;
    flex-direction: column;

    /* Smooth scroll */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Make the popover header sticky */
::ng-deep .fc-popover .fc-popover-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    padding: 8px 10px;
    border-bottom: 1px solid #ddd;
    font-weight: $font-weight-600;
}

/* Scrollable content below */
::ng-deep .fc-popover .fc-popover-body {
    overflow-y: auto;
    max-height: 200px; // Adjust to fit below header
    padding: 8px;
}

/* Optional scrollbar styling */
::ng-deep .fc-popover-body::-webkit-scrollbar {
    width: 6px;
}

::ng-deep .fc-popover-body::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.modal-body {
    .bg-white {
        border-radius: 6px;

        .d-flex {
            font-size: $font-size-14;

            .text-muted {
                font-size: $font-size-13;
                font-weight: $font-weight-500;
                color: #6c757d;
            }

            .fw-bold {
                font-size: $font-size-14;
            }
        }
    }
}

::ng-deep .modal {
    z-index: 1055 !important; // Bootstrap modal default is 1050
}

::ng-deep .fc-popover {
    z-index: 1040 !important;
}