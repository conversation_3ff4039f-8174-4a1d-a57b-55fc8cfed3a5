// Angular core and common modules
import { CommonModule, NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Quotation } from '../../../../../models/quotation/quotation';
import { RateSheet } from '../../../../../models/rate-sheet';
import { Shipment } from '../../../../../models/shipment/shipment';

// Services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

// Shared and custom components/directives
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';
import { CurrencyFormatterDirective } from '../../../../../shared/directives/custom-currency.directive';

// Managers
import { QuotationManager } from '../../quotation.manager';

@Component({
    selector: 'app-quotation-special-request',
    standalone: true,
    imports: [
        CommonModule,
        CurrencyFormatterDirective,
        FormsModule,
        NgClass,
        NgSelectModule,
        TranslateModule,
        RouterModule,
        ValidationMessageComponent,
    ],
    templateUrl: './quotation-special-request.component.html',
    styleUrl: './quotation-special-request.component.scss',
})
export class QuotationSpecialRequestComponent {
    @Input() shipment!: Shipment;
    @Input() quotation!: Quotation;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() rateSheetId!: string | null;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    specialRequestCharges!: RateSheet;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected cdRef: ChangeDetectorRef,
        protected quotationManager: QuotationManager,
    ) { }

    ngOnInit() {
        this.specialRequestCharges = new RateSheet();
        this.fetchSpecialPrice();
        this.cdRef.detectChanges();
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.onNextClick.emit(5);
    }

    save(form: any) {
        const isFormValid = form.valid;
        if (!isFormValid) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }
        this.saveButtonClicked.emit();
    }

    onBack() {
        this.onNextOrBackClick.emit(3);
    }

    onRadioButtonChange(
        data: any,
        field:
            | 'oversizeRate'
            | 'rushRequestRate'
            | 'enclosedRate'
            | 'fragileRate'
            | 'perishableRate'
            | 'dangerousGoodsRate',
    ) {
        if (!data) {
            this.quotation[field] = null;
        } else {
            this.quotation[field] = this.specialRequestCharges[field];
        }
    }

    fetchSpecialPrice() {
        this.filterParam.filtering.rateSheetId = this.rateSheetId ?? null;

        this.quotationManager.fetchSpecialPrice(this.filterParam).subscribe({
            next: (specialRequestCharges: any) => {
                if (specialRequestCharges) {
                    this.specialRequestCharges = specialRequestCharges;
                }
            },
        });
    }

    updateSwitchLabel(): void {
        const label = document.getElementById('switchStatus');
        const toggle = document.getElementById('toggleSwitch') as HTMLInputElement | null;

        if (label && toggle) {
            label.textContent = toggle.checked ? 'Yes' : 'No';
        }
    }
}
