import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../../../../config/base.service';
import { FilterParam } from '../../../../../models/common/filter-param';

// services

@Injectable({
    providedIn: 'root',
})
export class QuotationCargoService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/quotationitem', '/api/quotationItems');
    }

    getFreightAmount(filterParam: FilterParam) {
        return this.getRecords('/api/quotationitem/ratesheet/weight/charges', filterParam);
    }
}
