// Angular core imports
import { Injectable } from '@angular/core';

// Project-specific services
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../services/toast.service';

// Project-specific managers
import { BaseManager } from '../../../config/base.manager';
import { FileUploaderService } from './file-uploader.service';

@Injectable({
    providedIn: 'root',
})
export class FileUploaderManager extends BaseManager {
    constructor(
        private fileUploaderService: FileUploaderService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(fileUploaderService, loadingService, toastService);
    }
}
