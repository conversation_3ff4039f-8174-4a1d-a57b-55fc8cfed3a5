import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';
import { Address } from '../common/address';
import { Attachment } from '../common/attachment';

export class Employees extends BaseModel {
    phoneNumber!: string;
    driverNo!: string | null;
    email!: string;
    fullName!: string;
    firstName!: string;
    lastName!: string;
    addressDetail!: Address;
    hireDate!: string | null;
    countryCode: string = '+1-CA';
    emergencyContactCountryCode: string = '+1-CA';
    emergencyContactName!: string;
    emergencyContactNumber!: string;
    employmentStatus!: string;
    roleName!: string;
    documents!: Attachment[];

    constructor(data?: Partial<Employees>) {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.addressDetail = new Address();
        this.documents = new Array<Attachment>();
        this.addressDetail.type = 'HOME_ADDRESS';
        if (data) {
            Object.assign(this, data);
        }
    }

    forRequest() {
        this.phoneNumber = this.phoneNumber?.trim();
        this.email = this.email?.trim();
        this.fullName = this.fullName?.trim();
        this.firstName = this.firstName?.trim();
        this.lastName = this.lastName?.trim();
        return this;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    // Ensure this method is part of your Users class
    static fromResponse(data: any): Employees {
        return new Employees(data);
    }
}
