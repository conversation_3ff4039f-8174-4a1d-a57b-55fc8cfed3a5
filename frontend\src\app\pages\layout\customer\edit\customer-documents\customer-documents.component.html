<div class="site-page-container mt-3">
    <div class="site-card">
        <form #customerForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="row custom-responsive-row">
                        <div class="col-md-12 mb-14 px-0 px-md-0">
                            <app-file-uploader [uploaderId]="'employeeUploaderId'" *ngIf="uploaderVisibility"
                                [documents]="customer.documents"></app-file-uploader>
                        </div>
                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button" (click)="onBack()">
                                {{ "COMMON.BACK" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(customerForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>

                    </div>
                </div>
            </div>
        </form>
    </div>
</div>