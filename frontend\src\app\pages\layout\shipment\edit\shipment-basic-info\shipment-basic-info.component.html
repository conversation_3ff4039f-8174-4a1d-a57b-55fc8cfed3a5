<div class="site-page-container mt-3">
    <div class="site-card">
        <form #shipmentForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <!-- start of section for the basic information -->
                        <ng-container title="Basic Information">
                            <div class="label-wrap-box">
                                <span>{{"COMMON.BasicDetails" | translate}}</span>
                            </div>

                            <!-- Reference ID -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="text" name="referenceId" #ReferenceId="ngModel"
                                        [(ngModel)]="shipment.refID" placeholder="Reference/Load ID" disabled>
                                    <label for="referenceId">{{"SHIPMENT.referenceID" | translate}}</label>
                                </div>
                            </div>

                            <!-- Shipment Type -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="shipmentTypeOptions"
                                        [(ngModel)]="shipment.shipmentType" #ShipmentType="ngModel" name="Shipment Type"
                                        required="required" [ngClass]="{
                                        'is-invalid': !ShipmentType.valid && onClickValidation && !disabled
                                      }" [clearable]="false" [disabled]="disabled">
                                    </ng-select>

                                    <label for="shipmentType" class="ng-select-label">{{"SHIPMENT.shipmentType" |
                                        translate}}</label>

                                    <app-validation-message [field]="ShipmentType"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>

                            <!-- Driver Name -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="fullName" bindValue="id" [items]="drivers"
                                        [(ngModel)]="shipment.driver" #Driver="ngModel" name="driver" [ngClass]="{
                                        'is-invalid': !Driver.valid && onClickValidation && !disabled 
                                      }" (change)="onDriveSelection($event)" [typeahead]="searchDriverSubject"
                                        [loading]="loadingDriverNgSelect" [clearable]="false" [disabled]="disabled">
                                    </ng-select>
                                    <label for="driver" class="ng-select-label">{{"BARCODE.driver" |
                                        translate}}</label>
                                    <app-validation-message [field]="Driver" [onClickValidation]="onClickValidation">
                                    </app-validation-message>
                                </div>
                            </div>

                            <!-- shipment Status -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="shipmentStatusOptions"
                                        [(ngModel)]="shipment.status" #ShipmentStatus="ngModel" name="shipmentStatus"
                                        [clearable]="false"
                                        [disabled]="!(shipment.status === 'DELIVERED' && userRole === 'ROLE_SUPER_ADMIN')">
                                    </ng-select>
                                    <app-validation-message [field]="ShipmentStatus"
                                        [onClickValidation]="onClickValidation">
                                    </app-validation-message>

                                    <label for="ShipmentStatus" class="ng-select-label">{{"SHIPMENT.shipmentStatus" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Payment Type -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="shipmentPaymentTypeOptions"
                                        [(ngModel)]="shipment.paymentType" #ShipmentPaymentType="ngModel"
                                        name="Payment Type" required="required" [ngClass]="{
                                        'is-invalid': !ShipmentPaymentType.valid && onClickValidation && !disabled
                                      }" [clearable]="false" [disabled]="disabled">
                                    </ng-select>

                                    <label for="ShipmentPaymentType" class="ng-select-label">{{"SHIPMENT.paymentType" |
                                        translate}}</label>

                                    <app-validation-message [field]="ShipmentPaymentType"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>

                            <!-- Payment Status -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="shipmentPaymentStatusOptions"
                                        required="required" [(ngModel)]="shipment.paymentStatus" [clearable]="false"
                                        #paymentStatus="ngModel" name="payment Status" placeholder="" [disabled]="true">
                                    </ng-select>

                                    <label for="paymentStatus" class="ng-select-label">{{"SHIPMENT.paymentStatus" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- summary -->
                            <div class="col-md-12 mb-14 px-0 p-md-0">
                                <div class="form-floating textarea-custom" [class.disabled-container]="disabled">
                                    <textarea class="form-control" placeholder="{{'shipmentSummary' | translate}}"
                                        rows="4" id="shipmentSummary" name="Description" #ShipmentSummary="ngModel"
                                        [(ngModel)]="shipment.summary" [disabled]="disabled"></textarea>
                                    <label for="description pb-2">{{"SHIPMENT.shipmentSummary"| translate}}</label>
                                </div>
                            </div>

                        </ng-container>
                        <!-- end of section for the basic information -->

                        <!-- start of section for the customer information -->
                        <ng-container title="Customer Information">
                            <div class="label-wrap-box mt-14">
                                <span>{{"COMMON.customerDetail" | translate}}</span>
                            </div>

                            <!-- Customer -->
                            <div class="col-md-6 px-0 pe-md-2 ps-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="fullName" bindValue="id" [items]="customers"
                                        [(ngModel)]="shipment.customer" #Customer="ngModel" name="customer"
                                        required="required" (change)="getCustomerDetails($event)" [ngClass]="{
                                        'is-invalid': !Customer.valid && onClickValidation && !disabled
                                      }" [clearable]="false" [typeahead]="searchCustomerSubject"
                                        [loading]="loadingCustomerNgSelect" [disabled]="disabled">
                                    </ng-select>
                                    <app-validation-message [field]="Customer" [onClickValidation]="onClickValidation">
                                    </app-validation-message>

                                    <label for="Customer" class="ng-select-label">{{"CUSTOMER.objName" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Email -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="email" name="contactEmail" #contactEmail="ngModel"
                                        [(ngModel)]="shipment.contactPersonEmail" placeholder="" disabled>
                                    <label for="contactEmail">{{"DASHBOARD.Contact" | translate}} {{"EMAIL" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Name -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="text" name="contactName"
                                        [(ngModel)]="shipment.contactPersonName" placeholder="" #contactName="ngModel"
                                        disabled>
                                    <label for="contactName">{{"DASHBOARD.Contact" | translate}} {{"USERS.Name" |
                                        translate}}</label>
                                </div>
                            </div>

                            <!-- Contact Phone -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14">
                                    <app-dial-code-input [(countryCode)]="shipment.contactPersonCountryCode"
                                        [(number)]="shipment.contactPersonPhone" [onClickValidation]="onClickValidation"
                                        fieldName="phoneNumber" nameCode="CountryCode" [disableNumber]="true"
                                        [labelName]="'CUSTOMER.PhoneNumber' | translate"></app-dial-code-input>
                                </div>
                            </div>
                        </ng-container>
                        <!-- end of section for the customer information -->

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="save(shipmentForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="onNext(shipmentForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" *ngIf="disabled" appRippleEffect
                                type="button" (click)="onNextOrBackClick.emit(2)">
                                <div class="site-button-inner">
                                    {{ "COMMON.NEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>