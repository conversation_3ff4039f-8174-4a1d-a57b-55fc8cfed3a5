@import '../../../../variables.scss';
@import '../../../../assets/scss/mixins.scss';

.export-dropdown {
    position: relative;

    .btn-export {
        background-color: #ffdb2d; // Your primary yellow
        color: $black-color;
        font-weight: $font-weight-500;
        border: none;
        padding: 8px 20px;
        border-radius: 12px;
        outline: none;
        box-shadow: none;

        &:focus {
            outline: none;
            box-shadow: none !important; // remove blue glow
        }
    }

    .dropdown-menu {
        width: 100%; // match button width
        border-radius: 12px;
        padding: 8px 0;
        border: none;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
        min-width: unset;

        .dropdown-item {
            display: flex;
            align-items: center;
            font-size: $font-size-14;
            padding: 8px 16px;
            color: #212529;
            gap: 8px;

            i {
                font-size: $font-size-16;
            }

            &:hover {
                background-color: var(--primary-color); // your primary hover shade
                color: $black-color;
            }

            &:focus {
                background-color: var(--primary-color);
                outline: none;
                box-shadow: none;
            }
        }
    }
}

button:focus:not(:focus-visible) {
    outline: none;
}

.dropdown-toggle::after {
    display: none !important;
}

.export-button {
    @include button-style(48px, 18px, 48px);
    max-width: 48px;
}

@media (min-width: 576px) {
    .export-button {
        @include button-style(48px, 18px, 64px);
        max-width: 64px;
    }
}