// Angular core
import { Component, EventEmitter, Input, Output } from '@angular/core';

// Angular common and forms
import { CommonModule, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Angular router
import { ActivatedRoute, Router } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// RxJS

// App constants and models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Shipment } from '../../../../../models/shipment/shipment';

// Shared services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';

// Shared components
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Feature managers
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { AllowNumberOnlyDirective } from '../../../../../shared/directives/allow-number-only.directive';
import { CurrencyFormatterDirective } from '../../../../../shared/directives/custom-currency.directive';
import { CustomerManager } from '../../../customer/customer.manager';
import { EmployeesManager } from '../../../employees/employees.manager';
import { RateSheetManager } from '../../../ratesheet/rate-sheet.manager';
import { VehicleManager } from '../../../vehicle/vehicle.manager';
import { ShipmentManager } from '../../shipment.manager';

@Component({
    selector: 'app-shipment-customer-calculations',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        TranslateModule,
        NgClass,
        NgSelectModule,
        ValidationMessageComponent,
        AllowNumberOnlyDirective,
        NgxMaskDirective,
        CurrencyFormatterDirective,
    ],
    templateUrl: './shipment-customer-calculations.component.html',
    styleUrl: './shipment-customer-calculations.component.scss',
    providers: [provideNgxMask()],
})
export class ShipmentCustomerCalculationsComponent {
    @Input() shipment!: Shipment;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();
    @Output() onfetchDropdownDataCompleted = new EventEmitter<boolean>();

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected router: Router,
        protected customerManager: CustomerManager,
        protected shipmentManager: ShipmentManager,
        protected vehicleManager: VehicleManager,
        protected employeesManager: EmployeesManager,
        protected rateSheetManager: RateSheetManager,
        protected typeAheadService: TypeAheadService,
        protected modalService: NgbModal,
    ) { }

    handleCancelClick() {
        this.router.navigate(['/dashboard/shipments']);
    }

    onNext(form: any) {
        const isFormValid = form.valid;
        const areAllControlsDisabled = Object.values(form.controls).every((c: any) => c.disabled);

        if (!isFormValid && !areAllControlsDisabled) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.onNextOrBackClick.emit(7);
    }

    save(form: any) {
        const isFormValid = form.valid;
        const areAllControlsDisabled = Object.values(form.controls).every((c: any) => c.disabled);

        if (!isFormValid && !areAllControlsDisabled) {
            this.commonService.focusInvalidField();
            this.onClickValidation = true;
            return;
        }

        this.router.navigate(['/dashboard/shipments']);
    }

    onBack() {
        this.onNextOrBackClick.emit(5);
    }
}
