<div class="file-uploader content" [class.cursor-not-allowed]="disabled" *ngIf="!viewOnly">
  <div class="file-uploader-header display-align-center w-100" ng2FileDrop
    [ngClass]="{ 'nv-file-over': hasBaseDropZoneOver, 'has-image': hasFiles() }" (fileOver)="fileOverBase($event)"
    [uploader]="DocumentsUploader">

    @if(showUploaderIcon) {
    <input class="d-none" [id]="uploaderId" type="file" ng2FileSelect [uploader]="DocumentsUploader" multiple
      (change)="fileChange($event)" [accept]="fileAcceptType" [disabled]="disabled" />
    }

    <div class="w-100">
      <div class="file-upload-area d-flex flex-column align-items-center" (click)="triggerFileInput(uploaderId)">
        <img src="/assets/images/icons/Upload-file-icon.svg" alt="" width="50" class="uploader-svg-color"
          loading="eager" />
        <p class="h5 mt-2 mb-0">{{ uploaderTitle ? uploaderTitle : 'Upload Documents' }}</p>
        <p class="font-weight-500">{{ "COMMON.AllowedFileTypes" | translate:{ types: fileAcceptType } }}</p>
        <span class="font-weight-500 file-size-color"> File size should not exceed 5MB.</span>
      </div>
    </div>
  </div>
</div>

@if(hasFiles()) {
<div class="file-preview-container" [class.cursor-not-allowed]="disabled">
  @for(file of documents; track file) {
  @if(!file.isDeleted) {
  <div class="file-item col-12 col-md-6 col-xl-3">
    <div class="file-item-content">
      <div class="file-icon-container" placement="auto" ngbTooltip="Click to View" (click)="downloadFile(file)">
        @if(getDocIcon(file)) {
        <i [class]="getDocIcon(file)"></i>
        } @else() {
        <img [src]="getImagePath(file)" loading="eager" height="40" width="40" alt="file" />
        }
      </div>

      <div class="file-details">
        <div class="file-name" placement="auto" ngbTooltip="{{ file.originalName }}">
          {{ file.originalName }}
        </div>
        <div class="file-size">
          {{ formatFileSize(file.size) }}
        </div>
      </div>

      <span (click)="customRemoveFile(file,$event)" class="file-remove-btn" [class.pe-none]="disabled || viewOnly">
        <i class="bi bi-trash3 text-danger"></i>
      </span>
    </div>
  </div>
  }
  }
</div>
}