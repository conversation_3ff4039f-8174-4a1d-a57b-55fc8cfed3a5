<div [ngClass]="this.customClass ?? 'margin-bottom-12'">
    <div class="form-floating input-group custom-range-picker-radius" [ngClass]="this.customZIndex"
        [class.cursor-not-allowed]="disabled">
        <!-- Date input with ngbDatepicker -->
        <input class="form-control pe-none" name="date" placeholder="{{ labelName | translate }}"
            [(ngModel)]="selectedDate" #Date="ngModel" [disabled]="readOnly" [required]="isRequired"
            datepickerClass="date-picker-cls" (navigate)="datePickerOpen()" (closed)="datePickerClose()"
            [minDate]="minimumDate" [ngClass]="{
                'is-invalid': Date.invalid && isRequired && onClickValidation,
'cursor-not-allowed': readOnly
            }" ngbDatepicker #datePicker="ngbDatepicker" (dateSelect)="onDateSelect($event)"
            (click)="datePicker.toggle()" [footerTemplate]="footerTemplate" [disabled]="disabled"
            [class.cursor-not-allowed]="disabled" />

        <label for="date" [ngClass]="{'z-index-5': !!this.customZIndex}" class="z-index-4">
            {{labelName | translate}}
        </label>

        <!-- Calendar button -->
        <button class="btn btn-outline-secondary bi bi-calendar3 calender-button-cls display-align-center"
            [ngClass]="{'is-invalid': Date.invalid && isRequired && onClickValidation}" (click)="datePicker.toggle()"
            type="button" [disabled]="disabled">
        </button>
    </div>

    <ng-template #footerTemplate>
        <hr class="my-0" *ngIf="!clearFooterAction" />
        <button *ngIf="!clearFooterAction" class="btn clear-button-color btn-sm m-2 float-end"
            (click)="clearButton(datePicker)">Clear</button>
    </ng-template>

    <!-- Validation message -->
    <app-validation-message [field]="Date" [onClickValidation]="onClickValidation"
        *ngIf="isRequired"></app-validation-message>
</div>