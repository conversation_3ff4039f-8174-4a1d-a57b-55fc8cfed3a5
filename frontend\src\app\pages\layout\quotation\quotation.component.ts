// Angular core and common modules
import { CommonModule, NgClass } from '@angular/common';
import { Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Angular Router for navigation and route access
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-party UI modules
import { NgbAccordionModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';

// Base configuration and constants
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { Constant } from '../../../config/constants';

// Application-specific models
import { Quotation } from '../../../models/quotation/quotation';

// Shared services
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';

// Shared directives and components
import { NgCustomDateRangePickerComponent } from '../../../shared/common-component/ng-custom-date-range-picker/ng-custom-date-range-picker.component';
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';

// Shared pipes
import { RemoveUnderscorePipe } from '../../../shared/pipes/remove-underscore.pipe';

// Feature-specific components and managers
import { NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { Subject } from 'rxjs';
import { FilterParam } from '../../../models/common/filter-param';
import { TypeAheadFilterService, TypeaheadSearchConfig } from '../../../shared/services/typeahead.service';
import { CustomerManager } from '../customer/customer.manager';
import { ShipmentManager } from '../shipment/shipment.manager';
import { QuotationManager } from './quotation.manager';

@Component({
    selector: 'app-quotation',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        DateFormatPipe,
        DelayedInputDirective,
        FormsModule,
        NgClass,
        NgCustomDateRangePickerComponent,
        NgSelectModule,
        NgxMaskPipe,
        NgbAccordionModule,
        NgbTooltipModule,
        RemoveUnderscorePipe,
        RouterModule,
        StatusBadgeDirective,
        TooltipEllipsisDirective,
        TranslateModule,
    ],
    templateUrl: './quotation.component.html',
    styleUrls: ['./quotation.component.scss'],
    providers: [provideNgxMask()],
})
export class QuotationComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
    @ViewChild('refID') refID!: TemplateRef<string>;
    @ViewChild('customer') customer!: TemplateRef<string>;
    @ViewChild('contactPersonName') contactPersonName!: TemplateRef<string>;
    @ViewChild('contactPhone') contactPhone!: TemplateRef<string>;
    @ViewChild('pickupAddress') pickupAddress!: TemplateRef<string>;
    @ViewChild('deliveryAddress') deliveryAddress!: TemplateRef<string>;
    @ViewChild('status') status!: TemplateRef<string>;
    @ViewChild('createdOn') createdOn!: TemplateRef<string>;
    @ViewChild('action') action!: TemplateRef<any>;

    quotations!: Array<Quotation>;

    phoneMaskPattern = Constant.MASKS.PHONE_MASK;
    resourceType: string = Constant.RESOURCE_TYPE.QUOTATIONS;

    searchCustomerSubject: Subject<string> = new Subject<string>();
    searchPickupCitySubject: Subject<string> = new Subject<string>();
    searchDeliveryCitySubject: Subject<string> = new Subject<string>();

    loadingCustomerNgSelect!: boolean;
    loadingPickupCityNgSelect!: boolean;
    loadingDeliveryCityNgSelect!: boolean;

    customers: any[] = [];
    pickupCities: any[] = [];
    deliveryCities: any[] = [];


    isShowAccordion: boolean = false;
    durationFilters = Constant.DURATION_FILTERS;

    quotationStatusList = Constant.QUOTATION_STATUS;
    durationFilter = Constant.DURATION_FILTERS;

    private searchConfigs: TypeaheadSearchConfig<any>[] = [
        {
            subject: this.searchCustomerSubject,
            fetchFunction: this.customerManager.fetchForDropdownData.bind(this.customerManager),
            updateResults: (results: any[]) => (this.customers = results),
            updateLoading: (isLoading: boolean) => (this.loadingCustomerNgSelect = isLoading),
            selectedItemGetter: () =>
                this.customers.find(customer => customer.id === this.filterParam.filtering.customerId),
            endpoint: 'shipment/selection',
        },
        {
            subject: this.searchPickupCitySubject,
            fetchFunction: this.shipmentManager.fetchForPickupCityDropdown.bind(this.shipmentManager),
            updateResults: (results: any[]) => (this.pickupCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingPickupCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.pickupCities.find(city => city.id === this.filterParam.filtering.pickUpCity),
        },
        {
            subject: this.searchDeliveryCitySubject,
            fetchFunction: this.shipmentManager.fetchForPickupCityDropdown.bind(this.shipmentManager),
            updateResults: (results: any[]) => (this.deliveryCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingDeliveryCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.deliveryCities.find(city => city.id === this.filterParam.filtering.deliveryCity),
        },
    ];

    constructor(
        protected quotationManager: QuotationManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected customerManager: CustomerManager,
        protected shipmentManager: ShipmentManager,
        protected typeAheadService: TypeAheadFilterService,
        protected route: ActivatedRoute,
        protected override router: Router,
    ) {
        super(quotationManager, commonService, toastService, loadingService, router);
        this.setupAllSearches();
    }

    ngOnInit() {
        const urlHistoryData = history.state.data;
        if (urlHistoryData) {
            this.isShowAccordion = true;
            this.filterParam.filtering.status = urlHistoryData.toUpperCase();
        }
        if (history.state.Filtering) {
            this.filterParam.filtering.createdWithinDays = history.state.Filtering;
        }
        this.quotations = new Array<Quotation>();

        this.filterParam.fileName = this.resourceType;

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.QUOTATION;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.QUOTATION;

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            refID: this.refID,
            customer: this.customer,
            contactPersonName: this.contactPersonName,
            contactPhone: this.contactPhone,
            pickupAddress: this.pickupAddress,
            deliveryAddress: this.deliveryAddress,
            status: this.status,
            createdOn: this.createdOn,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override onFetchCompleted() {
        this.quotations = this.records.map((data) => Quotation.fromResponse(data));
        super.onFetchCompleted();
    }

    setupAllSearches() {
        const typeHeadFilter = new FilterParam();
        this.searchConfigs.forEach(config => {
            this.typeAheadService.setupSearchSubscription<any>(
                config.subject,
                typeHeadFilter,
                (param: FilterParam) => config.fetchFunction(param, config.endpoint),
                config.updateResults,
                config.updateLoading,
                config.selectedItemGetter,
                config.endpoint
            );
        });
    }
}
