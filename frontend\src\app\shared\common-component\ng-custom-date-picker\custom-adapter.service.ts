import { Injectable } from '@angular/core';
import { NgbDateAdapter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

@Injectable({
  providedIn: 'root'
})
export class CustomAdapterService extends NgbDateAdapter<string> {
  fromModel(value: string | null): NgbDateStruct | null {
    if (value) {
      const dateString = value.split('T')[0];
      const [year, month, day] = dateString.split('-').map(Number);
      return { day, month, year };
    }
    return null;
  }

  toModel(date: NgbDateStruct | null): string | null {
    if (!date) {
      return null;
    }

    const pad = (digit: number) => digit < 10 ? `0${digit}` : digit; // Helper function to pad single-digit numbers
    const year = date.year;
    const month = pad(date.month); // Ensure two digits for month
    const day = pad(date.day);     // Ensure two digits for day

    return `${year}-${month}-${day}`;
  }
}
