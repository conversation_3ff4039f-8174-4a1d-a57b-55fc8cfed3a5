import { Directive, HostListener, Input } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, Validator, ValidationErrors } from '@angular/forms';

@Directive({
  selector: '[appEmailValidator]',
  standalone: true,
  providers: [{ provide: NG_VALIDATORS, useExisting: EmailValidatorDirective, multi: true }]
})
export class EmailValidatorDirective implements Validator {
  @Input() allowEmpty: boolean = false;
  @Input() appNoWhitespaceValidator: boolean = true;

  @HostListener('input', ['$event'])
  onInput(event: any): void {
    const input = event.target as HTMLInputElement;
    let value = input.value;

    // Enforce lowercase conversion and text type
    value = value.toLowerCase().replace(/[^a-z0-9._%+-~!#$&'*\/=?^`{|}~-]+/g, '');

    // Reset to empty if not allowed and value is empty
    if (!this.allowEmpty && !value) {
      value = '';
    }

    // Update input value with sanitized value
    input.value = value;
  }

  validate(control: AbstractControl): ValidationErrors | null {
    // Skip validation if empty values are allowed
    if (this.allowEmpty && !control.value) {
      return null;
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const valid = emailRegex.test(control.value);
    return valid ? null : { invalidEmail: true };
  }
}
