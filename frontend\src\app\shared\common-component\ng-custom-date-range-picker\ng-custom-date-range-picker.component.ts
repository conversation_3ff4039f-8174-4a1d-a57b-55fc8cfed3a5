import { CommonModule } from '@angular/common';
import { Component, EventEmitter, HostListener, inject, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

// third party import
import {
    NgbCalendar,
    NgbDate,
    NgbDateAdapter,
    NgbDateParserFormatter,
    NgbDatepickerModule,
} from '@ng-bootstrap/ng-bootstrap';

// service import
import { CustomAdapterService } from './custom-adapter.service';
import { CustomDateParserFormatter } from './custom-date-parser-formatter.service';

// common component import
import { ValidationMessageComponent } from '../validation-message/validation-message.component';

@Component({
    selector: 'app-ng-custom-date-range-picker',
    standalone: true,
    imports: [ValidationMessageComponent, NgbDatepickerModule, FormsModule, CommonModule, TranslateModule],
    templateUrl: './ng-custom-date-range-picker.component.html',
    styleUrls: ['./ng-custom-date-range-picker.component.scss'],
    providers: [
        { provide: NgbDateAdapter, useClass: CustomAdapterService },
        { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
    ],
})
export class NgCustomDateRangePickerComponent {
    @Input() readOnly: boolean = false;
    @Input() labelName!: string;
    @Input() onClickValidation!: boolean;
    @Input() inputFromDate!: string | null;
    @Input() inputToDate!: string | null;
    @Input() customClass?: string;
    @Input() customZIndex?: string;
    @Input() isRequired: boolean = false;
    @Input() placement: string = 'bottom-start';

    @Output() setFromDate = new EventEmitter<string | null>(); // Output to notify parent of changes
    @Output() setToDate = new EventEmitter<string | null>(); // Output to notify parent of changes

    calendar = inject(NgbCalendar);
    formatter = inject(NgbDateParserFormatter);
    fromDate!: NgbDate | null;
    toDate!: NgbDate | null;
    hoveredDate: NgbDate | null = null;

    displayMonths: number = 2;
    screenWidth!: number;
    isMobile!: boolean;

    constructor(
        protected ngbDateParserFormatter: NgbDateParserFormatter,
        protected dateAdapter: NgbDateAdapter<string>,
    ) {
        this.updateScreenSize();
    }

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.updateScreenSize();
    }

    private updateScreenSize() {
        this.screenWidth = window.innerWidth;
        this.isMobile = this.screenWidth < 768; // Example breakpoint for mobile
        if (!this.isMobile) {
            this.displayMonths = 2;
        } else {
            this.displayMonths = 1;
        }
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['inputFromDate'] || changes['inputToDate']) {
            if (!this.inputFromDate && !this.inputToDate) {
                this.fromDate = null;
                this.toDate = null;
                this.hoveredDate = null;
            }
        }
    }

    // Clear the selected date
    clearButton(datepicker: any) {
        this.fromDate = null;
        this.toDate = null;
        this.inputFromDate = null;
        this.inputToDate = null;
        this.hoveredDate = null;
        this.setFromDate.emit(this.inputFromDate);
        this.setToDate.emit(this.inputToDate);

        datepicker.close();
    }

    get combinedDates(): string {
        if (!this.fromDate && !this.toDate) {
            return ''; // Return empty string if both dates are missing
        }

        const formattedFromDate = this.fromDate ? this.formatter.format(this.fromDate) : '';
        const formattedToDate = this.toDate ? this.formatter.format(this.toDate) : '';

        return formattedToDate ? `${formattedFromDate} - ${formattedToDate}` : formattedFromDate;
    }

    onDateSelection(date: NgbDate, datepicker: any) {
        if (!this.fromDate && !this.toDate) {
            this.fromDate = date;
        } else if (this.fromDate && !this.toDate && date?.after(this.fromDate)) {
            this.toDate = date;
            datepicker.close();
        } else if (this.fromDate && this.toDate) {
            // If both dates are selected, reset them and start the selection again
            this.fromDate = date;
            this.toDate = null;
        } else {
            this.toDate = date;
            this.fromDate = date;
            datepicker.close();
        }

        this.inputFromDate = this.dateAdapter.toModel(this.fromDate);
        this.inputToDate = this.dateAdapter.toModel(this.toDate);
        this.setFromDate.emit(this.inputFromDate);
        this.setToDate.emit(this.inputToDate);
    }

    isHovered(date: NgbDate) {
        return (
            this.fromDate &&
            !this.toDate &&
            this.hoveredDate &&
            date.after(this.fromDate) &&
            date.before(this.hoveredDate)
        );
    }

    isInside(date: NgbDate) {
        return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
    }

    isRange(date: NgbDate) {
        return (
            date.equals(this.fromDate) ||
            (this.toDate && date.equals(this.toDate)) ||
            this.isInside(date) ||
            this.isHovered(date)
        );
    }

    validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
        const parsed = this.formatter.parse(input);
        return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
    }
}
