{"version": 3, "sources": ["../../../../../node_modules/signature_pad/dist/signature_pad.js"], "sourcesContent": ["/*!\n * Signature Pad v5.0.7 | https://github.com/szimek/signature_pad\n * (c) 2025 <PERSON><PERSON><PERSON> | Released under the MIT license\n */\n\nclass Point {\n    constructor(x, y, pressure, time) {\n        if (isNaN(x) || isNaN(y)) {\n            throw new Error(`Point is invalid: (${x}, ${y})`);\n        }\n        this.x = +x;\n        this.y = +y;\n        this.pressure = pressure || 0;\n        this.time = time || Date.now();\n    }\n    distanceTo(start) {\n        return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n    }\n    equals(other) {\n        return (this.x === other.x &&\n            this.y === other.y &&\n            this.pressure === other.pressure &&\n            this.time === other.time);\n    }\n    velocityFrom(start) {\n        return this.time !== start.time\n            ? this.distanceTo(start) / (this.time - start.time)\n            : 0;\n    }\n}\n\nclass Bezier {\n    static fromPoints(points, widths) {\n        const c2 = this.calculateControlPoints(points[0], points[1], points[2]).c2;\n        const c3 = this.calculateControlPoints(points[1], points[2], points[3]).c1;\n        return new Bezier(points[1], c2, c3, points[2], widths.start, widths.end);\n    }\n    static calculateControlPoints(s1, s2, s3) {\n        const dx1 = s1.x - s2.x;\n        const dy1 = s1.y - s2.y;\n        const dx2 = s2.x - s3.x;\n        const dy2 = s2.y - s3.y;\n        const m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n        const m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n        const l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n        const l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n        const dxm = m1.x - m2.x;\n        const dym = m1.y - m2.y;\n        const k = l1 + l2 == 0 ? 0 : l2 / (l1 + l2);\n        const cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n        const tx = s2.x - cm.x;\n        const ty = s2.y - cm.y;\n        return {\n            c1: new Point(m1.x + tx, m1.y + ty),\n            c2: new Point(m2.x + tx, m2.y + ty),\n        };\n    }\n    constructor(startPoint, control2, control1, endPoint, startWidth, endWidth) {\n        this.startPoint = startPoint;\n        this.control2 = control2;\n        this.control1 = control1;\n        this.endPoint = endPoint;\n        this.startWidth = startWidth;\n        this.endWidth = endWidth;\n    }\n    length() {\n        const steps = 10;\n        let length = 0;\n        let px;\n        let py;\n        for (let i = 0; i <= steps; i += 1) {\n            const t = i / steps;\n            const cx = this.point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n            const cy = this.point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n            if (i > 0) {\n                const xdiff = cx - px;\n                const ydiff = cy - py;\n                length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n            }\n            px = cx;\n            py = cy;\n        }\n        return length;\n    }\n    point(t, start, c1, c2, end) {\n        return (start * (1.0 - t) * (1.0 - t) * (1.0 - t))\n            + (3.0 * c1 * (1.0 - t) * (1.0 - t) * t)\n            + (3.0 * c2 * (1.0 - t) * t * t)\n            + (end * t * t * t);\n    }\n}\n\nclass SignatureEventTarget {\n    constructor() {\n        try {\n            this._et = new EventTarget();\n        }\n        catch (_a) {\n            this._et = document;\n        }\n    }\n    addEventListener(type, listener, options) {\n        this._et.addEventListener(type, listener, options);\n    }\n    dispatchEvent(event) {\n        return this._et.dispatchEvent(event);\n    }\n    removeEventListener(type, callback, options) {\n        this._et.removeEventListener(type, callback, options);\n    }\n}\n\nfunction throttle(fn, wait = 250) {\n    let previous = 0;\n    let timeout = null;\n    let result;\n    let storedContext;\n    let storedArgs;\n    const later = () => {\n        previous = Date.now();\n        timeout = null;\n        result = fn.apply(storedContext, storedArgs);\n        if (!timeout) {\n            storedContext = null;\n            storedArgs = [];\n        }\n    };\n    return function wrapper(...args) {\n        const now = Date.now();\n        const remaining = wait - (now - previous);\n        storedContext = this;\n        storedArgs = args;\n        if (remaining <= 0 || remaining > wait) {\n            if (timeout) {\n                clearTimeout(timeout);\n                timeout = null;\n            }\n            previous = now;\n            result = fn.apply(storedContext, storedArgs);\n            if (!timeout) {\n                storedContext = null;\n                storedArgs = [];\n            }\n        }\n        else if (!timeout) {\n            timeout = window.setTimeout(later, remaining);\n        }\n        return result;\n    };\n}\n\nclass SignaturePad extends SignatureEventTarget {\n    constructor(canvas, options = {}) {\n        var _a, _b, _c;\n        super();\n        this.canvas = canvas;\n        this._drawingStroke = false;\n        this._isEmpty = true;\n        this._lastPoints = [];\n        this._data = [];\n        this._lastVelocity = 0;\n        this._lastWidth = 0;\n        this._handleMouseDown = (event) => {\n            if (!this._isLeftButtonPressed(event, true) || this._drawingStroke) {\n                return;\n            }\n            this._strokeBegin(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleMouseMove = (event) => {\n            if (!this._isLeftButtonPressed(event, true) || !this._drawingStroke) {\n                this._strokeEnd(this._pointerEventToSignatureEvent(event), false);\n                return;\n            }\n            this._strokeMoveUpdate(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleMouseUp = (event) => {\n            if (this._isLeftButtonPressed(event)) {\n                return;\n            }\n            this._strokeEnd(this._pointerEventToSignatureEvent(event));\n        };\n        this._handleTouchStart = (event) => {\n            if (event.targetTouches.length !== 1 || this._drawingStroke) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            this._strokeBegin(this._touchEventToSignatureEvent(event));\n        };\n        this._handleTouchMove = (event) => {\n            if (event.targetTouches.length !== 1) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            if (!this._drawingStroke) {\n                this._strokeEnd(this._touchEventToSignatureEvent(event), false);\n                return;\n            }\n            this._strokeMoveUpdate(this._touchEventToSignatureEvent(event));\n        };\n        this._handleTouchEnd = (event) => {\n            if (event.targetTouches.length !== 0) {\n                return;\n            }\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            this.canvas.removeEventListener('touchmove', this._handleTouchMove);\n            this._strokeEnd(this._touchEventToSignatureEvent(event));\n        };\n        this._handlePointerDown = (event) => {\n            if (!event.isPrimary || !this._isLeftButtonPressed(event) || this._drawingStroke) {\n                return;\n            }\n            event.preventDefault();\n            this._strokeBegin(this._pointerEventToSignatureEvent(event));\n        };\n        this._handlePointerMove = (event) => {\n            if (!event.isPrimary) {\n                return;\n            }\n            if (!this._isLeftButtonPressed(event, true) || !this._drawingStroke) {\n                this._strokeEnd(this._pointerEventToSignatureEvent(event), false);\n                return;\n            }\n            event.preventDefault();\n            this._strokeMoveUpdate(this._pointerEventToSignatureEvent(event));\n        };\n        this._handlePointerUp = (event) => {\n            if (!event.isPrimary || this._isLeftButtonPressed(event)) {\n                return;\n            }\n            event.preventDefault();\n            this._strokeEnd(this._pointerEventToSignatureEvent(event));\n        };\n        this.velocityFilterWeight = options.velocityFilterWeight || 0.7;\n        this.minWidth = options.minWidth || 0.5;\n        this.maxWidth = options.maxWidth || 2.5;\n        this.throttle = (_a = options.throttle) !== null && _a !== void 0 ? _a : 16;\n        this.minDistance = (_b = options.minDistance) !== null && _b !== void 0 ? _b : 5;\n        this.dotSize = options.dotSize || 0;\n        this.penColor = options.penColor || 'black';\n        this.backgroundColor = options.backgroundColor || 'rgba(0,0,0,0)';\n        this.compositeOperation = options.compositeOperation || 'source-over';\n        this.canvasContextOptions = (_c = options.canvasContextOptions) !== null && _c !== void 0 ? _c : {};\n        this._strokeMoveUpdate = this.throttle\n            ? throttle(SignaturePad.prototype._strokeUpdate, this.throttle)\n            : SignaturePad.prototype._strokeUpdate;\n        this._ctx = canvas.getContext('2d', this.canvasContextOptions);\n        this.clear();\n        this.on();\n    }\n    clear() {\n        const { _ctx: ctx, canvas } = this;\n        ctx.fillStyle = this.backgroundColor;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\n        this._data = [];\n        this._reset(this._getPointGroupOptions());\n        this._isEmpty = true;\n    }\n    fromDataURL(dataUrl, options = {}) {\n        return new Promise((resolve, reject) => {\n            const image = new Image();\n            const ratio = options.ratio || window.devicePixelRatio || 1;\n            const width = options.width || this.canvas.width / ratio;\n            const height = options.height || this.canvas.height / ratio;\n            const xOffset = options.xOffset || 0;\n            const yOffset = options.yOffset || 0;\n            this._reset(this._getPointGroupOptions());\n            image.onload = () => {\n                this._ctx.drawImage(image, xOffset, yOffset, width, height);\n                resolve();\n            };\n            image.onerror = (error) => {\n                reject(error);\n            };\n            image.crossOrigin = 'anonymous';\n            image.src = dataUrl;\n            this._isEmpty = false;\n        });\n    }\n    toDataURL(type = 'image/png', encoderOptions) {\n        switch (type) {\n            case 'image/svg+xml':\n                if (typeof encoderOptions !== 'object') {\n                    encoderOptions = undefined;\n                }\n                return `data:image/svg+xml;base64,${btoa(this.toSVG(encoderOptions))}`;\n            default:\n                if (typeof encoderOptions !== 'number') {\n                    encoderOptions = undefined;\n                }\n                return this.canvas.toDataURL(type, encoderOptions);\n        }\n    }\n    on() {\n        this.canvas.style.touchAction = 'none';\n        this.canvas.style.msTouchAction = 'none';\n        this.canvas.style.userSelect = 'none';\n        const isIOS = /Macintosh/.test(navigator.userAgent) && 'ontouchstart' in document;\n        if (window.PointerEvent && !isIOS) {\n            this._handlePointerEvents();\n        }\n        else {\n            this._handleMouseEvents();\n            if ('ontouchstart' in window) {\n                this._handleTouchEvents();\n            }\n        }\n    }\n    off() {\n        this.canvas.style.touchAction = 'auto';\n        this.canvas.style.msTouchAction = 'auto';\n        this.canvas.style.userSelect = 'auto';\n        this.canvas.removeEventListener('pointerdown', this._handlePointerDown);\n        this.canvas.removeEventListener('mousedown', this._handleMouseDown);\n        this.canvas.removeEventListener('touchstart', this._handleTouchStart);\n        this._removeMoveUpEventListeners();\n    }\n    _getListenerFunctions() {\n        var _a;\n        const canvasWindow = window.document === this.canvas.ownerDocument\n            ? window\n            : (_a = this.canvas.ownerDocument.defaultView) !== null && _a !== void 0 ? _a : this.canvas.ownerDocument;\n        return {\n            addEventListener: canvasWindow.addEventListener.bind(canvasWindow),\n            removeEventListener: canvasWindow.removeEventListener.bind(canvasWindow),\n        };\n    }\n    _removeMoveUpEventListeners() {\n        const { removeEventListener } = this._getListenerFunctions();\n        removeEventListener('pointermove', this._handlePointerMove);\n        removeEventListener('pointerup', this._handlePointerUp);\n        removeEventListener('mousemove', this._handleMouseMove);\n        removeEventListener('mouseup', this._handleMouseUp);\n        removeEventListener('touchmove', this._handleTouchMove);\n        removeEventListener('touchend', this._handleTouchEnd);\n    }\n    isEmpty() {\n        return this._isEmpty;\n    }\n    fromData(pointGroups, { clear = true } = {}) {\n        if (clear) {\n            this.clear();\n        }\n        this._fromData(pointGroups, this._drawCurve.bind(this), this._drawDot.bind(this));\n        this._data = this._data.concat(pointGroups);\n    }\n    toData() {\n        return this._data;\n    }\n    _isLeftButtonPressed(event, only) {\n        if (only) {\n            return event.buttons === 1;\n        }\n        return (event.buttons & 1) === 1;\n    }\n    _pointerEventToSignatureEvent(event) {\n        return {\n            event: event,\n            type: event.type,\n            x: event.clientX,\n            y: event.clientY,\n            pressure: 'pressure' in event ? event.pressure : 0,\n        };\n    }\n    _touchEventToSignatureEvent(event) {\n        const touch = event.changedTouches[0];\n        return {\n            event: event,\n            type: event.type,\n            x: touch.clientX,\n            y: touch.clientY,\n            pressure: touch.force,\n        };\n    }\n    _getPointGroupOptions(group) {\n        return {\n            penColor: group && 'penColor' in group ? group.penColor : this.penColor,\n            dotSize: group && 'dotSize' in group ? group.dotSize : this.dotSize,\n            minWidth: group && 'minWidth' in group ? group.minWidth : this.minWidth,\n            maxWidth: group && 'maxWidth' in group ? group.maxWidth : this.maxWidth,\n            velocityFilterWeight: group && 'velocityFilterWeight' in group\n                ? group.velocityFilterWeight\n                : this.velocityFilterWeight,\n            compositeOperation: group && 'compositeOperation' in group\n                ? group.compositeOperation\n                : this.compositeOperation,\n        };\n    }\n    _strokeBegin(event) {\n        const cancelled = !this.dispatchEvent(new CustomEvent('beginStroke', { detail: event, cancelable: true }));\n        if (cancelled) {\n            return;\n        }\n        const { addEventListener } = this._getListenerFunctions();\n        switch (event.event.type) {\n            case 'mousedown':\n                addEventListener('mousemove', this._handleMouseMove);\n                addEventListener('mouseup', this._handleMouseUp);\n                break;\n            case 'touchstart':\n                addEventListener('touchmove', this._handleTouchMove);\n                addEventListener('touchend', this._handleTouchEnd);\n                break;\n            case 'pointerdown':\n                addEventListener('pointermove', this._handlePointerMove);\n                addEventListener('pointerup', this._handlePointerUp);\n                break;\n        }\n        this._drawingStroke = true;\n        const pointGroupOptions = this._getPointGroupOptions();\n        const newPointGroup = Object.assign(Object.assign({}, pointGroupOptions), { points: [] });\n        this._data.push(newPointGroup);\n        this._reset(pointGroupOptions);\n        this._strokeUpdate(event);\n    }\n    _strokeUpdate(event) {\n        if (!this._drawingStroke) {\n            return;\n        }\n        if (this._data.length === 0) {\n            this._strokeBegin(event);\n            return;\n        }\n        this.dispatchEvent(new CustomEvent('beforeUpdateStroke', { detail: event }));\n        const point = this._createPoint(event.x, event.y, event.pressure);\n        const lastPointGroup = this._data[this._data.length - 1];\n        const lastPoints = lastPointGroup.points;\n        const lastPoint = lastPoints.length > 0 && lastPoints[lastPoints.length - 1];\n        const isLastPointTooClose = lastPoint\n            ? point.distanceTo(lastPoint) <= this.minDistance\n            : false;\n        const pointGroupOptions = this._getPointGroupOptions(lastPointGroup);\n        if (!lastPoint || !(lastPoint && isLastPointTooClose)) {\n            const curve = this._addPoint(point, pointGroupOptions);\n            if (!lastPoint) {\n                this._drawDot(point, pointGroupOptions);\n            }\n            else if (curve) {\n                this._drawCurve(curve, pointGroupOptions);\n            }\n            lastPoints.push({\n                time: point.time,\n                x: point.x,\n                y: point.y,\n                pressure: point.pressure,\n            });\n        }\n        this.dispatchEvent(new CustomEvent('afterUpdateStroke', { detail: event }));\n    }\n    _strokeEnd(event, shouldUpdate = true) {\n        this._removeMoveUpEventListeners();\n        if (!this._drawingStroke) {\n            return;\n        }\n        if (shouldUpdate) {\n            this._strokeUpdate(event);\n        }\n        this._drawingStroke = false;\n        this.dispatchEvent(new CustomEvent('endStroke', { detail: event }));\n    }\n    _handlePointerEvents() {\n        this._drawingStroke = false;\n        this.canvas.addEventListener('pointerdown', this._handlePointerDown);\n    }\n    _handleMouseEvents() {\n        this._drawingStroke = false;\n        this.canvas.addEventListener('mousedown', this._handleMouseDown);\n    }\n    _handleTouchEvents() {\n        this.canvas.addEventListener('touchstart', this._handleTouchStart);\n    }\n    _reset(options) {\n        this._lastPoints = [];\n        this._lastVelocity = 0;\n        this._lastWidth = (options.minWidth + options.maxWidth) / 2;\n        this._ctx.fillStyle = options.penColor;\n        this._ctx.globalCompositeOperation = options.compositeOperation;\n    }\n    _createPoint(x, y, pressure) {\n        const rect = this.canvas.getBoundingClientRect();\n        return new Point(x - rect.left, y - rect.top, pressure, new Date().getTime());\n    }\n    _addPoint(point, options) {\n        const { _lastPoints } = this;\n        _lastPoints.push(point);\n        if (_lastPoints.length > 2) {\n            if (_lastPoints.length === 3) {\n                _lastPoints.unshift(_lastPoints[0]);\n            }\n            const widths = this._calculateCurveWidths(_lastPoints[1], _lastPoints[2], options);\n            const curve = Bezier.fromPoints(_lastPoints, widths);\n            _lastPoints.shift();\n            return curve;\n        }\n        return null;\n    }\n    _calculateCurveWidths(startPoint, endPoint, options) {\n        const velocity = options.velocityFilterWeight * endPoint.velocityFrom(startPoint) +\n            (1 - options.velocityFilterWeight) * this._lastVelocity;\n        const newWidth = this._strokeWidth(velocity, options);\n        const widths = {\n            end: newWidth,\n            start: this._lastWidth,\n        };\n        this._lastVelocity = velocity;\n        this._lastWidth = newWidth;\n        return widths;\n    }\n    _strokeWidth(velocity, options) {\n        return Math.max(options.maxWidth / (velocity + 1), options.minWidth);\n    }\n    _drawCurveSegment(x, y, width) {\n        const ctx = this._ctx;\n        ctx.moveTo(x, y);\n        ctx.arc(x, y, width, 0, 2 * Math.PI, false);\n        this._isEmpty = false;\n    }\n    _drawCurve(curve, options) {\n        const ctx = this._ctx;\n        const widthDelta = curve.endWidth - curve.startWidth;\n        const drawSteps = Math.ceil(curve.length()) * 2;\n        ctx.beginPath();\n        ctx.fillStyle = options.penColor;\n        for (let i = 0; i < drawSteps; i += 1) {\n            const t = i / drawSteps;\n            const tt = t * t;\n            const ttt = tt * t;\n            const u = 1 - t;\n            const uu = u * u;\n            const uuu = uu * u;\n            let x = uuu * curve.startPoint.x;\n            x += 3 * uu * t * curve.control1.x;\n            x += 3 * u * tt * curve.control2.x;\n            x += ttt * curve.endPoint.x;\n            let y = uuu * curve.startPoint.y;\n            y += 3 * uu * t * curve.control1.y;\n            y += 3 * u * tt * curve.control2.y;\n            y += ttt * curve.endPoint.y;\n            const width = Math.min(curve.startWidth + ttt * widthDelta, options.maxWidth);\n            this._drawCurveSegment(x, y, width);\n        }\n        ctx.closePath();\n        ctx.fill();\n    }\n    _drawDot(point, options) {\n        const ctx = this._ctx;\n        const width = options.dotSize > 0\n            ? options.dotSize\n            : (options.minWidth + options.maxWidth) / 2;\n        ctx.beginPath();\n        this._drawCurveSegment(point.x, point.y, width);\n        ctx.closePath();\n        ctx.fillStyle = options.penColor;\n        ctx.fill();\n    }\n    _fromData(pointGroups, drawCurve, drawDot) {\n        for (const group of pointGroups) {\n            const { points } = group;\n            const pointGroupOptions = this._getPointGroupOptions(group);\n            if (points.length > 1) {\n                for (let j = 0; j < points.length; j += 1) {\n                    const basicPoint = points[j];\n                    const point = new Point(basicPoint.x, basicPoint.y, basicPoint.pressure, basicPoint.time);\n                    if (j === 0) {\n                        this._reset(pointGroupOptions);\n                    }\n                    const curve = this._addPoint(point, pointGroupOptions);\n                    if (curve) {\n                        drawCurve(curve, pointGroupOptions);\n                    }\n                }\n            }\n            else {\n                this._reset(pointGroupOptions);\n                drawDot(points[0], pointGroupOptions);\n            }\n        }\n    }\n    toSVG({ includeBackgroundColor = false } = {}) {\n        const pointGroups = this._data;\n        const ratio = Math.max(window.devicePixelRatio || 1, 1);\n        const minX = 0;\n        const minY = 0;\n        const maxX = this.canvas.width / ratio;\n        const maxY = this.canvas.height / ratio;\n        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n        svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n        svg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');\n        svg.setAttribute('viewBox', `${minX} ${minY} ${maxX} ${maxY}`);\n        svg.setAttribute('width', maxX.toString());\n        svg.setAttribute('height', maxY.toString());\n        if (includeBackgroundColor && this.backgroundColor) {\n            const rect = document.createElement('rect');\n            rect.setAttribute('width', '100%');\n            rect.setAttribute('height', '100%');\n            rect.setAttribute('fill', this.backgroundColor);\n            svg.appendChild(rect);\n        }\n        this._fromData(pointGroups, (curve, { penColor }) => {\n            const path = document.createElement('path');\n            if (!isNaN(curve.control1.x) &&\n                !isNaN(curve.control1.y) &&\n                !isNaN(curve.control2.x) &&\n                !isNaN(curve.control2.y)) {\n                const attr = `M ${curve.startPoint.x.toFixed(3)},${curve.startPoint.y.toFixed(3)} ` +\n                    `C ${curve.control1.x.toFixed(3)},${curve.control1.y.toFixed(3)} ` +\n                    `${curve.control2.x.toFixed(3)},${curve.control2.y.toFixed(3)} ` +\n                    `${curve.endPoint.x.toFixed(3)},${curve.endPoint.y.toFixed(3)}`;\n                path.setAttribute('d', attr);\n                path.setAttribute('stroke-width', (curve.endWidth * 2.25).toFixed(3));\n                path.setAttribute('stroke', penColor);\n                path.setAttribute('fill', 'none');\n                path.setAttribute('stroke-linecap', 'round');\n                svg.appendChild(path);\n            }\n        }, (point, { penColor, dotSize, minWidth, maxWidth }) => {\n            const circle = document.createElement('circle');\n            const size = dotSize > 0 ? dotSize : (minWidth + maxWidth) / 2;\n            circle.setAttribute('r', size.toString());\n            circle.setAttribute('cx', point.x.toString());\n            circle.setAttribute('cy', point.y.toString());\n            circle.setAttribute('fill', penColor);\n            svg.appendChild(circle);\n        });\n        return svg.outerHTML;\n    }\n}\n\nexport { SignaturePad as default };\n\n"], "mappings": ";;;AAKA,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,GAAG,GAAG,UAAU,MAAM;AAC9B,QAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACtB,YAAM,IAAI,MAAM,sBAAsB,CAAC,KAAK,CAAC,GAAG;AAAA,IACpD;AACA,SAAK,IAAI,CAAC;AACV,SAAK,IAAI,CAAC;AACV,SAAK,WAAW,YAAY;AAC5B,SAAK,OAAO,QAAQ,KAAK,IAAI;AAAA,EACjC;AAAA,EACA,WAAW,OAAO;AACd,WAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,EAClF;AAAA,EACA,OAAO,OAAO;AACV,WAAQ,KAAK,MAAM,MAAM,KACrB,KAAK,MAAM,MAAM,KACjB,KAAK,aAAa,MAAM,YACxB,KAAK,SAAS,MAAM;AAAA,EAC5B;AAAA,EACA,aAAa,OAAO;AAChB,WAAO,KAAK,SAAS,MAAM,OACrB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,MAAM,QAC5C;AAAA,EACV;AACJ;AAEA,IAAM,SAAN,MAAM,QAAO;AAAA,EACT,OAAO,WAAW,QAAQ,QAAQ;AAC9B,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AACxE,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AACxE,WAAO,IAAI,QAAO,OAAO,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,GAAG,OAAO,OAAO,OAAO,GAAG;AAAA,EAC5E;AAAA,EACA,OAAO,uBAAuB,IAAI,IAAI,IAAI;AACtC,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAC5D,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAC5D,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC1C,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC1C,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,MAAM,KAAK;AACxC,UAAM,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,EAAE;AAClD,UAAM,KAAK,GAAG,IAAI,GAAG;AACrB,UAAM,KAAK,GAAG,IAAI,GAAG;AACrB,WAAO;AAAA,MACH,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MAClC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,YAAY,YAAY,UAAU,UAAU,UAAU,YAAY,UAAU;AACxE,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,SAAS;AACL,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,QAAI;AACJ,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AAChC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC7F,YAAM,KAAK,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC7F,UAAI,IAAI,GAAG;AACP,cAAM,QAAQ,KAAK;AACnB,cAAM,QAAQ,KAAK;AACnB,kBAAU,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACrD;AACA,WAAK;AACL,WAAK;AAAA,IACT;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,GAAG,OAAO,IAAI,IAAI,KAAK;AACzB,WAAQ,SAAS,IAAM,MAAM,IAAM,MAAM,IAAM,KACxC,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IACnC,IAAM,MAAM,IAAM,KAAK,IAAI,IAC3B,MAAM,IAAI,IAAI;AAAA,EACzB;AACJ;AAEA,IAAM,uBAAN,MAA2B;AAAA,EACvB,cAAc;AACV,QAAI;AACA,WAAK,MAAM,IAAI,YAAY;AAAA,IAC/B,SACO,IAAI;AACP,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA,EACA,iBAAiB,MAAM,UAAU,SAAS;AACtC,SAAK,IAAI,iBAAiB,MAAM,UAAU,OAAO;AAAA,EACrD;AAAA,EACA,cAAc,OAAO;AACjB,WAAO,KAAK,IAAI,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,oBAAoB,MAAM,UAAU,SAAS;AACzC,SAAK,IAAI,oBAAoB,MAAM,UAAU,OAAO;AAAA,EACxD;AACJ;AAEA,SAAS,SAAS,IAAI,OAAO,KAAK;AAC9B,MAAI,WAAW;AACf,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ,MAAM;AAChB,eAAW,KAAK,IAAI;AACpB,cAAU;AACV,aAAS,GAAG,MAAM,eAAe,UAAU;AAC3C,QAAI,CAAC,SAAS;AACV,sBAAgB;AAChB,mBAAa,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,WAAW,MAAM;AAC7B,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,YAAY,QAAQ,MAAM;AAChC,oBAAgB;AAChB,iBAAa;AACb,QAAI,aAAa,KAAK,YAAY,MAAM;AACpC,UAAI,SAAS;AACT,qBAAa,OAAO;AACpB,kBAAU;AAAA,MACd;AACA,iBAAW;AACX,eAAS,GAAG,MAAM,eAAe,UAAU;AAC3C,UAAI,CAAC,SAAS;AACV,wBAAgB;AAChB,qBAAa,CAAC;AAAA,MAClB;AAAA,IACJ,WACS,CAAC,SAAS;AACf,gBAAU,OAAO,WAAW,OAAO,SAAS;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,eAAN,MAAM,sBAAqB,qBAAqB;AAAA,EAC5C,YAAY,QAAQ,UAAU,CAAC,GAAG;AAC9B,QAAI,IAAI,IAAI;AACZ,UAAM;AACN,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ,CAAC;AACd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,mBAAmB,CAAC,UAAU;AAC/B,UAAI,CAAC,KAAK,qBAAqB,OAAO,IAAI,KAAK,KAAK,gBAAgB;AAChE;AAAA,MACJ;AACA,WAAK,aAAa,KAAK,8BAA8B,KAAK,CAAC;AAAA,IAC/D;AACA,SAAK,mBAAmB,CAAC,UAAU;AAC/B,UAAI,CAAC,KAAK,qBAAqB,OAAO,IAAI,KAAK,CAAC,KAAK,gBAAgB;AACjE,aAAK,WAAW,KAAK,8BAA8B,KAAK,GAAG,KAAK;AAChE;AAAA,MACJ;AACA,WAAK,kBAAkB,KAAK,8BAA8B,KAAK,CAAC;AAAA,IACpE;AACA,SAAK,iBAAiB,CAAC,UAAU;AAC7B,UAAI,KAAK,qBAAqB,KAAK,GAAG;AAClC;AAAA,MACJ;AACA,WAAK,WAAW,KAAK,8BAA8B,KAAK,CAAC;AAAA,IAC7D;AACA,SAAK,oBAAoB,CAAC,UAAU;AAChC,UAAI,MAAM,cAAc,WAAW,KAAK,KAAK,gBAAgB;AACzD;AAAA,MACJ;AACA,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA,WAAK,aAAa,KAAK,4BAA4B,KAAK,CAAC;AAAA,IAC7D;AACA,SAAK,mBAAmB,CAAC,UAAU;AAC/B,UAAI,MAAM,cAAc,WAAW,GAAG;AAClC;AAAA,MACJ;AACA,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA,UAAI,CAAC,KAAK,gBAAgB;AACtB,aAAK,WAAW,KAAK,4BAA4B,KAAK,GAAG,KAAK;AAC9D;AAAA,MACJ;AACA,WAAK,kBAAkB,KAAK,4BAA4B,KAAK,CAAC;AAAA,IAClE;AACA,SAAK,kBAAkB,CAAC,UAAU;AAC9B,UAAI,MAAM,cAAc,WAAW,GAAG;AAClC;AAAA,MACJ;AACA,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA,WAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,WAAK,WAAW,KAAK,4BAA4B,KAAK,CAAC;AAAA,IAC3D;AACA,SAAK,qBAAqB,CAAC,UAAU;AACjC,UAAI,CAAC,MAAM,aAAa,CAAC,KAAK,qBAAqB,KAAK,KAAK,KAAK,gBAAgB;AAC9E;AAAA,MACJ;AACA,YAAM,eAAe;AACrB,WAAK,aAAa,KAAK,8BAA8B,KAAK,CAAC;AAAA,IAC/D;AACA,SAAK,qBAAqB,CAAC,UAAU;AACjC,UAAI,CAAC,MAAM,WAAW;AAClB;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,qBAAqB,OAAO,IAAI,KAAK,CAAC,KAAK,gBAAgB;AACjE,aAAK,WAAW,KAAK,8BAA8B,KAAK,GAAG,KAAK;AAChE;AAAA,MACJ;AACA,YAAM,eAAe;AACrB,WAAK,kBAAkB,KAAK,8BAA8B,KAAK,CAAC;AAAA,IACpE;AACA,SAAK,mBAAmB,CAAC,UAAU;AAC/B,UAAI,CAAC,MAAM,aAAa,KAAK,qBAAqB,KAAK,GAAG;AACtD;AAAA,MACJ;AACA,YAAM,eAAe;AACrB,WAAK,WAAW,KAAK,8BAA8B,KAAK,CAAC;AAAA,IAC7D;AACA,SAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,YAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AACzE,SAAK,eAAe,KAAK,QAAQ,iBAAiB,QAAQ,OAAO,SAAS,KAAK;AAC/E,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,kBAAkB,QAAQ,mBAAmB;AAClD,SAAK,qBAAqB,QAAQ,sBAAsB;AACxD,SAAK,wBAAwB,KAAK,QAAQ,0BAA0B,QAAQ,OAAO,SAAS,KAAK,CAAC;AAClG,SAAK,oBAAoB,KAAK,WACxB,SAAS,cAAa,UAAU,eAAe,KAAK,QAAQ,IAC5D,cAAa,UAAU;AAC7B,SAAK,OAAO,OAAO,WAAW,MAAM,KAAK,oBAAoB;AAC7D,SAAK,MAAM;AACX,SAAK,GAAG;AAAA,EACZ;AAAA,EACA,QAAQ;AACJ,UAAM,EAAE,MAAM,KAAK,OAAO,IAAI;AAC9B,QAAI,YAAY,KAAK;AACrB,QAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,QAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC9C,SAAK,QAAQ,CAAC;AACd,SAAK,OAAO,KAAK,sBAAsB,CAAC;AACxC,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,YAAY,SAAS,UAAU,CAAC,GAAG;AAC/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,QAAQ,IAAI,MAAM;AACxB,YAAM,QAAQ,QAAQ,SAAS,OAAO,oBAAoB;AAC1D,YAAM,QAAQ,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACnD,YAAM,SAAS,QAAQ,UAAU,KAAK,OAAO,SAAS;AACtD,YAAM,UAAU,QAAQ,WAAW;AACnC,YAAM,UAAU,QAAQ,WAAW;AACnC,WAAK,OAAO,KAAK,sBAAsB,CAAC;AACxC,YAAM,SAAS,MAAM;AACjB,aAAK,KAAK,UAAU,OAAO,SAAS,SAAS,OAAO,MAAM;AAC1D,gBAAQ;AAAA,MACZ;AACA,YAAM,UAAU,CAAC,UAAU;AACvB,eAAO,KAAK;AAAA,MAChB;AACA,YAAM,cAAc;AACpB,YAAM,MAAM;AACZ,WAAK,WAAW;AAAA,IACpB,CAAC;AAAA,EACL;AAAA,EACA,UAAU,OAAO,aAAa,gBAAgB;AAC1C,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,YAAI,OAAO,mBAAmB,UAAU;AACpC,2BAAiB;AAAA,QACrB;AACA,eAAO,6BAA6B,KAAK,KAAK,MAAM,cAAc,CAAC,CAAC;AAAA,MACxE;AACI,YAAI,OAAO,mBAAmB,UAAU;AACpC,2BAAiB;AAAA,QACrB;AACA,eAAO,KAAK,OAAO,UAAU,MAAM,cAAc;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,KAAK;AACD,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAClC,SAAK,OAAO,MAAM,aAAa;AAC/B,UAAM,QAAQ,YAAY,KAAK,UAAU,SAAS,KAAK,kBAAkB;AACzE,QAAI,OAAO,gBAAgB,CAAC,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC9B,OACK;AACD,WAAK,mBAAmB;AACxB,UAAI,kBAAkB,QAAQ;AAC1B,aAAK,mBAAmB;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM;AACF,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAClC,SAAK,OAAO,MAAM,aAAa;AAC/B,SAAK,OAAO,oBAAoB,eAAe,KAAK,kBAAkB;AACtE,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,oBAAoB,cAAc,KAAK,iBAAiB;AACpE,SAAK,4BAA4B;AAAA,EACrC;AAAA,EACA,wBAAwB;AACpB,QAAI;AACJ,UAAM,eAAe,OAAO,aAAa,KAAK,OAAO,gBAC/C,UACC,KAAK,KAAK,OAAO,cAAc,iBAAiB,QAAQ,OAAO,SAAS,KAAK,KAAK,OAAO;AAChG,WAAO;AAAA,MACH,kBAAkB,aAAa,iBAAiB,KAAK,YAAY;AAAA,MACjE,qBAAqB,aAAa,oBAAoB,KAAK,YAAY;AAAA,IAC3E;AAAA,EACJ;AAAA,EACA,8BAA8B;AAC1B,UAAM,EAAE,oBAAoB,IAAI,KAAK,sBAAsB;AAC3D,wBAAoB,eAAe,KAAK,kBAAkB;AAC1D,wBAAoB,aAAa,KAAK,gBAAgB;AACtD,wBAAoB,aAAa,KAAK,gBAAgB;AACtD,wBAAoB,WAAW,KAAK,cAAc;AAClD,wBAAoB,aAAa,KAAK,gBAAgB;AACtD,wBAAoB,YAAY,KAAK,eAAe;AAAA,EACxD;AAAA,EACA,UAAU;AACN,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,SAAS,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,GAAG;AACzC,QAAI,OAAO;AACP,WAAK,MAAM;AAAA,IACf;AACA,SAAK,UAAU,aAAa,KAAK,WAAW,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,IAAI,CAAC;AAChF,SAAK,QAAQ,KAAK,MAAM,OAAO,WAAW;AAAA,EAC9C;AAAA,EACA,SAAS;AACL,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,qBAAqB,OAAO,MAAM;AAC9B,QAAI,MAAM;AACN,aAAO,MAAM,YAAY;AAAA,IAC7B;AACA,YAAQ,MAAM,UAAU,OAAO;AAAA,EACnC;AAAA,EACA,8BAA8B,OAAO;AACjC,WAAO;AAAA,MACH;AAAA,MACA,MAAM,MAAM;AAAA,MACZ,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,UAAU,cAAc,QAAQ,MAAM,WAAW;AAAA,IACrD;AAAA,EACJ;AAAA,EACA,4BAA4B,OAAO;AAC/B,UAAM,QAAQ,MAAM,eAAe,CAAC;AACpC,WAAO;AAAA,MACH;AAAA,MACA,MAAM,MAAM;AAAA,MACZ,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,UAAU,MAAM;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,sBAAsB,OAAO;AACzB,WAAO;AAAA,MACH,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;AAAA,MAC/D,SAAS,SAAS,aAAa,QAAQ,MAAM,UAAU,KAAK;AAAA,MAC5D,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;AAAA,MAC/D,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;AAAA,MAC/D,sBAAsB,SAAS,0BAA0B,QACnD,MAAM,uBACN,KAAK;AAAA,MACX,oBAAoB,SAAS,wBAAwB,QAC/C,MAAM,qBACN,KAAK;AAAA,IACf;AAAA,EACJ;AAAA,EACA,aAAa,OAAO;AAChB,UAAM,YAAY,CAAC,KAAK,cAAc,IAAI,YAAY,eAAe,EAAE,QAAQ,OAAO,YAAY,KAAK,CAAC,CAAC;AACzG,QAAI,WAAW;AACX;AAAA,IACJ;AACA,UAAM,EAAE,iBAAiB,IAAI,KAAK,sBAAsB;AACxD,YAAQ,MAAM,MAAM,MAAM;AAAA,MACtB,KAAK;AACD,yBAAiB,aAAa,KAAK,gBAAgB;AACnD,yBAAiB,WAAW,KAAK,cAAc;AAC/C;AAAA,MACJ,KAAK;AACD,yBAAiB,aAAa,KAAK,gBAAgB;AACnD,yBAAiB,YAAY,KAAK,eAAe;AACjD;AAAA,MACJ,KAAK;AACD,yBAAiB,eAAe,KAAK,kBAAkB;AACvD,yBAAiB,aAAa,KAAK,gBAAgB;AACnD;AAAA,IACR;AACA,SAAK,iBAAiB;AACtB,UAAM,oBAAoB,KAAK,sBAAsB;AACrD,UAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;AACxF,SAAK,MAAM,KAAK,aAAa;AAC7B,SAAK,OAAO,iBAAiB;AAC7B,SAAK,cAAc,KAAK;AAAA,EAC5B;AAAA,EACA,cAAc,OAAO;AACjB,QAAI,CAAC,KAAK,gBAAgB;AACtB;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,WAAW,GAAG;AACzB,WAAK,aAAa,KAAK;AACvB;AAAA,IACJ;AACA,SAAK,cAAc,IAAI,YAAY,sBAAsB,EAAE,QAAQ,MAAM,CAAC,CAAC;AAC3E,UAAM,QAAQ,KAAK,aAAa,MAAM,GAAG,MAAM,GAAG,MAAM,QAAQ;AAChE,UAAM,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACvD,UAAM,aAAa,eAAe;AAClC,UAAM,YAAY,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC;AAC3E,UAAM,sBAAsB,YACtB,MAAM,WAAW,SAAS,KAAK,KAAK,cACpC;AACN,UAAM,oBAAoB,KAAK,sBAAsB,cAAc;AACnE,QAAI,CAAC,aAAa,EAAE,aAAa,sBAAsB;AACnD,YAAM,QAAQ,KAAK,UAAU,OAAO,iBAAiB;AACrD,UAAI,CAAC,WAAW;AACZ,aAAK,SAAS,OAAO,iBAAiB;AAAA,MAC1C,WACS,OAAO;AACZ,aAAK,WAAW,OAAO,iBAAiB;AAAA,MAC5C;AACA,iBAAW,KAAK;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,QACT,UAAU,MAAM;AAAA,MACpB,CAAC;AAAA,IACL;AACA,SAAK,cAAc,IAAI,YAAY,qBAAqB,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC9E;AAAA,EACA,WAAW,OAAO,eAAe,MAAM;AACnC,SAAK,4BAA4B;AACjC,QAAI,CAAC,KAAK,gBAAgB;AACtB;AAAA,IACJ;AACA,QAAI,cAAc;AACd,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,SAAK,iBAAiB;AACtB,SAAK,cAAc,IAAI,YAAY,aAAa,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,EACtE;AAAA,EACA,uBAAuB;AACnB,SAAK,iBAAiB;AACtB,SAAK,OAAO,iBAAiB,eAAe,KAAK,kBAAkB;AAAA,EACvE;AAAA,EACA,qBAAqB;AACjB,SAAK,iBAAiB;AACtB,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAAA,EACnE;AAAA,EACA,qBAAqB;AACjB,SAAK,OAAO,iBAAiB,cAAc,KAAK,iBAAiB;AAAA,EACrE;AAAA,EACA,OAAO,SAAS;AACZ,SAAK,cAAc,CAAC;AACpB,SAAK,gBAAgB;AACrB,SAAK,cAAc,QAAQ,WAAW,QAAQ,YAAY;AAC1D,SAAK,KAAK,YAAY,QAAQ;AAC9B,SAAK,KAAK,2BAA2B,QAAQ;AAAA,EACjD;AAAA,EACA,aAAa,GAAG,GAAG,UAAU;AACzB,UAAM,OAAO,KAAK,OAAO,sBAAsB;AAC/C,WAAO,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,WAAU,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,EAChF;AAAA,EACA,UAAU,OAAO,SAAS;AACtB,UAAM,EAAE,YAAY,IAAI;AACxB,gBAAY,KAAK,KAAK;AACtB,QAAI,YAAY,SAAS,GAAG;AACxB,UAAI,YAAY,WAAW,GAAG;AAC1B,oBAAY,QAAQ,YAAY,CAAC,CAAC;AAAA,MACtC;AACA,YAAM,SAAS,KAAK,sBAAsB,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,OAAO;AACjF,YAAM,QAAQ,OAAO,WAAW,aAAa,MAAM;AACnD,kBAAY,MAAM;AAClB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,sBAAsB,YAAY,UAAU,SAAS;AACjD,UAAM,WAAW,QAAQ,uBAAuB,SAAS,aAAa,UAAU,KAC3E,IAAI,QAAQ,wBAAwB,KAAK;AAC9C,UAAM,WAAW,KAAK,aAAa,UAAU,OAAO;AACpD,UAAM,SAAS;AAAA,MACX,KAAK;AAAA,MACL,OAAO,KAAK;AAAA,IAChB;AACA,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,WAAO;AAAA,EACX;AAAA,EACA,aAAa,UAAU,SAAS;AAC5B,WAAO,KAAK,IAAI,QAAQ,YAAY,WAAW,IAAI,QAAQ,QAAQ;AAAA,EACvE;AAAA,EACA,kBAAkB,GAAG,GAAG,OAAO;AAC3B,UAAM,MAAM,KAAK;AACjB,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,IAAI,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,IAAI,KAAK;AAC1C,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,WAAW,OAAO,SAAS;AACvB,UAAM,MAAM,KAAK;AACjB,UAAM,aAAa,MAAM,WAAW,MAAM;AAC1C,UAAM,YAAY,KAAK,KAAK,MAAM,OAAO,CAAC,IAAI;AAC9C,QAAI,UAAU;AACd,QAAI,YAAY,QAAQ;AACxB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AACnC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AACjB,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AACjB,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAC1B,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAC1B,YAAM,QAAQ,KAAK,IAAI,MAAM,aAAa,MAAM,YAAY,QAAQ,QAAQ;AAC5E,WAAK,kBAAkB,GAAG,GAAG,KAAK;AAAA,IACtC;AACA,QAAI,UAAU;AACd,QAAI,KAAK;AAAA,EACb;AAAA,EACA,SAAS,OAAO,SAAS;AACrB,UAAM,MAAM,KAAK;AACjB,UAAM,QAAQ,QAAQ,UAAU,IAC1B,QAAQ,WACP,QAAQ,WAAW,QAAQ,YAAY;AAC9C,QAAI,UAAU;AACd,SAAK,kBAAkB,MAAM,GAAG,MAAM,GAAG,KAAK;AAC9C,QAAI,UAAU;AACd,QAAI,YAAY,QAAQ;AACxB,QAAI,KAAK;AAAA,EACb;AAAA,EACA,UAAU,aAAa,WAAW,SAAS;AACvC,eAAW,SAAS,aAAa;AAC7B,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,oBAAoB,KAAK,sBAAsB,KAAK;AAC1D,UAAI,OAAO,SAAS,GAAG;AACnB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvC,gBAAM,aAAa,OAAO,CAAC;AAC3B,gBAAM,QAAQ,IAAI,MAAM,WAAW,GAAG,WAAW,GAAG,WAAW,UAAU,WAAW,IAAI;AACxF,cAAI,MAAM,GAAG;AACT,iBAAK,OAAO,iBAAiB;AAAA,UACjC;AACA,gBAAM,QAAQ,KAAK,UAAU,OAAO,iBAAiB;AACrD,cAAI,OAAO;AACP,sBAAU,OAAO,iBAAiB;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ,OACK;AACD,aAAK,OAAO,iBAAiB;AAC7B,gBAAQ,OAAO,CAAC,GAAG,iBAAiB;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,EAAE,yBAAyB,MAAM,IAAI,CAAC,GAAG;AAC3C,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC;AACtD,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,OAAO,KAAK,OAAO,SAAS;AAClC,UAAM,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AACxE,QAAI,aAAa,SAAS,4BAA4B;AACtD,QAAI,aAAa,eAAe,8BAA8B;AAC9D,QAAI,aAAa,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC7D,QAAI,aAAa,SAAS,KAAK,SAAS,CAAC;AACzC,QAAI,aAAa,UAAU,KAAK,SAAS,CAAC;AAC1C,QAAI,0BAA0B,KAAK,iBAAiB;AAChD,YAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,WAAK,aAAa,SAAS,MAAM;AACjC,WAAK,aAAa,UAAU,MAAM;AAClC,WAAK,aAAa,QAAQ,KAAK,eAAe;AAC9C,UAAI,YAAY,IAAI;AAAA,IACxB;AACA,SAAK,UAAU,aAAa,CAAC,OAAO,EAAE,SAAS,MAAM;AACjD,YAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,UAAI,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG;AAC1B,cAAM,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,WAAW,EAAE,QAAQ,CAAC,CAAC,MACvE,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC5D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC1D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC;AACjE,aAAK,aAAa,KAAK,IAAI;AAC3B,aAAK,aAAa,iBAAiB,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AACpE,aAAK,aAAa,UAAU,QAAQ;AACpC,aAAK,aAAa,QAAQ,MAAM;AAChC,aAAK,aAAa,kBAAkB,OAAO;AAC3C,YAAI,YAAY,IAAI;AAAA,MACxB;AAAA,IACJ,GAAG,CAAC,OAAO,EAAE,UAAU,SAAS,UAAU,SAAS,MAAM;AACrD,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,YAAM,OAAO,UAAU,IAAI,WAAW,WAAW,YAAY;AAC7D,aAAO,aAAa,KAAK,KAAK,SAAS,CAAC;AACxC,aAAO,aAAa,MAAM,MAAM,EAAE,SAAS,CAAC;AAC5C,aAAO,aAAa,MAAM,MAAM,EAAE,SAAS,CAAC;AAC5C,aAAO,aAAa,QAAQ,QAAQ;AACpC,UAAI,YAAY,MAAM;AAAA,IAC1B,CAAC;AACD,WAAO,IAAI;AAAA,EACf;AACJ;", "names": []}