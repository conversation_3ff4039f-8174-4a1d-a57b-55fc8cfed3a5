<div class="card-background" [routerLink]="routerLink">
    <div class="card-top-content ">
        <div class="card-top-content-header mb-1">{{ cardHeader }}</div>
        <div class="row">
            <div class="col-6 px-0">
                <div class="card-count-text ms-2">{{ cardSubHeader ? cardSubHeader : 10}}</div>
            </div>
            <div class="col-6 px-0 text-right d-flex-center">
                <ng-content></ng-content>
                <!-- <img [src]="'/assets/images/icons/' + imageSrc + '.svg'" alt="" width="66" height="66"> -->
            </div>
        </div>

    </div>
</div>