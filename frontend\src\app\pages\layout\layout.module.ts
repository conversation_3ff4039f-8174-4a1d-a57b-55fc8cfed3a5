import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NgbDropdownModule, NgbToastModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { DataTablesModule } from 'angular-datatables';

// shared directives
import { RippleEffectDirective } from '../../shared/directives/ripple-effect.directive';
import { NameInitialPipe } from '../../shared/pipes/name-initial.pipe';
import { RoleTransformPipe } from '../../shared/pipes/role-transform.pipe';

// layout components
import { LayoutComponent } from './layout.component';
import { LAYOUTROUTING } from './layout.routing';
import { EditNotificationDetailComponent } from './notifications/edit-notification-detail/edit-notification-detail.component';

@NgModule({
    declarations: [LayoutComponent],
    imports: [
        RouterModule.forChild(LAYOUTROUTING),
        DataTablesModule,
        CommonModule,
        NgbToastModule,
        NgbTooltipModule,
        NgbDropdownModule,
        NameInitialPipe,
        RoleTransformPipe,
        RippleEffectDirective,
        EditNotificationDetailComponent,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient],
            },
        }),
    ],
    providers: [],
})
export class LayoutModule { }

export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
    return new TranslateHttpLoader(http);
}
