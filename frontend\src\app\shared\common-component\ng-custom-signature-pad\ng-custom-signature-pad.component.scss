@import "../../../../variables.scss";

.signature-pad {
    .canvas-container {
        box-shadow: 1px 1px 10px 1px #f3f0f0 !important;
        width: calc(100% - 2px) !important;
        height: 100% !important;
        border-radius: 14px !important;
        border: 1px solid #dee2e6 !important;
        padding: 12px !important;
        cursor: crosshair !important;
    }

    .clear-btn-canvas {
        min-width: 80px !important;
        border: none !important;
        margin-right: 12px !important;
        padding: 2px 20px !important;
        border-radius: 10px 10px 0px 0px !important;
        color: $black-color !important;
        background-color: var(--primary-color) !important;
        font-weight: $font-weight-600 !important;
        font-size: $font-size-14 !important;
    }
}

.canvas-wrapper.disabled {
    cursor: not-allowed;

    canvas {
        pointer-events: none;
        background-color: #f0f0f0;
        border: 1px dashed #bbb !important;
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    button {
        pointer-events: none;
        cursor: not-allowed;
        opacity: 0.5;
    }
}