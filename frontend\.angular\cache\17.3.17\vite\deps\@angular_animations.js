import {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationGroupPlayer,
  AnimationMetadataType,
  BrowserAnimationBuilder,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  ɵPRE_STYLE
} from "./chunk-3RQK2E55.js";
import "./chunk-VMAOIBZT.js";
import "./chunk-IJ7Y55ZG.js";
import "./chunk-N4CPHFRV.js";
import "./chunk-3S2TBZJ6.js";
import "./chunk-FCQSAM7T.js";
import "./chunk-EIB7IA3J.js";
export {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationMetadataType,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  AnimationGroupPlayer as ɵAnimationGroupPlayer,
  BrowserAnimationBuilder as ɵBrowserAnimationBuilder,
  ɵPRE_STYLE
};
//# sourceMappingURL=@angular_animations.js.map
