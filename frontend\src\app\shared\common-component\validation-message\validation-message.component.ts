// Angular core imports
import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

// Third-party library imports
import { TranslateModule } from '@ngx-translate/core';


@Component({
    selector: 'app-validation-message',
    templateUrl: './validation-message.component.html',
    styleUrls: ['./validation-message.component.scss'],
    standalone: true,
    imports: [TranslateModule,CommonModule]
})
export class ValidationMessageComponent {
    [x: string]: any;
    type: any;
    @Input() field: any;
    @Input() onClickValidation!: boolean;
    @Input() customErrorMessage!: string;
    @Input() comparableField!: any;
    @Input() customClass!: string;
    @Input() fieldErrorMessage!: string;

    @Input() customPatternMessage!: string;
    @Input() customMaskMessage!: string;

    constructor() {

    }
}
