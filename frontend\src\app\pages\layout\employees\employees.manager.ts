import { Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { AccountService } from '../../../services/account.service';
import { LoadingService } from '../../../services/loading.service';
import { AuthService } from '../../../shared/services/auth.services';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';
import { EmployeeService } from './employees.service';

@Injectable({
    providedIn: 'root',
})
export class EmployeesManager extends BaseManager {
    constructor(
        private readonly employeeService: EmployeeService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
        protected accountService: AccountService,
        protected authService: AuthService,
        protected commonService: CommonService,
    ) {
        super(employeeService, loadingService, toastService);
    }

    updateEmployeeStatus(id: string, data: any): Observable<RestResponse> {
        return this.employeeService.updateEmployeeStatus(id, data).pipe(map((response: RestResponse) => response));
    }

    fetchMyProfileDetails(): Observable<RestResponse> {
        return this.employeeService.fetchMyProfileDetails().pipe(map((response: RestResponse) => response));
    }

    updateMyProfile(data: any): Observable<RestResponse> {
        return this.employeeService.updateMyProfile(data).pipe(
            map((response: RestResponse) => {
                const user = this.authService.getUser();
                user.firstName = data.firstName;
                user.lastName = data.lastName;
                user.phoneNumber = data.phoneNumber;
                user.fullName = `${data.firstName} ${data.lastName}`;
                this.authService.setUser(user);
                return response;
            }),
        );
    }

    fetchForDriverDropdown(param: FilterParam) {
        return this.employeeService.fetchForDriverDropdown(param).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
            catchError((error) => {
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
                this.toastService.error(errorMessage);
                return of([]); // Adjust the fallback type if needed
            }),
        );
    }
}
