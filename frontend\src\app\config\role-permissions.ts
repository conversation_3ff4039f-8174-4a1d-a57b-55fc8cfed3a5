import { createAccess, generateRolesAccess, ROLES } from './roles';

export const COMMON_ROLES = [ROLES.ADMIN, ROLES.SUPER_ADMIN];
const CUSTOMER_ROLES = [...COMMON_ROLES, ROLES.CUSTOMER];

export const VIEW_USER_MAPPING = {
    COMMON_ACCESS: {
        UserPage: createAccess(COMMON_ROLES),

        AccountSettingPage: createAccess(COMMON_ROLES),
    },

    EMPLOYEES_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    BARCODE_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    CUSTOMER_ACCESS: generateRolesAccess(CUSTOMER_ROLES, { addButtonRoles: COMMON_ROLES }),

    RATESHEET_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    QUOTATION_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    SHIPMENT_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    SHIPMENT_CALENDER_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    USERPROFILE_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    DRIVER_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),

    CONTACTUS_ACCESS: generateRolesAccess(COMMON_ROLES, { addButtonRoles: COMMON_ROLES }),
};
