{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"BeesExpressFrontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/BeesExpressFrontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/images/fav.ico", "src/assets"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/datatables.net-dt/css/dataTables.dataTables.css", "./node_modules/sweetalert2/dist/sweetalert2.min.css", "./node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.min.js", "./node_modules/datatables.net/js/dataTables.js", "./node_modules/bootstrap/dist/js/bootstrap.min.js", "./node_modules/sweetalert2/dist/sweetalert2.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10kb"}], "outputHashing": "all"}, "development": {"optimization": true, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "BeesExpressFrontend:build:production"}, "development": {"buildTarget": "BeesExpressFrontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "BeesExpressFrontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}