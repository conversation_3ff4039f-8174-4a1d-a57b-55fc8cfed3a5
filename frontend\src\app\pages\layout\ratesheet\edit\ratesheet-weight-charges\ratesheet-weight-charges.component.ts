// Angular Core Modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, NavigationStart, Router, RouterLink } from '@angular/router';

// Third-party Modules
import { NgbAccordionModule, NgbModal, NgbModalRef, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';
import { Subscription } from 'rxjs';

// Application Components and Services
import { BaseListServerSideComponent } from '../../../../../config/base.list.server.side.component';
import { LoadingService } from '../../../../../services/loading.service';
import { CommonService } from '../../../../../shared/services/common.service';
import { ToastService } from '../../../../../shared/services/toast.service';
import { TypeAheadService } from '../../../../../shared/services/typeahead-search.service';
import { RateSheetWeightChargesEditComponent } from './ratesheet-weight-charges-edit/ratesheet-weight-charges-edit.component';

//Application Directives and pipes
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';
import { TooltipEllipsisDirective } from '../../../../../shared/directives/tooltip-ellipsis.directive';
import { RemoveUnderscorePipe } from '../../../../../shared/pipes/remove-underscore.pipe';

// Application Configurations and Models
import { Constant } from '../../../../../config/constants';
import { RateSheetWeightCharge } from '../../../../../models/ratesheet-weight-charge';

// Feature-specific Manager
import { RateSheetSpecialRequestManager } from './ratesheet-weight-charges.manager';
import { DelayedInputDirective } from '../../../../../shared/directives/delayed-input.directive';

@Component({
    selector: 'app-rate-sheet-weight-charges',
    standalone: true,
    imports: [
        CommonModule,
        RouterLink,
        DataTablesModule,
        NgbAccordionModule,
        NgbTooltipModule,
        NgSelectModule,
        TranslateModule,
        RemoveUnderscorePipe,
        DelayedInputDirective,
        TooltipEllipsisDirective,
        RateSheetWeightChargesEditComponent,
        RippleEffectDirective,
        FormsModule,
    ],
    templateUrl: './ratesheet-weight-charges.component.html',
    styleUrl: './ratesheet-weight-charges.component.scss',
})
export class RateSheetWeightChargesComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
    @ViewChild('action') action!: TemplateRef<any>;
    @ViewChild('rateType') rateType!: TemplateRef<string>;
    @ViewChild('fromWeight') fromWeight!: TemplateRef<string>;
    @ViewChild('toWeight') toWeight!: TemplateRef<string>;
    @ViewChild('freight') freight!: TemplateRef<string>;
    @ViewChild('fuelCharges') fuelCharges!: TemplateRef<string>;
    @ViewChild('gst') gst!: TemplateRef<string>;
    @ViewChild('total') total!: TemplateRef<string>;

    @Output() onNextOrBackClick = new EventEmitter<number>();

    rateSheetWeightCharges!: Array<RateSheetWeightCharge>;
    resourceType: string = Constant.RESOURCE_TYPE.RATE_SHEET_WEIGHT_CHARGES;

    modalRef!: NgbModalRef;
    routerSubscription?: Subscription;
    rateTypes = Constant.RATE_TYPES;

    title!: string;
    rateSheetWeightCharge!: RateSheetWeightCharge;

    constructor(
        protected rateSheetSpecialRequestManager: RateSheetSpecialRequestManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
        protected typeAheadService: TypeAheadService,
        public modalService: NgbModal,
    ) {
        super(rateSheetSpecialRequestManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.rateSheetWeightCharges = new Array<RateSheetWeightCharge>();
        this.filterParam.filtering.rateSheetId = this.route.snapshot.paramMap.get('id') as string;

        this.filterParam.fileName = this.resourceType;

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.RATE_SHEET_WEIGHT_CHARGES_LIST;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.RATE_SHEET_WEIGHT_CHARGES;

        this.routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart && this.modalRef) {
                this.modalRef.close();
            }
        });

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            rateType: this.rateType,
            fromWeight: this.fromWeight,
            toWeight: this.toWeight,
            freight: this.freight,
            fuelCharges: this.fuelCharges,
            gst: this.gst,
            total: this.total,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    onNext() {
        this.onNextOrBackClick.emit(3);
    }

    onBack() {
        this.onNextOrBackClick.emit(1);
    }

    openModal(content: TemplateRef<any>, title: string, data?: any) {
        this.modalRef = this.modalService.open(content, { centered: true, size: 'lg', backdrop: 'static' });
        this.title = title;
        this.rateSheetWeightCharge = data;
    }

    override ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
        super.ngOnDestroy();
    }

    override onFetchCompleted() {
        this.rateSheetWeightCharges = this.records.map((data) => RateSheetWeightCharge.fromResponse(data));
        super.onFetchCompleted();
    }
}
