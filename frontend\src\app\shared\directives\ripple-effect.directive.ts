import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appRippleEffect]',
    standalone: true,
})
export class RippleEffectDirective {
    constructor(
        private element: ElementRef,
        private renderer: Renderer2,
    ) {
        this.renderer.setStyle(this.element.nativeElement, 'position', 'relative');
        this.renderer.setStyle(this.element.nativeElement, 'overflow', 'hidden');
    }

    @HostListener('mousedown', ['$event'])
    onMouseDown(event: MouseEvent) {
        const button = this.element.nativeElement as HTMLElement;
        const ripple = this.renderer.createElement('span');

        this.renderer.addClass(ripple, 'ripple');

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        this.renderer.setStyle(ripple, 'width', `${size}px`);
        this.renderer.setStyle(ripple, 'height', `${size}px`);
        this.renderer.setStyle(ripple, 'top', `${y}px`);
        this.renderer.setStyle(ripple, 'left', `${x}px`);

        this.renderer.appendChild(button, ripple);

        setTimeout(() => {
            this.renderer.removeChild(button, ripple);
        }, 600);
    }
}
