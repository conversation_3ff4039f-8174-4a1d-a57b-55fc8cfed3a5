<div class="site-page-container mt-3">
    <div class="site-card">
        <form #rateSheetForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <div class="label-wrap-box">
                            <span>{{"USERS.BasicInfo" | translate}}</span>
                        </div>

                        <!-- Name Field -->
                        <div class="col-md-12 px-0">
                            <div class="form-floating mb-14">
                                <input class="form-control" type="text" name="name" #Name="ngModel" required="required"
                                    [(ngModel)]="rateSheet.name" placeholder="{{'USERS.FirstName' | translate}}"
                                    [appNoWhitespaceValidator]="true"
                                    [ngClass]="{'is-invalid': !Name.valid  && onClickValidation }" />
                                <label for="name">{{"RATE_SHEET.objName#" | translate}}</label>
                                <app-validation-message [field]="Name" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Start Date field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2 mb-14">
                            <app-ng-custom-date-picker name="startDate" ngDefaultControl [isRequired]="true"
                                class="margin-bottom-12" [labelName]="'RATE_SHEET.startDate'"
                                (setDate)="setStartDate($event)" [selectedDate]="rateSheet.startDate"
                                [onClickValidation]="onClickValidation" (click)="setActiveDatePicker('startDate')"
                                [customZIndex]="openDatePicker === 'startDate' ? 'z-index-4' : 'z-index-3'"
                                [isRequired]="true">
                            </app-ng-custom-date-picker>
                        </div>

                        <!-- End Date Number -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2 mb-14">
                            <app-ng-custom-date-picker name="endDate" ngDefaultControl [isRequired]="true"
                                [minDate]="rateSheet.startDate" class="margin-bottom-12"
                                [labelName]="'RATE_SHEET.endDate'" (setDate)="rateSheet.endDate = $event"
                                [selectedDate]="rateSheet.endDate" [onClickValidation]="onClickValidation"
                                (click)="setActiveDatePicker('endDate')"
                                *ngIf="(showToEndDateField && updateToEndDateField)"
                                [customZIndex]="openDatePicker === 'endDate' ? 'z-index-4' : 'z-index-3'">
                            </app-ng-custom-date-picker>
                        </div>

                        <div class="label-wrap-box">
                            <span>{{"RATE_SHEET.pickupAddressInfo" | translate}}</span>
                        </div>

                        <!-- Pickup Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="rateSheet.pickupAddressDetail"
                            [onClickValidation]="onClickValidation" [types]="['(cities)']"
                            [addressLabel]="'Pickup City Address'" [isCityDisabled]="true" [isProvinceDisabled]="true"
                            [isZipCodeDisabled]="true" [isCountryDisabled]="true"
                            [isOnlyGoogleAddress]="true"></app-custom-address>

                        <div class="label-wrap-box">
                            <span>{{"RATE_SHEET.deliveryAddressInfo" | translate}}</span>
                        </div>

                        <!-- Delivery Address Detail -->
                        <app-custom-address [isRequired]="true" [(address)]="rateSheet.deliveryAddressDetail"
                            [onClickValidation]="onClickValidation" [types]="['(cities)']" [isCityDisabled]="true"
                            [isProvinceDisabled]="true" [isZipCodeDisabled]="true" [isCountryDisabled]="true"
                            [addressLabel]="'Delivery City Address'" [isOnlyGoogleAddress]="true"></app-custom-address>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(rateSheetForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(rateSheetForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>