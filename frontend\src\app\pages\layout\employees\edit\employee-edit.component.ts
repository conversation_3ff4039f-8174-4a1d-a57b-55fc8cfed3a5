// Angular core imports
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Routing and navigation
import { ActivatedRoute, Router } from '@angular/router';

// Third-party libraries
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { provideNgxMask } from 'ngx-mask';

// Manager
import { EmployeesManager } from '../employees.manager';

// Base component and constants
import { BaseEditComponent } from '../../../../config/base.edit.component';
import { Constant } from '../../../../config/constants';

// User model
import { Employees } from '../../../../models/customer/employees';

// Services
import { LoadingService } from '../../../../services/loading.service';
import { AuthService } from '../../../../shared/services/auth.services';
import { CommonService } from '../../../../shared/services/common.service';
import { ToastService } from '../../../../shared/services/toast.service';

// Custom components and directives
import { CustomAddressComponent } from '../../../../shared/common-component/custom-address/custom-address.component';
import { DialCodeInputComponent } from '../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { FileUploaderComponent } from '../../../../shared/common-component/file-uploader/file-uploader.component';
import { NgCustomDatePickerComponent } from '../../../../shared/common-component/ng-custom-date-picker/ng-custom-date-picker.component';
import { ValidationMessageComponent } from '../../../../shared/common-component/validation-message/validation-message.component';
import { CommonUtil } from '../../../../shared/common.util';
import { EmailValidatorDirective } from '../../../../shared/directives/email-validator.directive';
import { RippleEffectDirective } from '../../../../shared/directives/ripple-effect.directive';
import { SpecialCharRestrictorDirective } from '../../../../shared/directives/special-char-restrictor.directive';

@Component({
    selector: 'app-employee-edit',
    standalone: true,
    imports: [
        CommonModule,
        CustomAddressComponent,
        DialCodeInputComponent,
        EmailValidatorDirective,
        FileUploaderComponent,
        FormsModule,
        NgCustomDatePickerComponent,
        NgSelectModule,
        RippleEffectDirective,
        TranslateModule,
        ValidationMessageComponent,
        SpecialCharRestrictorDirective
    ],
    templateUrl: './employee-edit.component.html',
    styleUrls: ['./employee-edit.component.scss'],
    providers: [provideNgxMask()],
})
export class EmployeeEditComponent extends BaseEditComponent implements OnInit {
    employee!: Employees;
    date: string | null = CommonUtil.setTodayDate();
    phoneMaskPattern = Constant.MASKS.PHONE_MASK;
    userRoles = Constant.EMPLOYEE_ROLES;
    number!: any;
    countryCode: any = '+1-CA';
    employeeStatus = Constant.CUSTOMER_STATUS;
    activeDatePicker: boolean = true;
    openDatePicker: 'hireDate' | null = null;

    constructor(
        public authService: AuthService,
        private readonly employeesManager: EmployeesManager,
        protected override route: ActivatedRoute,
        public override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override router: Router,
        protected override commonService: CommonService,
        protected override translateService: TranslateService,
    ) {
        super(employeesManager, commonService, toastService, loadingService, route, router, translateService);
        this.employee = new Employees();
    }

    ngOnInit(): void {
        this.employee = new Employees();
        this.setRecord(this.employee);
        this.onClickValidation = false;
        this.request = { isNewRecord: true } as any;

        this.init();
    }

    override onFetchCompleted() {
        this.employee = Employees.fromResponse(this.record);
        this.setRecord(this.employee);
    }

    onRoleChange(roleId: string): void {
        if (roleId !== 'ROLE_DRIVER') {
            this.employee.driverNo = null;
        }
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/employees']);
    }

    override onSaveSuccess(data: any): void {
        this.router.navigate(['/dashboard/employees']);
    }

    setDate(event: any) {
        this.activeDatePicker = false;
        this.employee.hireDate = event;

        setTimeout(() => {
            this.activeDatePicker = true;
        });
    }

    setActiveDatePicker(picker: 'hireDate' | null) {
        this.openDatePicker = picker;
    }
}
