<div class="row">
    <form class="addressForm" #addressForm="ngForm">
        <div class="row">
            <div class="col-md-12 px-0">
                <div class="form-floating mb-14">
                    <input type="text" class="form-control" name="address" [(ngModel)]="address.address"
                        [required]="isRequired" #Address="ngModel" #addressInput placeholder=""
                        [ngClass]="{
                            'is-invalid': ((isOnlyGoogleAddress && (!Address.valid || !address.city)) || (!isOnlyGoogleAddress && !Address.valid) ) && !isAddressDisabled && onClickValidation}"
                        autocomplete="off" [appNoWhitespaceValidator]="isRequired" appGoogleAutocomplete
                        (placeChanged)="handleAddressChange($event)" [disabled]="isAddressDisabled"
                        #autocompleteInput />

                    <label for="address"> {{ addressLabel || ('Address.objName' | translate) }}
                    </label>

                    @if(!isOnlyGoogleAddress && !showOnlyAddress){
                    <app-validation-message [field]="Address" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                    }
                    @if( !showOnlyAddress && isOnlyGoogleAddress && (!Address.valid || !address.city) &&
                    onClickValidation) {
                    <div class="error-message fw-medium">
                        {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
                    </div>
                    }
                </div>
            </div>

            @if(!showOnlyAddress){
            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                <div class="form-floating  mb-14">
                    <input class="form-control" name="city" type="text" placeholder="{{'Address.city' |translate}}"
                        [(ngModel)]="address.city" #City="ngModel" [appNoWhitespaceValidator]="isRequired"
                        [ngClass]="{'is-invalid':!City.valid && !googleFilledFields.city && !isCityDisabled &&  onClickValidation}"
                        [disabled]="googleFilledFields.city || isCityDisabled" [required]="isRequired">
                    <label for="City">{{ cityLabel || ('Address.city' |translate)}}</label>

                    <app-validation-message [field]="City" [onClickValidation]="onClickValidation"
                        *ngIf="isRequired && !address.city">
                    </app-validation-message>
                </div>
            </div>

            <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                <div class="form-floating mb-14">
                    <input class="form-control" name="state" type="text" placeholder="{{'Address.province' |translate}}"
                        [required]="isRequired" [(ngModel)]="address.state" #State="ngModel"
                        [appNoWhitespaceValidator]="isRequired"
                        [ngClass]="{'is-invalid':!State.valid && !googleFilledFields.state && !isProvinceDisabled && onClickValidation}"
                        [disabled]="googleFilledFields.state || isProvinceDisabled">

                    <label for="State">{{ provinceLabel || ('Address.province' | translate) }}</label>

                    <app-validation-message [field]="State" [onClickValidation]="onClickValidation"
                        *ngIf="isRequired && !address.state">
                    </app-validation-message>
                </div>
            </div>

            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                <div class="form-floating  mb-14">
                    <input appSpecialCharRestrictor class="form-control" name="zipCode" type="text"
                        [ngClass]="{'is-invalid':!ZipCode.valid && !googleFilledFields.pin && !isZipCodeDisabled && onClickValidation }"
                        placeholder="{{'Address.zipCode' |translate}}" [(ngModel)]="address.pin" #ZipCode="ngModel"
                        [disabled]="googleFilledFields.pin || isZipCodeDisabled" [required]="isRequired">
                    <label for="ZipCode">{{ zipCodeLabel || ('Address.postalCode' | translate) }}</label>
                    <app-validation-message [field]="ZipCode" [onClickValidation]="onClickValidation"
                        *ngIf="isRequired && !address.pin">
                    </app-validation-message>
                </div>
            </div>

            <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                <div class="form-floating  mb-14">
                    <input class="form-control" name="Country" type="text"
                        placeholder="{{'Address.country' |translate}}" [(ngModel)]="address.country" #Country="ngModel"
                        [ngClass]="{'is-invalid':!Country.valid && !googleFilledFields.country && !isCountryDisabled && onClickValidation}"
                        [disabled]="googleFilledFields.country || isCountryDisabled" [required]="isRequired">

                    <label for="Country">{{ countryLabel || ('Address.country' |translate)}}</label>

                    <app-validation-message [field]="Country" [onClickValidation]="onClickValidation"
                        *ngIf="isRequired && !address.country">
                    </app-validation-message>
                </div>
            </div>
            }
        </div>
    </form>
</div>