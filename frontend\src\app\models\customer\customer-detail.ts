import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

import { TranslateService } from '@ngx-translate/core';
import { Address } from '../common/address';
export class CustomerDetail extends BaseModel {
    tenantId!: number;
    slug!: string;
    companyName!: string;
    companyPhone!: string;
    addressDetail!: Address;
    accountsPayableEmail!: string;
    keyContact!: string;
    keyContactPhone!: string;
    keyContactEmail!: string;
    keyContactPosition!: string;
    accountsContact!: string;
    accountsPhone!: string;
    accountsEmail!: string;
    accountsPosition!: string;
    companyCountryCode: string = '+1-CA';
    accountsCountryCode: string = '+1-CA';
    keyContactCountryCode: string = '+1-CA';
    step!: number;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.addressDetail = new Address();
    }

    static fromResponse(data: any): CustomerDetail {
        return { ...data };
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.companyName = this.trimMe(this.companyName);
        this.accountsPayableEmail = this.trimMe(this.accountsPayableEmail);
        this.keyContact = this.trimMe(this.keyContact);
        this.keyContactPhone = this.trimMe(this.keyContactPhone);
        this.keyContactEmail = this.trimMe(this.keyContactEmail);
        this.keyContactPosition = this.trimMe(this.keyContactPosition);
        this.accountsContact = this.trimMe(this.accountsContact);
        this.accountsPhone = this.trimMe(this.accountsPhone);
        this.accountsEmail = this.trimMe(this.accountsEmail);
        this.accountsPosition = this.trimMe(this.accountsPosition);
        this.accountsCountryCode = this.trimMe(this.accountsCountryCode);
        this.keyContactCountryCode = this.trimMe(this.keyContactCountryCode);
        return this;
    }
}
