// Angular core modules
import { NgClass } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Third-party modules
import { TranslateModule } from '@ngx-translate/core';

// Application managers
import { FormHandlerManager } from '../../managers/form-handler.manager';

// Models
import { Profile } from '../../models/access/profile';

// Services
import { AccountService } from '../../services/account.service';
import { LoadingService } from '../../services/loading.service';

// Shared utilities and services
import { AuthService } from '../../shared/services/auth.services';
import { ToastService } from '../../shared/services/toast.service';

// UI Components and Directives
import { Constant } from '../../config/constants';
import { ValidationMessageComponent } from '../../shared/common-component/validation-message/validation-message.component';
import { RippleEffectDirective } from '../../shared/directives/ripple-effect.directive';
import { TogglePasswordVisibilityModule } from '../../shared/directives/toggle-password-visibility.directive';

@Component({
    selector: 'app-change-password',
    standalone: true,
    imports: [
        FormsModule,
        NgClass,
        RippleEffectDirective,
        TogglePasswordVisibilityModule,
        TranslateModule,
        ValidationMessageComponent,
    ],
    templateUrl: './change-password.component.html',
    styleUrl: './change-password.component.scss',
})
export class ChangePasswordComponent {
    onClickValidation: boolean;
    profile: Profile;

    passwordErrorMessage = Constant.ERROR_MESSAGES.password;

    constructor(
        private readonly toastService: ToastService,
        private readonly loadingService: LoadingService,
        private readonly accountService: AccountService,
        private readonly formHandlerManager: FormHandlerManager,
        private readonly authService: AuthService,
    ) {
        this.onClickValidation = false;
        this.profile = new Profile();
    }

    async updatePassword(form: any) {
        this.onClickValidation = !form.valid;
        if (!form.valid) {
            return;
        }
        if (!this.profile.isValidChangePasswordRequest(form)) {
            return;
        }

        if (this.profile.password !== this.profile.confirmPassword) {
            this.toastService.error("Password doesn't matches");
            return;
        }

        this.loadingService.show();

        this.formHandlerManager.handleFormSubmission(
            this.accountService.changePassword(this.profile.forRequest()),
            (resp) => {
                this.loadingService.hide();

                this.profile = {} as any;
                this.toastService.success(resp.message);
                this.authService.logout();
            },
        );
    }
}
