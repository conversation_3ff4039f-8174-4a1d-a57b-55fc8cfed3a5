<div class="site-page-container mt-3">
    <div class="site-card">
        <form #shipmentForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <!-- Shipment Added -->
                        <div class="col-md-12 px-0 mb-16 d-flex justify-content-start align-content-center">
                            <label class="d-block me-2" for="securedYes"><strong>{{"SHIPMENT.podAdded" |
                                    translate}}</strong></label>

                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="pod" [(ngModel)]="shipment.podAdded"
                                    [value]="true" id="podYes" (change)="onRadioButtonChange(shipment.podAdded)"
                                    [disabled]="shipment.podAdded || disabled">
                                <label class="form-check-label" for="podYes">Yes</label>
                            </div>

                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="pod" [(ngModel)]="shipment.podAdded"
                                    [value]="false" id="podNo" (change)="onRadioButtonChange(shipment.podAdded)"
                                    [disabled]="shipment.podAdded || disabled">
                                <label class="form-check-label" for="podNo">No</label>
                            </div>
                        </div>

                        <!-- Shipment Notes -->
                        <div class="col-12 mb-16 mt-18 px-0">
                            <div class="form-floating textarea-custom"
                                [class.disabled-container]="!shipment.podAdded || disabled">
                                <textarea class="form-control" rows="4" id="podNotesId" name="podNotes"
                                    #PodNotes="ngModel" [(ngModel)]="shipment.podNotes"
                                    [disabled]="!shipment.podAdded || disabled"
                                    placeholder="{{'SHIPMENT.podNotes' | translate}}"></textarea>
                                <label for="podNotes">{{"SHIPMENT.podNotes"| translate}}</label>
                            </div>
                        </div>

                        <!-- Shipment Notes -->
                        <div class="col-12 px-0 mb-18 mt-18" *ngIf="signaturePadVisibility">
                            <app-ng-custom-signature-pad [initialSignature]="shipment.podSignatures"
                                [isHeadingShown]="false" (signatureUpdated)="onSignatureUpdated($event)"
                                #NgCustomSignaturePadComponent [disabled]="!shipment.podAdded || disabled">
                            </app-ng-custom-signature-pad>
                        </div>

                        <!-- POD Documents -->
                        <div class="col-md-12 mb-16 px-0 px-md-0" *ngIf="uploaderVisibility">
                            <app-file-uploader [uploaderId]="'employeeUploaderId'" [documents]="shipment.podImages"
                                [uploaderTitle]="'SHIPMENT.uploaderPodImages' | translate"
                                [fileTypes]="'.jpg, .jpeg, .png'"
                                [disabled]="!shipment.podAdded || disabled"></app-file-uploader>
                        </div>

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="save(shipmentForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>

                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>