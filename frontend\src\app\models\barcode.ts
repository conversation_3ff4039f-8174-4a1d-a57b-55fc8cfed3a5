import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { User } from './access/user';
import { Employees } from './customer/employees';
export class Barcode extends BaseModel {
    tenantId!: number;
    slug!: string;
    refID!: string;
    driverUserDetail!: User;
    driver!: string;
    barcodeNo!: string;
    isUsed!: boolean;
    user!: string;
    barCodeCount!: string;
    employeeDetail!: Employees;
    isPrinted: boolean = false;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.isUsed = false;
        this.driverUserDetail = new User();
    }

    static fromResponse(data: any): Barcode {
        const barcode = { ...data };

        return barcode;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        if (this.isNullOrUndefinedAndEmpty(this.refID)) {
            form.controls.refID.setErrors({ invalid: true });
            return false;
        }
        if (this.isNullOrUndefinedAndEmpty(this.barcodeNo)) {
            form.controls.barcodeNo.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
        this.refID = this.trimMe(this.refID);
        this.barcodeNo = this.trimMe(this.barcodeNo);
        return this;
    }
}
