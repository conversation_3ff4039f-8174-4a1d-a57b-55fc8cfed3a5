import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'roleTransform',
    standalone: true,
})
export class RoleTransformPipe implements PipeTransform {
    private readonly roleMap: { [key: string]: string } = {
        ROLE_SUPER_ADMIN: 'Super Admin',
        ROLE_DRIVER: 'Driver',
        ROLE_CUSTOMER: 'Customer',
        ROLE_OFFICE_ADMIN: 'Admin',
    };

    transform(value: string): string {
        if (!value) {
            return value;
        }

        return this.roleMap[value] || value;
    }
}
