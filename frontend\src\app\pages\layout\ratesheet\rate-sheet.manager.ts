import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { RateSheetService } from './rate-sheet.service';

@Injectable({
    providedIn: 'root',
})
export class RateSheetManager extends BaseManager {
    constructor(
        protected rateSheetService: RateSheetService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(rateSheetService, loadingService, toastService);
    }

    stepperList: any[] = [
        {
            stepNumber: 1,
            stepTitle: 'Basic Information',
            stepPath: '/assets/images/icons/information-sign.svg',
            selected: true,
        },
        {
            stepNumber: 2,
            stepTitle: 'Rates',
            stepPath: '/assets/images/svg/Rate-sheet-charges-icon.svg',
            selected: false,
        },
        {
            stepNumber: 3,
            stepTitle: 'Special Request',
            stepPath: '/assets/images/svg/Special-request-icon.svg',
            selected: false,
        },
    ];

    fetchForPickupCityDropdown(param: FilterParam) {
        return this.rateSheetService.fetchForPickupCityDropdown(param).pipe(
            map((response: RestResponse) => {
                return response.data;
            }),
        );
    }

    copyRateSheet(id: string) {
        return this.rateSheetService.getCopiedRateSheet(id).pipe(map((response: RestResponse) => response));
    }
}
