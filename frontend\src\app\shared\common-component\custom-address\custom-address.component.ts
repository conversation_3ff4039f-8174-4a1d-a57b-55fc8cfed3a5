// Angular core imports
import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    QueryList,
    ViewChild,
    ViewChildren,
} from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Loader } from '@googlemaps/js-api-loader';
import { TranslateModule } from '@ngx-translate/core';

// Custom components and models
import { environment } from '../../../../environments/environment';
import { Address } from '../../../models/common/address';
import { LoadingService } from '../../../services/loading.service';
import { NoWhitespaceValidatorDirective } from '../../directives/no-whitespace-validator.directive';
import { AuthService } from '../../services/auth.services';
import { ValidationMessageComponent } from '../validation-message/validation-message.component';

@Component({
    selector: 'app-custom-address',
    standalone: true,
    imports: [TranslateModule, CommonModule, FormsModule, ValidationMessageComponent, NoWhitespaceValidatorDirective],
    templateUrl: './custom-address.component.html',
    styleUrl: './custom-address.component.scss',
})
export class CustomAddressComponent implements OnInit, AfterViewInit, OnDestroy {
    @Input() address!: Address;
    @Input() onClickValidation!: boolean;
    @Input() isRequired!: boolean;
    @Input() types: string[] = ['geocode'];
    @Input() countryRestriction: string[] = ['ca'];
    @Input() addressLabel!: string;
    @Input() cityLabel!: string;
    @Input() provinceLabel!: string;
    @Input() zipCodeLabel!: string;
    @Input() countryLabel!: string;
    @Input() showOnlyAddress: boolean = false;
    @Input() isAddressDisabled!: boolean;
    @Input() isCityDisabled!: boolean;
    @Input() isProvinceDisabled!: boolean;
    @Input() isZipCodeDisabled!: boolean;
    @Input() isCountryDisabled!: boolean;

    @Input() isOnlyGoogleAddress!: boolean;

    @Output() addressChange = new EventEmitter<Address>();
    @Output() addressSelected = new EventEmitter<any>();

    @ViewChild('autocompleteInput') autocompleteInput!: ElementRef;
    @ViewChild('addressForm') addressForm!: NgForm;
    @ViewChildren('addressInput, cityInput, stateInput, zipCodeInput, countryInput')
    inputFields!: QueryList<ElementRef>;

    googleFilledFields = {
        city: false,
        state: false,
        pin: false,
        country: false,
    };

    private autocomplete: google.maps.places.Autocomplete | undefined;
    private loader: Loader;
    private scriptLoaded = false;

    constructor(
        public cdr: ChangeDetectorRef,
        public authService: AuthService,
        protected loadingService: LoadingService,
    ) {
        this.loader = new Loader({
            apiKey: environment.googleMapApiKey,
            version: 'weekly',
            libraries: ['places'],
            language: 'en',
            region: 'CA',
        });
    }

    ngOnInit(): void {
        this.address = this.address ?? new Address();
    }

    ngAfterViewInit(): void {
        try {
            this.loadingService.show();
            this.scriptLoaded = true;
            this.initAutocomplete();
            this.loadingService.hide();
        } catch (error) {
            console.error('Google Maps API failed to load:', error);
        }
    }

    private initAutocomplete(): void {
        try {
            if (!this.autocompleteInput?.nativeElement) {
                throw new Error('Autocomplete input element not found');
            }

            if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
                throw new Error('Google Maps API not properly loaded');
            }

            this.autocomplete = new google.maps.places.Autocomplete(this.autocompleteInput.nativeElement, {
                types: this.types,
                componentRestrictions: { country: this.countryRestriction },
                fields: ['address_components', 'geometry', 'formatted_address', 'place_id'],
            });

            this.autocomplete.addListener('place_changed', () => {
                try {
                    const place = this.autocomplete?.getPlace();
                    this.handleAddressChange(place);
                } catch (e) {
                    console.error('Error handling place selection:', e);
                }
            });
        } catch (error) {
            console.error('Failed to initialize autocomplete:', error);
        }
    }

    onBlurAddress(data: any): void {
        const value = this.autocompleteInput?.nativeElement.value;
        if (value && !this.address.city) {
            this.address.address = data?.target?.value;

            this.addressChange.emit(this.address);
            this.cdr.detectChanges();
        }
    }

    handleAddressChange(place: any): void {
        this.clearAddressField();

        if (!place?.address_components) return;

        this.googleFilledFields = {
            city: false,
            state: false,
            pin: false,
            country: false,
        };

        // Update address object
        this.address.address = place.formatted_address || '';
        this.address.latitude = place.geometry?.location?.lat()?.toString() || '';
        this.address.longitude = place.geometry?.location?.lng()?.toString() || '';
        this.address.placeId = place.place_id || '';

        this.addressSelected.emit(this.address);

        // Process address components
        for (const component of place.address_components) {
            const componentType = component.types[0];

            switch (componentType) {
                case 'postal_code':
                    this.address.pin = component.long_name;
                    this.googleFilledFields.pin = true;
                    break;
                case 'country':
                    this.address.country = component.long_name;
                    this.address.countryCode = component.short_name;
                    this.googleFilledFields.country = true;
                    break;
                case 'locality':
                case 'sublocality':
                    this.address.city = component.short_name;
                    this.googleFilledFields.city = true;
                    break;
                case 'administrative_area_level_1':
                    this.address.state = this.removeSpecialCharacters(component.short_name);
                    this.googleFilledFields.state = true;
                    break;
            }
        }

        this.addressChange.emit(this.address);
        this.cdr.detectChanges();
    }

    clearAddressField(): void {
        this.googleFilledFields = {
            city: false,
            state: false,
            pin: false,
            country: false,
        };

        const address = this.address;
        address.address = '';
        address.city = '';
        address.country = '';
        address.countryCode = '';
        address.latitude = '';
        address.longitude = '';
        address.pin = '';
        address.state = '';
        address.placeId = '';

        this.addressChange.emit(address);
    }

    ngOnDestroy(): void {
        this.closeGoogleAutocompleteDropdown();
        document.removeEventListener('click', this.closePacContainerOnSelect, true);
    }

    closePacContainerOnSelect = (event: Event): void => {
        const target = event.target as HTMLElement;
        if (target.closest('.pac-container')) {
            setTimeout(() => {
                const container = document.querySelector('.pac-container') as HTMLElement;
                if (container) container.style.display = 'none';
            }, 200);
        }
    };

    closeGoogleAutocompleteDropdown(): void {
        const pacContainers = document.querySelectorAll('.pac-container');
        pacContainers.forEach((container) => container.remove());
        if (this.autocomplete) {
            google.maps.event.clearInstanceListeners(this.autocomplete);
        }
    }

    get validForm(): boolean {
        return this.addressForm?.valid || false;
    }

    get invalidControls(): ElementRef[] {
        return this.inputFields?.filter((input) => input.nativeElement.classList.contains('ng-invalid')) || [];
    }

    private removeSpecialCharacters(inputString: string): string {
        return inputString.replace(/[^A-Za-z]+/g, '');
    }
}
