<div class="site-page-container mt-3">
    <div class="site-card">
        <form #shipmentSpecialRequestForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">
                        <!--Label  -->
                        <div class="label-wrap-box mt-2 mb-24">
                            <span>{{"RATE_SHEET.SpecialRequestInfo" | translate}}</span>
                        </div>

                        <!-- Oversize Rate Request -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="oversizeRadio">{{"SHIPMENT.oversize" |
                                    translate}}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oversize" [value]="true"
                                                [(ngModel)]="quotation.isOversize" id="oversizeYes"
                                                (change)="onRadioButtonChange(quotation.isOversize,'oversizeRate')" />
                                            <label class="form-check-label" for="oversizeYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oversize" [value]="false"
                                                [(ngModel)]="quotation.isOversize" id="oversizeNo"
                                                (change)="onRadioButtonChange(quotation.isOversize,'oversizeRate')" />
                                            <label class="form-check-label" for="oversizeNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #OversizeRate="ngModel"
                                            appCurrencyFormatter [maxlength]="15" [(ngModel)]="quotation.oversizeRate"
                                            placeholder="" required="required" name="oversizeRate"
                                            [disabled]="!quotation.isOversize"
                                            [ngClass]="{'is-invalid': !OversizeRate.valid && quotation.isOversize && onClickValidation}"
                                            [required]="quotation.isOversize"
                                            placeholder="'RATE_SHEET.oversizeRate | translate'">

                                        <label for="oversizeRate">{{
                                            "RATE_SHEET.oversizeRate" | translate
                                            }} </label>

                                        <app-validation-message [field]="OversizeRate" *ngIf="quotation.isOversize"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Enclosed -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="enclosedRadio">{{"Quotation.isEnclosed" |
                                    translate}}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enclosed" [value]="true"
                                                id="enclosedYes" [(ngModel)]="quotation.isEnclosed"
                                                (change)="onRadioButtonChange(quotation.isEnclosed,'enclosedRate')" />
                                            <label class="form-check-label" for="enclosedYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="enclosed" [value]="false"
                                                id="enclosedNo" [(ngModel)]="quotation.isEnclosed"
                                                (change)="onRadioButtonChange(quotation.isEnclosed,'enclosedRate')" />
                                            <label class="form-check-label" for="enclosedNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #EnclosedRate="ngModel"
                                            appCurrencyFormatter [(ngModel)]="quotation.enclosedRate"
                                            name="enclosedRate" [maxlength]="15" [disabled]="!quotation.isEnclosed"
                                            placeholder="'Quotation.enclosedRate' | translate'"
                                            [ngClass]="{'is-invalid': !EnclosedRate.valid && quotation.isEnclosed && onClickValidation}"
                                            [required]="quotation.isEnclosed">
                                        <label for="enclosedRate">
                                            {{ "RATE_SHEET.enclosedRate" | translate }}
                                        </label>

                                        <app-validation-message [field]="EnclosedRate" *ngIf="quotation.isEnclosed"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Perishable -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="PerishableRadio">{{ "Quotation.isPerishable" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="perishableRadio"
                                                [value]="true" [(ngModel)]="quotation.isPerishable"
                                                id="PerishableRadioYes"
                                                (change)="onRadioButtonChange(quotation.isPerishable,'perishableRate')" />
                                            <label class="form-check-label" for="PerishableRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="perishableRadio"
                                                [value]="false" [(ngModel)]="quotation.isPerishable"
                                                id="PerishableRadioNo"
                                                (change)="onRadioButtonChange(quotation.isPerishable,'perishableRate')" />
                                            <label class="form-check-label" for="PerishableRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #PerishableRate="ngModel"
                                            appCurrencyFormatter [(ngModel)]="quotation.perishableRate" placeholder=""
                                            name="perishableRate" [maxlength]="15" [disabled]="!quotation.isPerishable"
                                            [ngClass]="{'is-invalid': !PerishableRate.valid && quotation.isPerishable && onClickValidation}"
                                            [required]="quotation.isPerishable">
                                        <label for="perishableRate">{{
                                            "RATE_SHEET.perishableRate" | translate
                                            }}</label>

                                        <app-validation-message [field]="PerishableRate" *ngIf="quotation.isPerishable"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Rush Rate Request -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="rushRequestRate">{{
                                    "SHIPMENT.RushedRateImmediately" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="rushRequestRate"
                                                [value]="true" id="rushRequestRateYes"
                                                [(ngModel)]="quotation.isRushRequest" id="rushRequestRateYes"
                                                (change)="onRadioButtonChange(quotation.isRushRequest,'rushRequestRate')" />
                                            <label class="form-check-label" for="rushRequestRateYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="rushRequestRate"
                                                [value]="false" [(ngModel)]="quotation.isRushRequest"
                                                id="rushRequestRateNo"
                                                (change)="onRadioButtonChange(quotation.isRushRequest,'rushRequestRate')" />
                                            <label class="form-check-label" for="rushRequestRateNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #RateRushRequest="ngModel"
                                            [(ngModel)]="quotation.rushRequestRate" placeholder=""
                                            name="rateRushRequest" appCurrencyFormatter [maxlength]="15"
                                            placeholder="'SHIPMENT.RushedRateImmediately | translate'"
                                            [disabled]="!quotation.isRushRequest"
                                            [ngClass]="{'is-invalid': !RateRushRequest.valid && quotation.isRushRequest && onClickValidation}"
                                            [required]="quotation.isRushRequest">
                                        <label for="floatingInputValue">
                                            {{ "SHIPMENT.RushedRateImmediately" | translate }} Rate
                                        </label>

                                        <app-validation-message [field]="RateRushRequest"
                                            *ngIf="quotation.isRushRequest"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Fragile Rate -->
                        <div class="col-md-6 position-relative px-0 ps-md-0 pe-md-2 ">
                            <div class="special-request-button mb-26">
                                <label class="radio-button-custom" for="fragileRadio"> {{ "Quotation.isFragile" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fragileRadio"
                                                [value]="true" name="fragile" [(ngModel)]="quotation.isFragile"
                                                id="fragileRadioYes"
                                                (change)="onRadioButtonChange(quotation.isFragile,'fragileRate')" />
                                            <label class="form-check-label" for="fragileRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="fragileRadio"
                                                (change)="onRadioButtonChange(quotation.isFragile,'fragileRate')"
                                                id="fragileRadioNo" [value]="false" name="secured"
                                                [(ngModel)]="quotation.isFragile" />
                                            <label class="form-check-label" for="fragileRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>
                                    <span class="form-floating">
                                        <input type="text" class="form-control" #FragileRate="ngModel"
                                            [(ngModel)]="quotation.fragileRate" placeholder="" appCurrencyFormatter
                                            name="fragileRate" [maxlength]="15" [disabled]="!quotation.isFragile"
                                            placeholder="'Quotation.fragileRate | translate'"
                                            [ngClass]="{'is-invalid': !FragileRate.valid && quotation.isFragile && onClickValidation}"
                                            [required]="quotation.isFragile">
                                        <label for="fragileRate">{{
                                            "Quotation.isFragile" | translate
                                            }} Rate</label>

                                        <app-validation-message [field]="FragileRate" *ngIf="quotation.isFragile"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Dangerous Goods -->
                        <div class="col-md-6 position-relative px-0 pe-md-0 ps-md-2  ">
                            <div class="mb-26 special-request-button">
                                <label class="radio-button-custom" for="DangerousGoodsRadio">{{
                                    "Quotation.isDangerousGoods" |
                                    translate }}</label>
                                <div class="input-field-radio">
                                    <div class="radio-buttons">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="dangerousGoodsRadio"
                                                [value]="true" id="dangerousGoodsRadioYes"
                                                [(ngModel)]="quotation.isDangerousGoods" id="DangerousGoodsRadioYes"
                                                (change)="onRadioButtonChange(quotation.isDangerousGoods,'dangerousGoodsRate')" />
                                            <label class="form-check-label" for="DangerousGoodsRadioYes">{{"USERS.YES" |
                                                translate}}</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="dangerousGoodsRadio"
                                                [value]="false" [(ngModel)]="quotation.isDangerousGoods"
                                                id="DangerousGoodsRadioNo"
                                                (change)="onRadioButtonChange(quotation.isDangerousGoods,'dangerousGoodsRate')" />
                                            <label class="form-check-label" for="DangerousGoodsRadioNo">{{"USERS.NO" |
                                                translate}}</label>
                                        </div>
                                    </div>

                                    <div class="form-floating">
                                        <input type="text" class="form-control" #DangerousGoodsRate="ngModel"
                                            [(ngModel)]="quotation.dangerousGoodsRate" placeholder=""
                                            placeholder="'RATE_SHEET.dangerousGoodsRate| translate'"
                                            [disabled]="!quotation.isDangerousGoods" name="dangerousGoodsRate"
                                            appCurrencyFormatter [maxlength]="15"
                                            [ngClass]="{'is-invalid': !DangerousGoodsRate.valid && quotation.isDangerousGoods && onClickValidation}"
                                            [required]="quotation.isDangerousGoods">
                                        <label for="dangerousGoodsRate">
                                            {{ "RATE_SHEET.dangerousGoodsRate" | translate }}
                                        </label>

                                        <app-validation-message [field]="DangerousGoodsRate"
                                            *ngIf="quotation.isDangerousGoods"
                                            [onClickValidation]="onClickValidation"></app-validation-message>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                [routerLink]="['/dashboard/shipments']">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(shipmentSpecialRequestForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onNext(shipmentSpecialRequestForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>