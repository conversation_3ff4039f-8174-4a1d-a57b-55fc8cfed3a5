import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class StringUtilService {
    maritalData: string[] = ['Single', 'Married', 'Divorced', 'Separated', 'Widowed'];
    roleNames: string[] = [
        'Back Office',
        'Account Manager',
        'Collection Person',
        'Sales Head',
        'Sales Manager',
        'Sales Person',
        'Marketing Manager',
        'Tele Market Supervisor',
        'Tele Market Executive',
        'Human Resource',
    ];

    file: { path: string } | null = null; // Example file object, adjust as needed

    // List of allowed image extensions
    private imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    private pdfExtensions = ['pdf'];
    private docExtensions = ['doc', 'docx'];
    private excelExtensions = ['xls', 'xlsx'];

    convertRoleToString(role: string): string {
        // Remove the "ROLE_" prefix if it exists
        let updatedRole = role.replace(/^ROLE_/, '');

        // Remove underscores and convert the rest of the string to a readable format
        updatedRole = updatedRole
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');

        return updatedRole;
    }

    convertModuleToString(role: string): string {
        // Remove the "ROLE_" prefix if it exists
        let updatedRole = role.replace(/^ROLE_/, '');
        updatedRole = updatedRole.replace(/^_/, '');

        // Remove underscores and convert the rest of the string to a readable format
        updatedRole = updatedRole
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');

        return updatedRole;
    }
    convertRolesToString(role: string): string {
        // Remove the "ROLE_" prefix if it exists
        let updatedRole = role.replace(/^_/, '');

        // Remove underscores and convert the rest of the string to a readable format
        updatedRole = updatedRole
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');

        return updatedRole;
    }

    getProfilePicUrl(profilePicUrl: string | null | undefined): string {
        const defaultUrl = '/assets/images/avtar-user.png';
        return profilePicUrl ? profilePicUrl : defaultUrl;
    }

    formatDate(dateString: string | Date): string {
        if (dateString == '') {
            return '';
        }
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours() % 12;
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const ampm = date.getHours() >= 12 ? 'PM' : 'AM';

        return `${day}/${month}/${year} ${hours}:${minutes} ${ampm}`;
    }

    static formatCalDate(dateString: string, daysToSubtract: number = 0): string {
        let date: Date;
        if (!dateString) {
            date = new Date();
            date.setDate(date.getDate() - daysToSubtract);
        } else {
            date = new Date(dateString);
        }
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${year}-${month}-${day}`;
    }

    getRoleButtonClass(role: string): string {
        const roleButtonClasses: { [key: string]: string } = {
            ROLE_ADMIN: 'btn-primary',
            ROLE_BACK_OFFICE: 'btn-secondary',
            ROLE_ACCOUNT_MANAGER: 'btn-success',
            ROLE_COLLECTION_PERSON: 'btn-danger',
            ROLE_SALES_HEAD: 'btn-warning',
            ROLE_SALES_MANAGER: 'btn-info',
            ROLE_VENUE_MANAGER: 'btn-light',
            ROLE_SALES_PERSON: 'btn-dark',
            ROLE_MARKETING_MANAGER: 'btn-light-green',
            ROLE_TELE_MARKET_SUPERVISOR: 'btn-dark-blue',
            ROLE_TELE_MARKET_EXECUTIVE: 'btn-wheat',
            ROLE_CUSTOMER: 'btn-orchid',
            ROLE_HUMAN_RESOURCE: 'btn-bronze',
        };

        return roleButtonClasses[role] || 'btn-secondary'; // Default class
    }

    getMaritalDataOptions() {
        return this.maritalData.map((status) => ({
            value: status.toUpperCase(),
            display: this.toTitleCase(status),
        }));
    }

    getRoleOptions() {
        return this.roleNames.map((role) => ({
            id: `ROLE_${role.toUpperCase().replace(/ /g, '_')}`,
            name: this.toTitleCase(role),
        }));
    }

    private toTitleCase(str: string): string {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    get18YearBeforeDate() {
        const currentDate = new Date();
        const eighteenYearsAgo = new Date(
            currentDate.getFullYear() - 18,
            currentDate.getMonth(),
            currentDate.getDate(),
        );
        const date = eighteenYearsAgo.toISOString().split('T')[0];
        return date;
    }

    // Assuming user given date  in 'yyyy-MM-dd' format
    calculateAdjustedDate(dob: string | undefined, daysToAdd: number = 0): string {
        let dobDate: Date;
        if (!dob) {
            dobDate = new Date();
        } else {
            dobDate = new Date(dob);
        }
        dobDate.setDate(dobDate.getDate() + daysToAdd);
        return dobDate.toISOString().split('T')[0];
    }

    isImage(filePath: string): boolean {
        const extension = filePath?.split('.').pop()?.toLowerCase();
        return extension ? this.imageExtensions.includes(extension) : false;
    }

    isPdf(filePath: string): boolean {
        const extension = filePath?.split('.').pop()?.toLowerCase();
        return extension ? this.pdfExtensions.includes(extension) : false;
    }

    isDoc(filePath: string): boolean {
        const extension = filePath?.split('.').pop()?.toLowerCase();
        return extension ? this.docExtensions.includes(extension) : false;
    }

    isExcel(filePath: string): boolean {
        const extension = filePath?.split('.').pop()?.toLowerCase();
        return extension ? this.excelExtensions.includes(extension) : false;
    }

    getImageSrc(filePath: string): string {
        return this.getAttachmentIcon(filePath);
    }

    getAttachmentIcon(filePath: string): string {
        return this.isImage(filePath)
            ? filePath
            : this.isPdf(filePath)
              ? '/assets/images/logo-icon.png'
              : this.isDoc(filePath)
                ? '/assets/images/logo-icon.png'
                : this.isExcel(filePath)
                  ? '/assets/images/logo-icon.png'
                  : '/assets/images/logo-icon.png'; // Default image if not an image
    }

    getAttachmentType(filePath: string): string {
        return this.isImage(filePath)
            ? 'IMAGE'
            : this.isPdf(filePath)
              ? 'PDF'
              : this.isDoc(filePath)
                ? 'DOCUMENT'
                : this.isExcel(filePath)
                  ? 'EXCEL'
                  : 'OTHER';
    }

    getUniqueModule() {}
}
