// box-shadow
@mixin box-shadow($x, $y, $blur, $color) {
    box-shadow: $x $y $blur $color;
}

// flexbox-center
@mixin flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

// flexbox-column-center
@mixin flex-column-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

//align -column-center
@mixin flex-column-align-center {
    display: flex;
    flex-direction: column;
    align-items: center;
}

@mixin flex-column-align-space-between {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

// flexbox-column-center-vertical
@mixin flex-column-center-vertical {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

@mixin flex-space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@mixin flex-space-between-start {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

@mixin d-flex-align-center {
    display: flex;
    align-items: center;
}

@mixin flex-space-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

// text ellipsis
@mixin text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

// ellipsis based on line
@mixin text-ellipsis-line {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    width: fit-content;
}

@mixin common-box-shadow {
    box-shadow: 0px 3px 6px #00000029;
}

// transition property
@mixin transition($property, $duration, $timing-function: ease-in-out) {
    transition: $property $duration $timing-function;
}

// Common button mixin
@mixin button-style($height, $font-size: 20px, $width: 100%) {
    border-radius: 12px;
    min-height: $height;
    max-height: $height;
    padding: 12px 24px;
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: $black-color !important;
    font-weight: $font-weight-500;
    min-width: $width !important;

    @include flex-center;

    i {
        font-size: $font-size;
    }

    &:focus,
    &:active,
    &:hover {
        outline: none !important;
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: $black-color !important;
    }
}