.wrapper {
    display: flex;
    width: 100%;

    &:has(.auth-layout) {
        overflow: auto;
    }

    #sidebar {
        overflow-y: auto;
        overflow-x: hidden;
        top: 0;
        left: 0;
        width: 250px;
        z-index: 50;
        height: 100vh;
        color: $white-color;
        position: fixed;
        min-height: 100%;
        transition: all 0.3s;
        background-color: #2f3349;
        box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);

        .category-line {
            color: $white-color;
            font-size: 0.7rem;
            font-weight: $font-weight-600;
            letter-spacing: 0.065rem;
            text-transform: uppercase;
            padding: 0.75rem 1.65rem;
            white-space: nowrap;
            opacity: 0.5;
            position: relative;

            span {
                position: relative;
                background-color: #2f3349;
                z-index: 2;
            }

            &::after {
                content: '';
                display: block;
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                height: 2px;
                background-color: #9e9e9e;
                transform: translateY(-50%);
            }
        }

        .sidebar-header {
            margin-top: 20px;

            .logo {
                margin-bottom: 30px;
                margin-top: 30px;

                .logo-img {
                    @include flex-column-center;
                }
            }
        }

        @media (min-width: 992px) {
            &:hover {
                width: 250px;
                transition: all 0.3s;
            }

            @include flex-column-align-center;
            margin-bottom: 25px;
        }

        &.final-active {
            width: 0px;
            margin-left: 0px;
            height: 100vh;
            transition: all 0.3s;
        }

        ul.components {
            width: 100% !important;
            padding: 20px 0 0;
            overflow-y: auto;
            overflow-x: hidden;

            li {
                min-height: 45px;
                padding: 0 15px;

                .sidebar-item {
                    padding: 10px;
                    font-size: 13px;
                    cursor: pointer;
                    text-decoration: none;
                    color: $white-color;
                    font-weight: $font-weight-500;
                    width: 100%;
                    border-radius: 6px;
                    transition: ease 0.3s ease;
                    @include d-flex-align-center;

                    .icon-container {
                        border-radius: 4px;
                        margin-right: 10px;
                        @include flex-center;

                        .sidebar-icon {
                            .sidebar-svg {
                                width: 20px;
                                height: 20px;
                                filter: brightness(0) invert(1); // white icons
                            }
                        }

                        .sidebar-content-title {
                            width: 250px;
                            transition:
                                margin-left 0.7s ease-in-out,
                                opacity 0.3s ease-in-out;
                            opacity: 1;
                        }

                        .sidebar-content-close {
                            margin-left: 60px;
                            transition:
                                margin-left 0.7s ease-in-out,
                                opacity 0.3s ease-in-out;
                            opacity: 0;
                        }
                    }
                }

                // active/highlight styles only
                &.active-menu-item,
                &.active-menu-item:hover,
                &.active-menu-item:active,
                &.active-menu-item:focus {
                    .sidebar-item {
                        background-color: var(--primary-color);
                        color: $black-color;

                        .setting-menu-container {
                            display: flex;
                        }

                        .icon-container {
                            .sidebar-icon {
                                .sidebar-svg {
                                    filter: unset;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    #content {
        width: calc(100% - 250px);
        min-height: 100vh;
        transition: all 0.3s;
        position: absolute;
        top: 0;
        right: 0;
        background: $white-color;

        .menu-toggle-icon {
            font-size: 2em;
            float: left;
            cursor: $cursor-pointer;
        }

        &.active {
            width: 100%;
        }

        &.not-active {
            right: 0px;
            left: 0px;
            width: 100%;
        }

        .page-info-top-bar {
            z-index: 999;
            background: $white-color;
            box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);
            position: sticky;
            left: 0;
            right: 0;
            top: 0;
            justify-content: space-between;
            padding: 10px 20px;
            text-align: right;
            max-height: 100px;
            @include d-flex-align-center;

            .page-name-container {
                text-align: left;
                display: flex;
                flex-direction: column;
                margin-left: 20px;
                margin-bottom: 2px;

                .page-heading-name {
                    font-size: 24px;
                    line-height: 30px;
                    max-width: calc(100% - 0px);
                    @include text-ellipsis;
                }

                h3,
                .breadcrumb {
                    margin-bottom: 0px;
                }

                h3 {
                    font-size: 28px;
                    line-height: 40px;
                    font-weight: $font-weight-600;
                }

                .breadcrumb {
                    li {
                        a {
                            font-size: 13px;
                            color: #7f8fa4 !important;
                            font-weight: 400;
                            letter-spacing: 0;
                            font-style: normal;
                            line-height: normal;
                        }

                        &.active {
                            a {
                                color: #333 !important;
                            }
                        }
                    }
                }
            }

            .page-user-info-container {
                .user-image-logo {
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 45px;
                    background: transparent url('/assets/images/icons/user-logo-background.png') 0% 0%;
                    background-size: 100% 100%;
                    font-size: 18px;
                    font-weight: $font-weight-600;
                    color: var(--primary-color);
                }

                .user-info-logout-container {
                    display: flex;
                    flex-direction: column;
                    text-align: left;
                    margin-left: 12px;

                    .user-info-name {
                        font-size: 18px;
                        line-height: 24px;
                        color: var(--primary-color);
                        font-weight: $font-weight-600;
                    }
                }
            }
        }

        .site-dashboard-container {
            padding: 24px 36px;
        }

        .main-site-container {
            padding: 18px 24px 12px;

            .edit-button-container {
                background: var(--primary-gradient-color);
            }

            .action-button-container {
                height: 40px;
                width: 40px;
                margin: 0 4px;
                border-radius: 12px !important;

                .action-button {
                    margin-right: 5px !important;
                    border-radius: 10px !important;
                }
            }

            .action-button.btn-primary {
                color: $white-color;
                background-color: #2a2a2a !important;
                border: 1px solid #2a2a2a !important;
            }

            .action-button i {
                color: $white-color !important;
            }

            .dashboard-content-container {
                min-height: 600px;
                background-color: $white-color;
                position: relative;
                margin: auto;

                &.section-edit-form {
                    min-height: auto;
                }
            }
        }
    }
}

.profile-container {
    position: relative;
    display: inline-block;

    .profile-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        @include flex-center;
        font-size: 1.5rem;
        font-weight: $font-weight-600;
        cursor: $cursor-pointer;
        position: relative;
        color: #ffc107;
        border: 2px solid var(--primary-color);
        background: #fff8d2;

        &::after {
            display: none;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            background: #28a745;
            border: 2px solid $white-color;
            border-radius: 50%;
            position: absolute;
            bottom: 0;
            right: 0;
        }
    }

    .dropdown-menu {
        width: 220px;
        border-radius: 8px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        padding: 8px;

        &.export-button-design {
            width: 80px;
        }

        .user-logo-image {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            text-align: center;
            line-height: 45px;
            font-size: 18px;
            font-weight: $font-weight-600;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            background: #fff8d2;
        }

        .menu-icon-size {
            font-size: 20px;
        }

        .user-name-text {
            text-transform: capitalize;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 110px;
            font-size: $font-size-14;
        }

        .dropdown-item {
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 5px 10px;
            font-size: $font-size-14;
            transition: background 0.3s ease;

            i {
                margin-right: 10px;
            }

            &:hover {
                background: var(--primary-color);
            }

            &:active {
                background-color: var(--primary-color);
                color: $black-color;
            }
        }

        .logout-btn {
            width: 100%;
            background: #ff4d4f;
            color: $white-color;
            border: none;
            padding: 10px;
            font-size: $font-size-14;
            text-align: center;
            border-radius: 6px;
            cursor: $cursor-pointer;
            transition: background 0.3s ease;

            &:hover {
                background: #e43d3d;
            }
        }
    }
}

@media screen and (max-width: 991px) {
    .wrapper {
        #sidebar {
            width: 0;
            margin-left: 0;
            transition: width 0.5s ease;

            &.final-active {
                width: 250px;
                left: 0;
                z-index: 9;
                transition: width 0.5s ease;
            }
        }

        #content {
            width: 100%;
            left: 0;
            transition: width 0.5s ease;

            &.active {
                left: 0;
                right: 0;
                width: 100%;
                transition: width 0.5s ease;
            }

            .page-info-top-bar {
                .page-name-container {
                    text-align: left;

                    h3 {
                        font-size: 26px !important;
                    }
                }
            }

            .site-customer-main-container {
                padding: 20px 25px;

                .dashboard-content-container {
                    &.section-edit-form {
                        .no-padding-left {
                            padding-right: 0px !important;
                        }

                        .no-padding-right {
                            padding-left: 0px !important;
                        }
                    }
                }
            }
        }

        .sidebarCollapse span {
            display: none;
        }
    }
}

@media screen and (max-width: 480px) {
    .wrapper {
        #content {
            &.mobile-content-body {
                width: 100%;
            }

            .site-customer-main-container {
                padding: 15px 10px;

                .dashboard-content-container {
                    padding: 20px 10px;
                }

                .padding-0 {
                    padding: 0px !important;
                }
            }

            .page-info-top-bar {
                padding: 10px;
                padding-bottom: 10px;

                .toggle-menu-icon {
                    cursor: pointer;
                }

                .menu-toggled {
                    max-width: 30px;
                }

                .page-name-container {
                    text-align: left;

                    h3 {
                        font-size: 20px !important;
                        line-height: 30px;
                        font-weight: $font-weight-600;
                    }

                    .breadcrumb {
                        margin-top: -5px !important;

                        .breadcrumb-item {
                            a {
                                font-size: 13px;
                            }

                            &::before {
                                height: 12px;
                                overflow: hidden;
                                display: inline;
                            }
                        }
                    }
                }
            }
        }
    }
}

.sidebar-layout-container {
    .sidebar-accordion {
        .active-menu-item {
            .sidebar-item {
                display: flex;
                justify-content: space-between;

                .setting-menu-container {
                    display: flex;
                    align-items: center;
                    width: 100%;

                    .sidebar-content-title {
                        width: auto !important;
                    }
                }

                i {
                    font-size: 18px;
                }
            }
        }

        .submenu-container {
            overflow: hidden;

            .submenu-list {
                padding: 10px 0;
                background: #32374f;
                border-top: 1px solid #3e4563;
                border-bottom: 1px solid #3e4563;
            }
        }
    }
}

//---------Dashboard Page ---------/
.card-dashboard-container {
    background-image: url(/assets/images/card-bg.png);
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;

    .card-body {
        .icon-shape {
            align-items: center;
            display: inline-flex;
            justify-content: center;
            text-align: center;
            vertical-align: middle;
            height: 3rem;
            line-height: 3rem;
            width: 3rem;

            i {
                font-size: 24px;
            }

            &.bg-primary-alert {
                color: #845adf !important;
                background-color: #f2eefc !important;
            }
        }

        .card-info {
            h4 {
                font-size: 1rem;
                font-weight: $font-weight-700;
                color: #1e293b;
            }

            p {
                font-size: $font-size-16;
                font-weight: $font-weight-500;
                color: #666;
                margin-top: 2px;
            }
        }

        .text-inherit {
            font-size: 20px;
        }
    }

    &.active,
    &:focus,
    &:hover {
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        transition: 1s ease;
    }

    &.bg-primary-card {
        border-top-color: #845adf !important;

        .card-body {
            .icon-shape {
                i {
                    font-size: 24px;
                }

                &.bg-primary-alert {
                    color: #845adf !important;
                    background-color: #f2eefc !important;
                }
            }
        }
    }

    &.bg-danger-card {
        border-top-color: #e43f52 !important;

        .card-body {
            .icon-shape {
                i {
                    font-size: 24px;
                }

                &.bg-danger-alert {
                    color: #e43f52 !important;
                    background-color: #e43f521a !important;
                }
            }
        }
    }

    &.bg-success-card {
        border-top-color: #26bf94 !important;

        .card-body {
            .icon-shape {
                i {
                    font-size: 24px;
                }

                &.bg-success-alert {
                    color: #26bf94 !important;
                    background-color: rgb(38 191 148 / 10%) !important;
                }
            }
        }
    }

    &.bg-warning-card {
        border-top-color: #ffe69c !important;

        .card-body {
            .icon-shape {
                i {
                    font-size: 24px;
                }

                &.bg-warning-alert {
                    color: #ffc004 !important;
                    background-color: #fff3cd !important;
                }
            }
        }
    }
}