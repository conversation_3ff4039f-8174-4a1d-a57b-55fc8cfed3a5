{"workbench.settings.useSplitJSON": false, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "typescript", "html"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "typescript.preferences.importModuleSpecifier": "non-relative", "eslint.alwaysShowStatus": true, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[scss]": {"editor.defaultFormatter": "vscode.css-language-features"}, "cSpell.words": ["intial"], "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}}