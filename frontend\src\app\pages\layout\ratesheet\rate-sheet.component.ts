// Angular Core Modules
import { CommonModule } from '@angular/common';
import { Compo<PERSON>, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

// Third-party Modules
import { NgbAccordionModule, NgbPopoverModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';

// Application Components, Directives, Pipes and Services
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { LoadingService } from '../../../services/loading.service';
import { NgCustomDateRangePickerComponent } from '../../../shared/common-component/ng-custom-date-range-picker/ng-custom-date-range-picker.component';
import { TooltipEllipsisDirective } from '../../../shared/directives/tooltip-ellipsis.directive';
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';
import { TypeAheadFilterService, TypeaheadSearchConfig } from '../../../shared/services/typeahead.service';

// Application Configurations and Models
import { Constant } from '../../../config/constants';
import { RateSheet } from '../../../models/rate-sheet';

// Feature-specific Manager
import { FormsModule } from '@angular/forms';
import { catchError, map, Subject, throwError } from 'rxjs';
import { FilterParam } from '../../../models/common/filter-param';
import { CommonUtil } from '../../../shared/common.util';
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { RateSheetManager } from './rate-sheet.manager';

@Component({
    selector: 'app-rate-sheet',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        RouterLink,
        DataTablesModule,
        DelayedInputDirective,
        NgbAccordionModule,
        NgbTooltipModule,
        NgSelectModule,
        TranslateModule,
        DateFormatPipe,
        NgCustomDateRangePickerComponent,
        TooltipEllipsisDirective,
        NgbPopoverModule,
    ],
    templateUrl: './rate-sheet.component.html',
    styleUrls: ['./rate-sheet.component.scss'],
})
export class RateSheetComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
    @ViewChild('action') action!: TemplateRef<any>;
    @ViewChild('createdOn') createdOn!: TemplateRef<string>;
    @ViewChild('deliveryAddress') deliveryAddress!: TemplateRef<string>;
    @ViewChild('endDate') endDate!: TemplateRef<string>;
    @ViewChild('name') name!: TemplateRef<string>;
    @ViewChild('pickupAddress') pickupAddress!: TemplateRef<string>;
    @ViewChild('startDate') startDate!: TemplateRef<string>;

    rateSheets!: Array<RateSheet>;
    durationFilter = Constant.DURATION_FILTERS;
    resourceType: string = Constant.RESOURCE_TYPE.RATE_SHEET;

    searchPickupCitySubject: Subject<string> = new Subject<string>();
    searchDeliveryCitySubject: Subject<string> = new Subject<string>();

    loadingPickupCityNgSelect!: boolean;
    loadingDeliveryCityNgSelect!: boolean;

    pickupCities: any[] = [];
    deliveryCities: any[] = [];

    searchConfigs: TypeaheadSearchConfig<any>[] = [
        {
            subject: this.searchPickupCitySubject,
            fetchFunction: this.rateSheetManager.fetchForPickupCityDropdown.bind(this.rateSheetManager),
            updateResults: (results: any[]) => (this.pickupCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingPickupCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.pickupCities.find(city => city.id === this.filterParam.filtering.pickUpCity)
        },
        {
            subject: this.searchDeliveryCitySubject,
            fetchFunction: this.rateSheetManager.fetchForPickupCityDropdown.bind(this.rateSheetManager),
            updateResults: (results: any[]) => (this.deliveryCities = results),
            updateLoading: (isLoading: boolean) => (this.loadingDeliveryCityNgSelect = isLoading),
            selectedItemGetter: () =>
                this.deliveryCities.find(city => city.id === this.filterParam.filtering.deliveryCity)
        }
    ];


    constructor(
        protected rateSheetManager: RateSheetManager,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected override commonService: CommonService,
        protected route: ActivatedRoute,
        protected override router: Router,
        protected typeAheadService: TypeAheadFilterService,
    ) {
        super(rateSheetManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.setupAllSearches();

        this.rateSheets = new Array<RateSheet>();

        this.filterParam.fileName = this.resourceType;

        this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.RATE_SHEET_LIST;
        this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.RATE_SHEET_LIST;

        // --- Bind navigation state params to filter ---
        if (history.state.Filtering) {
            this.filterParam.filtering.createdWithinDays = history.state.Filtering;
        }

        this.init();
    }

    ngAfterViewInit() {
        const templates = {
            name: this.name,
            pickupAddress: this.pickupAddress,
            deliveryAddress: this.deliveryAddress,
            startDate: this.startDate,
            endDate: this.endDate,
            createdOn: this.createdOn,
            action: this.action,
        };

        this.setupColumns(templates);
    }

    override onFetchCompleted() {
        this.rateSheets = this.records.map((data) => RateSheet.fromResponse(data));
        super.onFetchCompleted();
    }

    rateSheetWeightChargeExcel(data: any) {
        const weightChargeFilterParam = new FilterParam();
        weightChargeFilterParam.filtering.rateSheetId = data.id;
        weightChargeFilterParam.fileName = `${data.name}_${CommonUtil.setTodayDate()}`;
        this.exportToExcel(weightChargeFilterParam, '/api/ratesheet/ratesheetweightcharge/export/to/excel');
    }

    async copyRateSheet(rateSheet: any) {
        this.commonService.confirmation(
            `Would you like to copy this ${rateSheet.name} RateSheet ?`,
            this.copyRateCardCallback.bind(this),
            rateSheet.id,
        );
    }

    copyRateCardCallback(data: any): void {
        this.loadingService.show();

        this.rateSheetManager
            .copyRateSheet(data)
            .pipe(
                map((response) => {
                    if (response.data) {
                        this.toastService.success(response?.message);
                        this.loadingService.hide();
                        this.router.navigate([`/dashboard/rate-sheet/edit/${response.data}`]);
                        this.refreshRecord();
                    } else {
                        this.toastService.error(response?.message);
                        this.loadingService.hide();
                    }

                    return response;
                }),
                catchError((error) => {
                    this.toastService.error(error.message);
                    this.loadingService.hide();
                    return throwError(() => error);
                }),
            )
            .subscribe();
    }

    // start of typeahead Search
    setupAllSearches() {
        const typeHeadFilter = new FilterParam();

        this.searchConfigs.forEach(config => {
            this.typeAheadService.setupSearchSubscription<any>(
                config.subject,
                typeHeadFilter,
                config.fetchFunction,
                config.updateResults,
                config.updateLoading,
                config.selectedItemGetter,
                config.endpoint
            );
        });
    }
    // end of typeahead search
}
