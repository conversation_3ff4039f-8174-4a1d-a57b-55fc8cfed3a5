<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar"
                    placeholder="Search by Barcode No. or Ref ID..." appDelayedInput (delayedInput)="search($event)"
                    [delayTime]="1000" #searchInput>
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">

                <button type="button" class="btn btn-primary export-button d-flex justify-content-center"
                    appRippleEffect (click)="openModal(content,'Mark Barcodes Printed', true)"
                    ngbTooltip="Update Barcode Print Status">
                    <span>
                        <i class="bi bi-arrow-repeat me-1 font-size-28"></i>
                    </span>
                </button>

                <!-- Filter Button -->
                <button type="button" class="btn btn-primary export-button" appRippleEffect
                    (click)="accordion.toggle('filter');" ngbTooltip="Filter">
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>
                </button>

                <div ngbDropdown class="profile-container" #dropdown="ngbDropdown"
                    *ngIf="barcodes && barcodes.length > 0">
                    <button ngbDropdownToggle class="btn btn-primary export-button" appRippleEffect ngbTooltip="Export">
                        <span>
                            <i class="bi bi-upload me-1 font-size-20"></i>
                        </span>
                    </button>

                    <div ngbDropdownMenu class="dropdown-menu export-button-design">
                        <div class="dropdown-item dropdown-clickable-item rounded-2 cursor-pointer fw-500"
                            appRippleEffect (click)="generateDocument('CSV')">
                            <i class="bi bi-file-earmark-spreadsheet menu-icon-size"></i> {{ "COMMON.CSV" |
                            translate }}
                        </div>

                        <div class="dropdown-item dropdown-clickable-item rounded-2 cursor-pointer fw-500"
                            appRippleEffect (click)="generateDocument('PDF')">
                            <i class="bi bi-filetype-pdf menu-icon-size"></i> {{ "COMMON.PDF" | translate }}
                        </div>
                    </div>
                </div>

                <!-- Add New Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    (click)="openModal(content,'Add Barcodes Count', false)">
                    <span class="text-center">
                        <img src="/assets/images/icons/Add_icon.svg" alt="Add-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"COMMON.ADDNEW" |
                        translate}}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
        <div ngbAccordion #accordion="ngbAccordion" class="mt-2">
            <div ngbAccordionItem="filter" class="border-0">
                <div ngbAccordionCollapse>
                    <div ngbAccordionBody class="filter-container p-4 w-100">
                        <ng-template>
                            <div class="row gx-0 gy-3">

                                <!-- Driver -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="fullName" bindValue="id" [items]="drivers"
                                            [(ngModel)]="filterParam.filtering.driverId" #Driver="ngModel" name="driver"
                                            [typeahead]="searchDriverSubject" [loading]="loadingDriverNgSelect">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "BARCODE.driver" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                            [items]="statusOptions" [(ngModel)]="filterParam.filtering.status"
                                            #StatusFilter="ngModel">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "SHIPMENT.status" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <!--Barcode Print Status -->
                                <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                    <div class="form-group form-floating custom-ng-select">
                                        <ng-select bindLabel="name" bindValue="id" name="barcodePrintedStatusFilter"
                                            [items]="barcodePrintedOptions"
                                            [(ngModel)]="filterParam.filtering.barcodePrintedStatus"
                                            #BarcodePrintedStatusFilter="ngModel" [clearable]="false">
                                        </ng-select>
                                        <label for="StatusFilter" class="ng-select-label">{{
                                            "BARCODE.barCodePrintStatus" | translate
                                            }}</label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2 mt-12">
                                    <button class="btn btn-primary custom-small-button" (click)="applyFilter()"
                                        appRippleEffect>{{"COMMON.APPLY" |
                                        translate}}</button>
                                    <button class="btn btn-primary custom-small-button"
                                        (click)="clearFilter(searchInput)" appRippleEffect>{{"COMMON.CLEAR" |
                                        translate}}</button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{ 'BARCODE.barcodeNo' | translate }}</th>
                            <th>{{ 'BARCODE.shimpentRefId' | translate }}</th>
                            <th>{{ 'BARCODE.driver' | translate }} </th>
                            <th class="text-center">{{ 'SHIPMENT.status' | translate }} </th>
                            <th class="text-center">{{ 'SHIPMENT.PrintStatus' | translate }} </th>
                            <th class="text-center">{{ 'COMMON.CREATED_ON' | translate }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <div class="table-container-body">
                <ng-template #refID let-data="adtData">
                    <span>{{ data.refID }}</span>
                </ng-template>
                <ng-template #driver let-data="adtData">
                    <span>
                        {{ data?.driverUserDetail?.fullName }}
                    </span>
                </ng-template>

                <ng-template #status let-data="adtData">
                    <div class="d-flex justify-content-center" *ngIf="data.status">
                        <span [appStatusBadge]="data?.status | removeUnderscore"></span>
                    </div>
                </ng-template>

                <ng-template #barcodeNo let-data="adtData">
                    <span>{{ data.barcodeNo }}</span>
                </ng-template>

                <ng-template #printStatus let-data="adtData">
                    <div class="d-flex justify-content-center">
                        <span [appStatusBadge]="(data.isPrinted ? 'printed' : 'not_printed') | removeUnderscore"></span>
                    </div>
                </ng-template>

                <ng-template #createdOn let-data="adtData">
                    <div class="d-flex justify-content-center">{{ data.createdOn | dateFormat}} </div>
                </ng-template>

            </div>
        </div>
    </div>
    <ng-template #content let-modal>
        <div class="modal-content-container">
            <div class="modal-header">
                <h4 class="modal-title">{{title}}</h4>
                <button type="button" class="btn-close modal-close" aria-label="Close"
                    (click)="modal.dismiss('Cross click')"></button>
            </div>
            <div class="modal-body pt-0">
                @if(isBarcodeGenerationModal){
                <div class="row">
                    <!-- Starting BarCode -->
                    <div class="col-md-6 px-md-2 mt-12">
                        <div class="form-group form-floating custom-ng-select">
                            <ng-select bindLabel="barcodeNo" bindValue="barcodeNo" [items]="startBarcodes"
                                [(ngModel)]="filterParam.filtering.start" #StartBarcode="ngModel" name="startBarcode"
                                [typeahead]="searchStartBarcodeSubject" [loading]="loadingStartBarcodeNgSelect">
                            </ng-select>
                            <label for="StatusFilter" class="ng-select-label">{{
                                "BARCODE.fromBarcode" | translate
                                }}</label>
                        </div>
                    </div>

                    <!-- Ending BarCode -->
                    <div class="col-md-6 px-md-2 mt-12">
                        <div class="form-group form-floating custom-ng-select">
                            <ng-select bindLabel="barcodeNo" bindValue="barcodeNo" name="endBarcode"
                                [items]="endBarcodes" [(ngModel)]="filterParam.filtering.end" #EndBarcode="ngModel"
                                [typeahead]="searchLastBarcodeSubject" [loading]="loadingLastBarcodeNgSelect">
                            </ng-select>
                            <label for="StatusFilter" class="ng-select-label">{{
                                "BARCODE.toBarcode" | translate
                                }}</label>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2 mt-12">
                        <button class="btn btn-primary custom-small-button" (click)="markBarcodePrinted()"
                            appRippleEffect>{{"COMMON.APPLY" |
                            translate}}</button>
                    </div>
                </div>
                }@else {
                <app-barcode-edit [modalRef]="modalRef" (saveButtonClicked)="onSaveButtonClicked()">
                </app-barcode-edit>

                }
            </div>
        </div>
    </ng-template>