import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';
import { Shipment } from './shipment';
export class ShipmentItem extends BaseModel {
    tenantId!: number;
    slug!: string;
    shipmentDetail!: Shipment;
    shipment!: string;
    description!: string;
    cargoType!: string;
    weight!: number;
    weightType!: string;
    weightInPounds!: number;
    length!: number;
    width!: number;
    height!: number;
    volume!: number;
    freight!: number;
    fuelCharges!: number;
    gst!: number;
    total!: number;
    rateType!: string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
    }

    static fromResponse(data: any): ShipmentItem {
        return { ...data };
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.description = this.trimMe(this.description);
        this.cargoType = this.trimMe(this.cargoType);
        this.weightType = this.trimMe(this.weightType);
        this.rateType = this.trimMe(this.rateType);
        return this;
    }
}
