import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appAllowNumberOnly]',
  standalone: true
})
export class AllowNumberOnlyDirective {

  @Input() allowedValue: string | null = null; // Allow a specific value

  constructor(private elementRef: ElementRef) { }

  @HostListener('input', ['$event']) onInputChange(event: Event) {
    const initialValue = this.elementRef.nativeElement.value;

    // Replace non-numeric characters except for the allowed value (e.g., dot)
    const newValue = initialValue.replace(new RegExp(`[^0-9${this.allowedValue}]`, 'g'), '');

    // If the value has changed, stop event propagation
    if (initialValue !== newValue) {
      this.elementRef.nativeElement.value = newValue;
      event.stopPropagation();
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    const allowedKeys = [
      'Backspace', 'ArrowLeft', 'ArrowRight', 'Delete', 'Tab', 'Enter'
    ];

    // Allow Ctrl + A (Select All) and Ctrl + C (Copy)
    if (
      (event.ctrlKey && (event.key === 'a' || event.key === 'c')) ||
      allowedKeys.includes(event.key)
    ) {
      return; // Allow these functional keys
    }

    // Allow the specific value if provided
    if (this.allowedValue && event.key === this.allowedValue) {
      return; // Allow the specific value (e.g., dot)
    }

    // Allow only numeric input (0-9)
    const isNumericOrAllowed = /^[0-9]$/.test(event.key);
    if (!isNumericOrAllowed) {
      event.preventDefault(); // Prevent any key that is not a number or allowed key
    }
  }
}
