@import '../../../../variables.scss';

.calender-button-cls {
    width: 50px;
    border-top-right-radius: 12px !important;
    border-bottom-right-radius: 12px !important;
    border: 2px solid var(--bs-border-color);
    border-left-width: 1px;

    &:hover {
        background-color: var(--primary-color); // Change background color on hover
        border-color: #e2e0e0; // Change border color on hover
        color: $black-color;
    }

    &:active {
        background-color: var(--primary-color);
        color: $black-color;
    }

    &.bi-calendar3 {
        &::before {
            font-size: 20px;
        }
    }
}

.z-index-4 {
    z-index: 4;
}

.z-index-3 {
    z-index: 3;
}

.z-index-5 {
    z-index: 5;
}

.margin-bottom-41 {
    margin-bottom: 41px;
}

.form-control[readonly] {
    background-color: $white-color !important;
}

.is-invalid.form-control[readonly] {
    border-width: 2px !important;
    border-color: $form-border-color !important;
}

.is-invalid.calender-button-cls {
    border-width: 2px !important;
    border-left: none !important;
    border-color: $form-border-color !important;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.clear-button-color {
    color: $black-color;
    background: var(--primary-color);

    &:active {
        background-color: var(--primary-color);
        color: $black-color;
        border-color: var(--primary-color);
    }
}

.date-picker-size {
    input {
        height: 56px !important;
        min-height: 56px;
    }
}

.custom-range-picker-radius {
    flex-direction: row;
}

::ng-deep.ngb-dp-navigation-select {
    .form-select {
        padding-top: unset !important;

        &:focus {
            box-shadow: none !important;
        }
    }
}
