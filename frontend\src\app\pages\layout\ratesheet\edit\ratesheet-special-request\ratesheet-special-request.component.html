<div class="site-page-container mt-3">
    <div class="site-card">
        <form #rateSheetForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <div class="label-wrap-box">
                            <span>{{"RATE_SHEET.SpecialRequestInfo" | translate}}</span>
                        </div>

                        <!-- Oversize Rate field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="oversizeRate" #OversizeRate="ngModel"
                                    [(ngModel)]="rateSheet.oversizeRate" appCurrencyFormatter
                                    placeholder="'RATE_SHEET.oversizeRate | translate'" [ngClass]="{
                          'is-invalid': !OversizeRate.valid && onClickValidation
                        }" [maxlength]="10" />
                                <label for="oversizeRate">{{
                                    "RATE_SHEET.oversizeRate" | translate
                                    }}</label>
                                <app-validation-message [field]="OversizeRate" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Rush Request Rate field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="rushRequestRate"
                                    #RushRequestRate="ngModel" [(ngModel)]="rateSheet.rushRequestRate"
                                    placeholder="Customer Name" [ngClass]="{
                          'is-invalid': !RushRequestRate.valid && onClickValidation
                        }" [maxlength]="10" appCurrencyFormatter />
                                <label for="rushRequestRate">{{
                                    "RATE_SHEET.rushRequestRate" | translate
                                    }}</label>
                                <app-validation-message [field]="RushRequestRate"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Enclosed Rate field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="enclosedRate" #EnclosedRate="ngModel"
                                    [(ngModel)]="rateSheet.enclosedRate" placeholder="Customer Name" [ngClass]="{
                          'is-invalid': !EnclosedRate.valid && onClickValidation
                        }" [maxlength]="10" appCurrencyFormatter />
                                <label for="enclosedRate">{{
                                    "RATE_SHEET.enclosedRate" | translate
                                    }}</label>
                                <app-validation-message [field]="EnclosedRate" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Fragile Rate field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="fragileRate" #FragileRate="ngModel"
                                    [(ngModel)]="rateSheet.fragileRate" placeholder="Customer Name" [ngClass]="{
                          'is-invalid': !FragileRate.valid  && onClickValidation
                        }" [maxlength]="10" appCurrencyFormatter />
                                <label for="fragileRate">{{
                                    "RATE_SHEET.fragileRate" | translate
                                    }}</label>
                                <app-validation-message [field]="FragileRate" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Perishable Rate field -->
                        <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="perishableRate" #PerishableRate="ngModel"
                                    [(ngModel)]="rateSheet.perishableRate" placeholder="Customer Name" [ngClass]="{
                          'is-invalid': !PerishableRate.valid && onClickValidation
                        }" [maxlength]="10" appCurrencyFormatter />
                                <label for="perishableRate">{{
                                    "RATE_SHEET.perishableRate" | translate
                                    }}</label>
                                <app-validation-message [field]="PerishableRate"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Dangerous Goods Rate field -->
                        <div class="col-md-6 px-0 pe-md-0 ps-md-2">
                            <div class="form-floating form-group mb-14">
                                <input class="form-control" type="text" name="dangerousGoodsRate"
                                    #DangerousGoodsRate="ngModel" [(ngModel)]="rateSheet.dangerousGoodsRate"
                                    placeholder="Customer Name" [ngClass]="{
                          'is-invalid': !DangerousGoodsRate.valid && onClickValidation
                        }" [maxlength]="10" appCurrencyFormatter />
                                <label for="dangerousGoodsRate">{{
                                    "RATE_SHEET.dangerousGoodsRate" | translate
                                    }}</label>
                                <app-validation-message [field]="DangerousGoodsRate"
                                    [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                [routerLink]="['/dashboard/rate-sheets']">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="save(rateSheetForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>