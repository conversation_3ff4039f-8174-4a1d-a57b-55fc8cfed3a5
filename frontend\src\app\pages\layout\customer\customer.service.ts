import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';

@Injectable({
    providedIn: 'root',
})
export class CustomerService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/account/customer', '/api/account/customers');
    }

    updateCustomerStatus(id: string, data: any): Observable<RestResponse> {
        return this.updateRecord(`/api/account/user/${id}/status`, data);
    }

    fetchForCitiesDropdown(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/address/city/selection', filterParam);
    }

    fetchForProvinceDropdown(filterParam: FilterParam): Observable<RestResponse> {
        return this.getRecords('/api/address/province/selection', filterParam);
    }
}
