// Angular core imports
import { CommonModule } from '@angular/common';
import { Directive, ElementRef, EventEmitter, HostListener, NgModule, Output, Renderer2 } from '@angular/core';

@Directive({
    selector: '[appTogglePasswordVisibility]',
})
export class TogglePasswordVisibilityDirective {
    @Output() visibilityToggled: EventEmitter<boolean> = new EventEmitter<boolean>();

    private inputElement!: HTMLInputElement;

    constructor(
        private el: ElementRef,
        private renderer: Renderer2,
    ) {}

    ngAfterViewInit() {
        // Find the closest input element
        this.inputElement = this.el.nativeElement
            .closest('.toggle-password-visible')
            ?.querySelector('input[type="password"], input[type="text"]') as HTMLInputElement;

        if (!this.inputElement) {
            console.warn('Input element not found.');
            return;
        }

        // Initial icon update
        this.updateIcon(this.inputElement.type === 'text');
    }

    @HostListener('click')
    onClick() {
        if (!this.inputElement) {
            return;
        }

        // Toggle input type
        const newType = this.inputElement.type === 'password' ? 'text' : 'password';
        this.renderer.setAttribute(this.inputElement, 'type', newType);

        // Update the visibility toggle event
        this.visibilityToggled.emit(newType === 'text');

        // Update the icon class
        this.updateIcon(newType === 'text');
    }

    private updateIcon(isText: boolean) {
        const iconElement = this.el.nativeElement
            .closest('.toggle-password-visible')
            ?.querySelector('.eye-icon') as HTMLElement;

        if (iconElement) {
            if (isText) {
                this.renderer.removeClass(iconElement, 'bi-eye-slash');
                this.renderer.addClass(iconElement, 'bi-eye');
            } else {
                this.renderer.removeClass(iconElement, 'bi-eye');
                this.renderer.addClass(iconElement, 'bi-eye-slash');
            }
        } else {
            console.warn('Icon element not found.');
        }
    }
}

@NgModule({
    declarations: [TogglePasswordVisibilityDirective],
    imports: [CommonModule],
    exports: [TogglePasswordVisibilityDirective], // Export the directive to make it available in other components
})
export class TogglePasswordVisibilityModule {}
