import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseService } from '../../../config/base.service';
import { RestResponse } from '../../../models/common/auth.model';

@Injectable({
    providedIn: 'root',
})
export class ContactsService extends BaseService {
    constructor(public override http: HttpClient) {
        super(http, '/api/contactus', '/api/contactus/list');
    }

    updateContactUsStatus(data: any): Observable<RestResponse> {
        const URL = '/api/contactus/status/update';
        return this.saveRecord(URL, data);
    }
}
