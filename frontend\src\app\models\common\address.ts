import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

import { TranslateService } from '@ngx-translate/core';
export class Address extends BaseModel {
    tenantId!: number;
    slug!: string;
    addressLine1!: string;
    addressLine2!: string;
    city!: string | null;
    state!: string | null;
    country!: string | null;
    pin!: string | null;
    landmark!: string;
    type!: string;
    latitude!: string;
    longitude!: string;
    unitNumber!: string;
    countryCode!: string;
    address!: string;
    placeId!: string;
    isManualAddress!: boolean;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
    }

    static fromResponse(data: any): Address {
        return { ...data };
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.addressLine1 = this.trimMe(this.addressLine1);
        this.addressLine2 = this.trimMe(this.addressLine2);
        this.landmark = this.trimMe(this.landmark);
        this.type = this.trimMe(this.type);
        this.latitude = this.trimMe(this.latitude);
        this.longitude = this.trimMe(this.longitude);
        this.unitNumber = this.trimMe(this.unitNumber);
        this.countryCode = this.trimMe(this.countryCode);
        return this;
    }
}
