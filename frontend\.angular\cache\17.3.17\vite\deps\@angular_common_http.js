import {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HTTP_ROOT_INTERCEPTOR_FNS,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptorHandler,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  PRIMARY_HTTP_BACKEND,
  provideHttpClient,
  withFetch,
  withHttpTransferCache,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration
} from "./chunk-5UELJGZN.js";
import "./chunk-VMAOIBZT.js";
import "./chunk-IJ7Y55ZG.js";
import "./chunk-N4CPHFRV.js";
import "./chunk-3S2TBZJ6.js";
import "./chunk-FCQSAM7T.js";
import "./chunk-EIB7IA3J.js";
export {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  provideHttpClient,
  withFetch,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration,
  HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS,
  HttpInterceptorHandler as ɵHttpInterceptingHandler,
  HttpInterceptorHandler as ɵHttpInterceptorHandler,
  PRIMARY_HTTP_BACKEND as ɵPRIMARY_HTTP_BACKEND,
  withHttpTransferCache as ɵwithHttpTransferCache
};
//# sourceMappingURL=@angular_common_http.js.map
