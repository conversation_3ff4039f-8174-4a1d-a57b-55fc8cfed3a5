export class FilterParam {
    relationTable!: string;
    relationId!: string;

    sorting!: SortingParam;
    filtering!: Filtering;
    pagination!: Pagination;

    fileName!: string;
    columns!: string[];
    timeZoneOffset!: number;
    filterParam: { staffId: string } | undefined;

    constructor() {
        this.sorting = new SortingParam();
        this.filtering = new Filtering();
        this.pagination = new Pagination();
    }
}

export class Filtering {
    searchText?: string;
    searchTerm!: string | undefined;
    status!: string;
    role?: string;
    isActive?: boolean;
    paymentType?: string;
    city?: string;
    province?: string;
    createdOn?: string;
    toDate!: string | null;
    fromDate!: string | null;
    rateSheetId!: string | null;
    rateType!: string | null;
    customerId!: string;
    pickUpCity!: string | null;
    deliveryCity!: string | null;
    shipmentType!: string;
    paymentStatus!: string;
    shipmentId!: string;
    date!: Date;
    driverId!: string;
    currentDate!: string;
    isRead!: boolean;

    minPrice!: number;
    maxPrice!: number;

    start!: string | null;
    end!: string | null;

    packageId!: number;
    cargoType!: string;
    weight!: number;

    user!: string;
    quotationId!: string;
    month!: string;

    barcodePrintedStatus!: string;
    latitude!: string;
    longitude!: string;
    isPrinted!: boolean;

    createdWithinDays?: number | null;
}

export class SortingParam {
    sortOrder!: string;
    sortColumn!: string;
}

export class Pagination {
    next?: number;
    offset?: number;
}
