import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

// third party library
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import moment from 'moment';
import { NgxMaterialTimepickerModule, NgxMaterialTimepickerTheme } from 'ngx-material-timepicker';

// components
import { ValidationMessageComponent } from '../validation-message/validation-message.component';

@Component({
    selector: 'app-ng-custom-time-picker',
    standalone: true,
    imports: [
        ValidationMessageComponent,
        NgbDatepickerModule,
        FormsModule,
        CommonModule,
        TranslateModule,
        NgxMaterialTimepickerModule,
    ],
    templateUrl: './ng-custom-time-picker.component.html',
    styleUrls: ['./ng-custom-time-picker.component.scss'],
})
export class NgCustomTimePickerComponent {
    @Input() isRequired: boolean = false;
    @Output() setTime = new EventEmitter<string>();
    @Input() labelName!: string;
    @Input() disabled!: boolean;
    @Input() minDate!: string | null;
    @Input() onClickValidation!: boolean;
    @Input() readOnly: boolean = false;
    @Input() selectedTime: string = '';

    constructor() {}

    ngOnInit() {
        this.selectedTime = '';
    }

    customTheme: NgxMaterialTimepickerTheme = {
        container: {
            buttonColor: 'var(--secondary-color)',
        },
        dial: {
            dialBackgroundColor: 'var(--secondary-color)',
        },
        clockFace: {
            clockHandColor: 'var(--secondary-color)',
        },
    };

    getCurrentTime(): string {
        return moment().format('HH:mm');
    }

    onTimeSelect(time: string): void {
        this.selectedTime = this.formatTime24Hour(time);
        this.setTime.emit(time);
    }

    private formatTime24Hour(time: string): string {
        const parsedTime = moment(time, ['HH:mm', 'HH:mm:ss', 'HH:mm:ss.SSS']);
        return parsedTime.isValid() ? parsedTime.format('HH:mm') : this.getCurrentTime();
    }
}
