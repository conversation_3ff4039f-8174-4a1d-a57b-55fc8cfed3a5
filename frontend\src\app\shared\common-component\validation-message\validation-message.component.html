@if(field && field.invalid && onClickValidation && !customErrorMessage) {
<div class="error-message fw-medium">
    @if(field?.errors?.required || field?.errors?.whitespace) {
    <div>
        @if(type && type == 'FILE_UPLOAD') {
        <div [class]="customClass">
            {{"COMMON.REQUIRED_FILE_VALIDATION_MESSAGE" | translate}}
        </div>
        }
        @if(!(type || type == 'FILE_UPLOAD')) {
        <div [class]="customClass">
            {{fieldErrorMessage != null ? fieldErrorMessage : ("COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate)}}
        </div>
        }
    </div>
    }

    @if(field?.errors?.email) {
    <div [class]="customClass">
        {{"COMMON.REQUIRED_EMAIL_VALIDATION_MESSAGE" | translate}}
    </div>
    }

    @if(field?.errors?.pattern ||
    (!field?.errors?.required && field.errors?.invalidEmail || field.errors?.invalidPhone ||
    field.errors?.invalidPassword)) {
    <div [class]="customClass">
        @if(customPatternMessage) {
        <span>{{customPatternMessage}}</span>
        }
        @if(!customPatternMessage) {
        <span>{{fieldErrorMessage != null ? fieldErrorMessage : ("COMMON.REQUIRED_PATTERN_VALIDATION_MESSAGE" |
            translate)}}</span>
        }
    </div>
    }

    @if(field?.errors?.mobile) {
    <div [class]="customClass">
        {{"COMMON.REQUIRED_MOBILE_VALIDATION_MESSAGE" | translate}}
    </div>
    }

    @if(field?.errors?.mask && !field.errors?.invalidPhone) {
    <div [class]="customClass">
        @if(customMaskMessage) {
        <span>{{customMaskMessage}}</span>
        }
        @if(!customMaskMessage) {
        <span>{{"COMMON.REQUIRED_MASK_VALIDATION_MESSAGE" | translate}} {{field?.errors?.mask.requiredMask}}</span>
        }
    </div>
    }

    @if(field?.errors?.incorrect) {
    <div [class]="customClass">
        {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    }

    @if(field?.errors?.year) {
    <div [class]="customClass">
        {{"COMMON.YEAR_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    }

    @if(field?.errors?.month) {
    <div [class]="customClass">
        {{"COMMON.MONTH_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    }

    @if(field?.errors?.['minlength'] || field?.errors?.customMin) {
    <div [class]="customClass">
        {{"COMMON.REQUIRED_INPUT_MIN_VALIDATION_MESSAGE" | translate}} {{field?.errors?.minlength?.requiredLength}}
    </div>
    }

    @if(field?.errors?.['maxlength'] || field?.errors?.customMax) {
    <div [class]="customClass">
        {{"COMMON.REQUIRED_INPUT_MAX_VALIDATION_MESSAGE" | translate}} {{field?.errors?.maxlength?.requiredLength}}
    </div>
    }
</div>
}

@if(field && field.valid && comparableField && onClickValidation && field.value !== comparableField.value) {
<div class="error-message fw-medium">
    Password does not match
</div>
}

@if(customErrorMessage != null && onClickValidation) {
<div class="error-message fw-medium">
    {{customErrorMessage}}
</div>
}