<app-shipment [detailPage]="true" [customerId]="customerId"></app-shipment>

<div class="site-page-container mt-3">
    <div class="site-card">
        <div class="custom-responsive-row row justify-content-center">
            <div class="col-md-9">
                <div class="col-md-12 custom-buttons-container">
                    <button class="btn cancel-button" appRippleEffect type="button"
                        [routerLink]="['/dashboard/customers']">
                        {{ 'COMMON.CANCEL' | translate }}
                    </button>
                    <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                        (click)="onBack()">
                        {{ 'COMMON.BACK' | translate }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>