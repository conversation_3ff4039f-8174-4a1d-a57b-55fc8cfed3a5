// Angular core modules
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';

// Angular router modules
import { Router, RouterLink } from '@angular/router';

// Third-party modules
import { TranslateModule } from '@ngx-translate/core';

// Managers
import { FormHandlerManager } from '../../../managers/form-handler.manager';

// Models
import { Login } from '../../../models/access/login';

// Services
import { LoadingService } from '../../../services/loading.service';
import { LoginService } from '../../../services/login.service';
import { ToastService } from '../../../shared/services/toast.service';

// Shared utilities and directives
import { ValidationMessageComponent } from '../../../shared/common-component/validation-message/validation-message.component';
import { EmailValidatorDirective } from '../../../shared/directives/email-validator.directive';
import { RippleEffectDirective } from '../../../shared/directives/ripple-effect.directive';
import { CommonService } from '../../../shared/services/common.service';
import { AuthRightSectionComponent } from '../auth-right-section/auth-right-section.component';

// Define the component
@Component({
    selector: 'app-forgot-password',
    standalone: true,
    imports: [
        FormsModule,
        CommonModule,
        TranslateModule,
        RouterLink,
        ValidationMessageComponent,
        RippleEffectDirective,
        EmailValidatorDirective,
        AuthRightSectionComponent,
        NgOptimizedImage,
    ],
    templateUrl: './forgot-password.component.html',
    styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent {
    // Properties
    onClickValidation: boolean = false;
    forgotPasswordData!: Login;

    // Constructor
    constructor(
        private readonly router: Router,
        private readonly loadingService: LoadingService,
        private readonly loginService: LoginService,
        private readonly toastService: ToastService,
        protected formHandlerManager: FormHandlerManager,
        protected commonService: CommonService,
    ) {}

    ngOnInit() {
        this.loadingService.hide();
        this.onClickValidation = false;

        this.forgotPasswordData = new Login();
    }

    requestPasswordReset(form: { valid: boolean }) {
        if (!form.valid) {
            this.onClickValidation = true; // Update the onClickValidation variable
            return;
        }

        this.formHandlerManager.handleFormSubmission(
            this.loginService.resetPassword(this.forgotPasswordData),
            (response) => {
                this.toastService.success(response.message);
            },
            '/login',
        );
    }

    goToLogin() {
        this.router.navigate(['/login']);
    }
}
