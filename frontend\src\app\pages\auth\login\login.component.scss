@import '../../../../variables.scss';

.form-section {
    .form-floating {
        position: relative;

        label {
            font-size: $font-size-14;
            letter-spacing: 0;
            font-weight: 400;
        }

        label::after {
            font-size: $font-size-14;
            letter-spacing: 0;
            font-weight: 400;
            background-color: transparent;
        }

        .field-icon {
            position: absolute;
            top: 18px;
            right: 30px;
            height: max-content;

            img {
                max-width: 24px;
            }
        }
    }

    @media (min-width: 991px) {
        font-size: $font-size-16;
    }
}

.cursor-pointer {
    cursor: pointer;
}

.btn-login {
    background: var(--login-color) !important;
    border-color: var(--login-color) !important;
    max-width: 100%;
    min-width: 100%;
}
