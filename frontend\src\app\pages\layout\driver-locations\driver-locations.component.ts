import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { map } from 'rxjs';
import { Address } from '../../../models/common/address';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { CustomAddressComponent } from '../../../shared/common-component/custom-address/custom-address.component';
import { MapComponent } from '../../../shared/common-component/map/map.component';
import { ToastService } from '../../../shared/services/toast.service';
import { VehicleManager } from '../vehicle/vehicle.manager';

@Component({
    selector: 'app-driver-locations',
    standalone: true,
    imports: [CommonModule, MapComponent, CustomAddressComponent, TranslateModule],
    templateUrl: './driver-locations.component.html',
    styleUrl: './driver-locations.component.scss',
})
export class DriverLocationsComponent {
    @ViewChild('driverMap') driverMap?: MapComponent;

    driverLists: any = [];
    searchAddress = new Address();
    showDriverMap: boolean = true;
    centerPoint!: { lat: number; lng: number };
    isMapLoading: boolean = false; // Add loading state

    constructor(
        protected vehicleManager: VehicleManager,
        protected loadingService: LoadingService,
        protected toastService: ToastService,
        private cdr: ChangeDetectorRef
    ) { }

    fetchDriverLocations(lat: string, lng: string) {
        const filterParam = new FilterParam();
        filterParam.filtering.latitude = lat;
        filterParam.filtering.longitude = lng;

        this.centerPoint = { lat: parseFloat(lat), lng: parseFloat(lng) };

        this.vehicleManager.fetchDriverLocations(filterParam)
            .pipe(
                map((response: RestResponse) => (response?.data ?? []).map((item: any) => ({
                    lat: Number(item?.latitude),
                    lng: Number(item?.longitude),
                    name: item?.driverDetail?.fullName,
                })))
            )
            .subscribe({
                next: (events) => {
                    this.driverLists = events;
                    this.showMapAndRefresh();
                },
                error: (err) => {
                    this.toastService.error(err?.message);
                    this.loadingService.hide();
                    this.isMapLoading = false;
                }
            });
    }

    onAddressSelected(address: Address) {
        if (!address.longitude && !address.latitude) {
            return;
        }

        this.isMapLoading = true;
        this.loadingService.show(); // Only call once here

        this.fetchDriverLocations(address?.latitude, address?.longitude);
    }

    private showMapAndRefresh(): void {
        this.showDriverMap = true;
        this.cdr.detectChanges();

        // Wait for the map to be fully rendered before hiding loader
        setTimeout(() => {
            if (this.driverMap) {
                this.driverMap.refreshView();
                // Hide loader after map refresh is complete
                setTimeout(() => {
                    this.loadingService.hide();
                    this.isMapLoading = false;
                }, 100);
            } else {
                this.loadingService.hide();
                this.isMapLoading = false;
            }
        }, 0);
    }
}

