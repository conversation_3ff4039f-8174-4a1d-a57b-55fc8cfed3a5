import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../../config/base.model';
import { ToastService } from '../../shared/services/toast.service';

export class Contacts extends BaseModel {
    tenantId!: number;
    slug!: string;
    fullName!: string;
    firstName!: string;
    lastName!: string;
    email!: string;
    contact!: string;
    phone!: string;
    message!: string;
    subject!: string;
    status!: string | null;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
    }

    static fromResponse(data: any): Contacts {
        const contacts = { ...data };

        return contacts;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        return this;
    }
}
