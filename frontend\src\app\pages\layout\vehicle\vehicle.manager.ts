import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { VehicleService } from './vehicle.service';

@Injectable({
    providedIn: 'root',
})
export class VehicleManager extends BaseManager {
    constructor(
        protected vehicleService: VehicleService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(vehicleService, loadingService, toastService);
    }

    fetchDriverLocations(filterParam: FilterParam): Observable<RestResponse> {
        return this.vehicleService
            .fetchDriverLocationsDetails(filterParam)
            .pipe(map((response: RestResponse) => response));
    }
}
