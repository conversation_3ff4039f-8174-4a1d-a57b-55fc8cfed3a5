@import '../../variables.scss';

/* General page styling */
.terms-privacy-page {
    background: #f9fafb;
    color: #333;
    line-height: 1.6;

    .page-content-padding {
        background: $white-color;
        border-radius: 12px;
        padding: 2rem 2.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    }

    h5,
    h6 {
        color: #222;
        font-weight: 600;
        margin-bottom: 0.8rem;
    }

    h5 {
        font-size: 1.5rem;
        border-bottom: 2px solid var(--login-color, #ffb703);
        padding-bottom: 0.5rem;
        margin-bottom: 1.5rem;
    }

    h6 {
        font-size: 1.1rem;
        margin-top: 1.8rem;
    }

    p {
        margin-bottom: 0.8rem;
        color: #555;
    }

    ul.terms-listing {
        margin: 0.5rem 0 1rem 0;
        padding-left: 1.4rem;

        li {
            margin-bottom: 0.5rem;
            color: #444;
            position: relative;
            padding-left: 1rem;

            &::before {
                content: "✔";
                color: var(--login-color, #ffb703);
                font-weight: bold;
                position: absolute;
                left: 0;
            }
        }
    }

    address {
        font-style: normal;
        background: #f3f4f67a;
        border-left: 4px solid var(--login-color, #ffb703);
        padding: 1rem;
        border-radius: 8px;
        margin-top: 1rem;

        a {
            color: var(--login-color, #ffb703);
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

.email-text {
    color: #00bfff !important;
    font-weight: 500;
    text-decoration: none;

    &:hover {
        color: darken(#00bfff, 10%) !important; // slightly darker blue on hover
        text-decoration: underline !important;
    }

    &:visited {
        color: #00bfff !important; // keep same color after click
    }
}