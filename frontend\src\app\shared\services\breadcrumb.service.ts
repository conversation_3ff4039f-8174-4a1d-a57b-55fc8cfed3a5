import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class BreadcrumbService {
    breadcrumbsSubject = new BehaviorSubject<any[]>([]);
    breadcrumbs$ = this.breadcrumbsSubject.asObservable();

    numberSubject = new BehaviorSubject<any>(null); // Initial value
    number$ = this.numberSubject.asObservable();

    addLoadingBreadcrumb(breadcrumbs: any[]) {
        const updatedBreadcrumbs = [...breadcrumbs];
        updatedBreadcrumbs.push({
            isLoading: true,
            isDynamicTitle: true,
            active: true,
        });
        this.setBreadcrumbs(updatedBreadcrumbs);
        return updatedBreadcrumbs;
    }

    setBreadcrumbs(breadcrumbs: any[]) {
        this.breadcrumbsSubject.next(breadcrumbs);
    }

    setNumber(value: any): void {
        this.numberSubject.next(value);
    }

    clearNumber() {
        this.numberSubject.next(null);
    }
}
