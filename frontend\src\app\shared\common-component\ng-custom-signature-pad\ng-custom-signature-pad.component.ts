// Angular core
import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    HostListener,
    Input,
    Output,
    ViewChild,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import SignaturePad from 'signature_pad';

// third-party library
@Component({
    selector: 'app-ng-custom-signature-pad',
    templateUrl: './ng-custom-signature-pad.component.html',
    styleUrls: ['./ng-custom-signature-pad.component.scss'],
    standalone: true,
    imports: [TranslateModule],
})
export class NgCustomSignaturePadComponent implements AfterViewInit {
    @ViewChild('canvas', { static: true }) canvasElement!: ElementRef<HTMLCanvasElement>;
    signaturePad!: SignaturePad;
    @Input() initialSignature!: string;
    @Output() signatureUpdated = new EventEmitter<string | null>();
    @Input() isHeadingShown: boolean = true;
    @Input() signatureTypeModalRef!: any;
    @Input() disabled: boolean = false;

    private signatureLines: any[] = [];
    previousWidth!: number;
    previousHeight!: number;

    ngAfterViewInit(): void {
        this.adjustCanvasSize();
        this.signaturePad = new SignaturePad(this.canvasElement.nativeElement, {
            minWidth: 1,
            maxWidth: 1.5,
            throttle: 0,
            minDistance: 0,
            velocityFilterWeight: 0.4,
            penColor: 'rgb(0, 0, 0)',
        });

        this.signaturePad.addEventListener('endStroke', () => {
            const updatedSignature = this.signaturePad.toDataURL();
            this.onSignatureUpdated(updatedSignature);
        });

        if (this.initialSignature) {
            this.signaturePad.fromDataURL(this.initialSignature);
        }

        this.previousWidth = this.canvasElement.nativeElement.offsetWidth;
        this.previousHeight = this.canvasElement.nativeElement.offsetHeight;
    }

    ngOnDestroy(): void {
        this.signaturePad.off();
    }

    @HostListener('window:resize')
    onResize() {
        this.adjustCanvasSize();
    }

    onSignatureUpdated(updatedSignature: string) {
        this.signatureUpdated.emit(updatedSignature);
    }

    private adjustCanvasSize() {
        const canvas = this.canvasElement.nativeElement;
        const container = canvas.closest('.canvas-wrapper') as HTMLElement;
        const containerWidth = container.offsetWidth;
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = (containerWidth - 2) * ratio;
        canvas.height = 200 * ratio;

        const context = canvas.getContext('2d');
        if (context) {
            context.scale(ratio, ratio);
        }

        if (this.signaturePad) {
            if (this.initialSignature) {
                this.signaturePad.fromDataURL(this.initialSignature);
            }

            this.signatureLines = this.signaturePad.toData();
            const currentWidth = this.canvasElement.nativeElement.offsetWidth;
            const currentHeight = this.canvasElement.nativeElement.offsetHeight;
            const scaleX = currentWidth / this.previousWidth;
            const scaleY = currentHeight / this.previousHeight;
            this.previousWidth = currentWidth;
            this.previousHeight = currentHeight;
            this.rescaleSignature(this.signatureLines, scaleX, scaleY);
            this.signaturePad.fromData(this.signatureLines);
        }
    }

    private rescaleSignature(lines: any, scaleX: number, scaleY: number) {
        lines.forEach((line: { points: any[] }) => {
            line.points.forEach((point) => {
                point.x *= scaleX;
                point.y *= scaleY;
            });
        });
    }

    clearSignature() {
        this.signaturePad.clear();
        this.signatureUpdated.emit(null);
    }
}
