<ng-container>
    <div class="p-3 pb-1 d-flex justify-content-end">
        <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
            (click)="accordion.toggle('filter');">
            <span>
                <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
            </span>

            <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                translate}}
            </span>
        </button>
    </div>

    <div ngbAccordion #accordion="ngbAccordion" class="mt-2 px-3 pb-2">
        <div ngbAccordionItem="filter" class="border-0">
            <div ngbAccordionCollapse>
                <div ngbAccordionBody class="filter-container p-4 w-100">
                    <ng-template>
                        <div class="row gx-0 gy-3">
                            <!-- Drivers  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="fullName" bindValue="id" [items]="drivers"
                                        [(ngModel)]="filterParam.filtering.driverId" #Driver="ngModel" name="driver"
                                        [typeahead]="searchDriverSubject" [loading]="loadingDriverNgSelect">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "BARCODE.driver" | translate
                                        }}</label>
                                </div>
                            </div>

                            <!-- Customer  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="fullName" bindValue="id" name="StatusFilter"
                                        #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.customerId"
                                        [items]="customers" [typeahead]="searchCustomerSubject"
                                        [loading]="loadingCustomerNgSelect">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "CUSTOMER.objName" | translate
                                        }}</label>
                                </div>
                            </div>

                            <!-- Pickup Address  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                        #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.pickUpCity"
                                        [items]="pickupCities" [typeahead]="searchPickupCitySubject"
                                        [loading]="loadingPickupCityNgSelect">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "RATE_SHEET.pickupCity" | translate
                                        }}</label>
                                </div>
                            </div>

                            <!-- Delivery Address  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 px-xl-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="city" bindValue="city" name="StatusFilter"
                                        #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.deliveryCity"
                                        [typeahead]="searchDeliveryCitySubject" [items]="deliveryCities"
                                        [loading]="loadingDeliveryCityNgSelect">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "RATE_SHEET.deliveryCity" | translate
                                        }}</label>
                                </div>
                            </div>

                            <!-- Status  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-md-2 ps-md-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                        #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.status"
                                        [items]="shipmentStatusOptions">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "SHIPMENT.shipmentStatus" | translate
                                        }}</label>
                                </div>
                            </div>

                            <!-- PaymentStatus  -->
                            <div class="col-md-4 col-xl-3 px-0 pe-xl-2 ps-xl-0 mt-12">
                                <div class="form-group form-floating custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" name="StatusFilter"
                                        #StatusFilter="ngModel" [(ngModel)]="filterParam.filtering.paymentStatus"
                                        [items]="shipmentPaymentStatusOptions">
                                    </ng-select>
                                    <label for="StatusFilter" class="ng-select-label">{{
                                        "SHIPMENT.paymentStatus" | translate
                                        }}</label>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end gap-2 mt-12">
                                <button class="btn btn-primary custom-small-button" (click)="onApplyFilter()"
                                    appRippleEffect>{{"COMMON.APPLY" |
                                    translate}}</button>
                                <button class="btn btn-primary custom-small-button" (click)="onClearFilter()"
                                    appRippleEffect>{{"COMMON.CLEAR" |
                                    translate}}</button>
                            </div>
                        </div>
                    </ng-template>
                </div>
            </div>
        </div>
    </div>

    <div class="calendar-scroll-container p-3 pt-0">
        <full-calendar #calendar [options]="calendarOptions"></full-calendar>
    </div>
</ng-container>

<ng-template #content let-modal>
    <div class="modal-content-container">
        <div class="modal-header">
            <h4 class="modal-title">Shipment Detail</h4>
            <button type="button" class="btn-close modal-close" aria-label="Close"
                (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body pt-0">
            <div class="bg-white rounded p-3 shadow-sm">
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'SHIPMENT.objName' | translate}}</span>
                    <span class="fw-bold text-primary">{{ events.title }}</span>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'BARCODE.driver' | translate}} {{'COMMON.NAME' | translate}}</span>
                    <span class="fw-bold">{{ events.driver }}</span>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'CUSTOMER.objName' | translate}}</span>
                    <span class="fw-bold">{{ events.customer }}</span>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'RATE_SHEET.pickupCity' | translate}}</span>
                    <span class="fw-bold">{{ events.pickupCity }}</span>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'RATE_SHEET.deliveryCity' | translate}}</span>
                    <span class="fw-bold">{{ events.deliveryCity }}</span>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'SHIPMENT.shipmentStatus' | translate}}</span>
                    <div class="d-flex" *ngIf="events.status">
                        <span [appStatusBadge]="events.status | removeUnderscore"></span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span class="text-muted">{{'SHIPMENT.paymentStatus' | translate}}</span>
                    <div class="d-flex" *ngIf="events.paymentStatus">
                        <span [appStatusBadge]="events.paymentStatus | removeUnderscore"></span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center pt-2">
                    <span class="text-muted">{{'RateSheetWeightCharge.total' | translate}} Price</span>
                    <span class="fw-bold text-success">${{ events.totalAmount }}</span>
                </div>
            </div>

            <button class="btn btn-primary custom-medium-button mt-4" style="min-width: 100% !important;"
                (click)="onEdit(events)">
                View Full Shipment Details
            </button>
        </div>
    </div>
</ng-template>