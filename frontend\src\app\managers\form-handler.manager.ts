/// Angular Imports
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

// Third-party Imports
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

// Local Project Imports

//common services
import { LoadingService } from '../services/loading.service';
import { ToastService } from '../shared/services/toast.service';

@Injectable({
    providedIn: 'root',
})
export class FormHandlerManager {
    constructor(
        protected loadingService: LoadingService,
        protected toastService: ToastService,
        protected router: Router,
    ) {}

    // public methods
    handleFormSubmission<T>(
        serviceCall: Observable<T>,
        successCallback: (response: T) => void,
        redirectUrl?: string,
    ): void {
        this.subscribeToServiceCall(serviceCall, successCallback, redirectUrl);
    }

    // private Methods
    private handleError(error: any): Observable<null> {
        this.toastService.error(error.message || error.title);
        return of(null); // Complete the observable stream
    }

    private handleResponse<T>(response: any, successCallback: (response: T) => void, redirectUrl?: string): void {
        successCallback(response);

        if (redirectUrl) {
            this.router.navigate([redirectUrl]);
        }
    }

    // call the service method
    private subscribeToServiceCall<T>(
        serviceCall: Observable<T>,
        successCallback: (response: T) => void,
        redirectUrl?: string,
    ): void {
        this.loadingService.show();

        serviceCall
            .pipe(
                tap((response: any) => this.handleResponse(response, successCallback, redirectUrl)),
                catchError((error) => this.handleError(error)),
                tap(() => this.loadingService.hide()),
            )
            .subscribe();
    }
}
