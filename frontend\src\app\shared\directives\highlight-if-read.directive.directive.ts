import { Directive, ElementRef, Input, OnChanges, Renderer2, SimpleChanges } from '@angular/core';

@Directive({
    selector: '[appHighlightIfRead]',
    standalone: true,
})
export class HighlightIfReadDirective implements OnChanges {
    @Input('appHighlightIfRead') isRead: boolean = false;

    constructor(
        private el: ElementRef,
        private renderer: Renderer2,
    ) {}

    ngOnChanges(changes: SimpleChanges): void {
        if (this.isRead) return;

        let parent = this.el.nativeElement;

        // Traverse up the DOM to find the closest <tr>
        while (parent && parent.tagName !== 'TR') {
            parent = parent.parentElement;
        }

        if (parent) {
            this.renderer.setStyle(parent, 'background-color', '#fdf5b0'); // light yellow
        }
    }
}
