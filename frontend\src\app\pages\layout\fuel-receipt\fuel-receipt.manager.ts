import { Injectable } from '@angular/core';
import { BaseManager } from '../../../config/base.manager';
import { LoadingService } from '../../../services/loading.service';
import { ToastService } from '../../../shared/services/toast.service';
import { FuelReceiptService } from './fuel-receipt.service';

@Injectable({
    providedIn: 'root',
})
export class FuelReceiptManager extends BaseManager {
    constructor(
        protected fuelReceiptService: FuelReceiptService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(fuelReceiptService, loadingService, toastService);
    }

}
