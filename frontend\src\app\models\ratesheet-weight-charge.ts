import { TranslateService } from '@ngx-translate/core';
import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/services/toast.service';
import { RateSheet } from './rate-sheet';
export class RateSheetWeightCharge extends BaseModel {
    tenantId!: number;
    slug!: string;
    rateSheetDetail!: RateSheet;
    rateSheet!: string;
    fromWeight!: number;
    toWeight!: number;
    freight!: number;
    fuelCharges!: number;
    gst!: number;
    total!: number;
    rateType!: string;
    gstRate!: number;
    fuelRate!: number;

    constructor(data?: Partial<RateSheetWeightCharge>) {
        super();
        this.isDeleted = false;
        this.isActive = true;

        if (data) {
            Object.assign(this, data);
        }
    }

    static fromResponse(data: any): RateSheetWeightCharge {
        return new RateSheetWeightCharge(data);
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        if (this.isNullOrUndefinedAndEmpty(this.rateType)) {
            form.controls.rateType.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
        return this;
    }
}
