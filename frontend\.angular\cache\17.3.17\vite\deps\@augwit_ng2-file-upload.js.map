{"version": 3, "sources": ["../../../../../node_modules/@augwit/ng2-file-upload/fesm2022/augwit-ng2-file-upload.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nclass FileLikeObject {\n  constructor(fileOrInput) {\n    this.rawFile = fileOrInput;\n    const fakePathOrObject = fileOrInput instanceof HTMLInputElement ? fileOrInput.value : fileOrInput;\n    const postfix = typeof fakePathOrObject === 'string' ? 'FakePath' : 'Object';\n    const method = `_createFrom${postfix}`;\n    this[method](fakePathOrObject);\n  }\n  _createFromFakePath(path) {\n    this.lastModifiedDate = void 0;\n    this.size = void 0;\n    this.type = `like/${path.slice(path.lastIndexOf('.') + 1).toLowerCase()}`;\n    this.name = path.slice(path.lastIndexOf('/') + path.lastIndexOf('\\\\') + 2);\n  }\n  _createFromObject(object) {\n    this.size = object.size;\n    this.type = object.type;\n    this.name = object.name;\n  }\n}\nclass FileItem {\n  constructor(uploader, some, options) {\n    this.url = '/';\n    this.headers = [];\n    this.withCredentials = true;\n    this.formData = [];\n    this.isReady = false;\n    this.isUploading = false;\n    this.isUploaded = false;\n    this.isSuccess = false;\n    this.isCancel = false;\n    this.isError = false;\n    this.progress = 0;\n    this.uploader = uploader;\n    this.some = some;\n    this.options = options;\n    this.file = new FileLikeObject(some);\n    this._file = some;\n    if (uploader.options) {\n      this.method = uploader.options.method || 'POST';\n      this.alias = uploader.options.itemAlias || 'file';\n    }\n    this.url = uploader.options.url;\n  }\n  upload() {\n    try {\n      this.uploader.uploadItem(this);\n    } catch (e) {\n      this.uploader._onCompleteItem(this, '', 0, {});\n      this.uploader._onErrorItem(this, '', 0, {});\n    }\n  }\n  cancel() {\n    this.uploader.cancelItem(this);\n  }\n  remove() {\n    this.uploader.removeFromQueue(this);\n  }\n  onBeforeUpload() {\n    return void 0;\n  }\n  onBuildForm(form) {\n    return {\n      form\n    };\n  }\n  onProgress(progress) {\n    return {\n      progress\n    };\n  }\n  onSuccess(response, status, headers) {\n    return {\n      response,\n      status,\n      headers\n    };\n  }\n  onError(response, status, headers) {\n    return {\n      response,\n      status,\n      headers\n    };\n  }\n  onCancel(response, status, headers) {\n    return {\n      response,\n      status,\n      headers\n    };\n  }\n  onComplete(response, status, headers) {\n    return {\n      response,\n      status,\n      headers\n    };\n  }\n  _onBeforeUpload() {\n    this.isReady = true;\n    this.isUploading = true;\n    this.isUploaded = false;\n    this.isSuccess = false;\n    this.isCancel = false;\n    this.isError = false;\n    this.progress = 0;\n    this.onBeforeUpload();\n  }\n  _onBuildForm(form) {\n    this.onBuildForm(form);\n  }\n  _onProgress(progress) {\n    this.progress = progress;\n    this.onProgress(progress);\n  }\n  _onSuccess(response, status, headers) {\n    this.isReady = false;\n    this.isUploading = false;\n    this.isUploaded = true;\n    this.isSuccess = true;\n    this.isCancel = false;\n    this.isError = false;\n    this.progress = 100;\n    this.index = undefined;\n    this.onSuccess(response, status, headers);\n  }\n  _onError(response, status, headers) {\n    this.isReady = false;\n    this.isUploading = false;\n    this.isUploaded = true;\n    this.isSuccess = false;\n    this.isCancel = false;\n    this.isError = true;\n    this.progress = 0;\n    this.index = undefined;\n    this.onError(response, status, headers);\n  }\n  _onCancel(response, status, headers) {\n    this.isReady = false;\n    this.isUploading = false;\n    this.isUploaded = false;\n    this.isSuccess = false;\n    this.isCancel = true;\n    this.isError = false;\n    this.progress = 0;\n    this.index = undefined;\n    this.onCancel(response, status, headers);\n  }\n  _onComplete(response, status, headers) {\n    this.onComplete(response, status, headers);\n    if (this.uploader.options.removeAfterUpload) {\n      this.remove();\n    }\n  }\n  _prepareToUploading() {\n    this.index = this.index || ++this.uploader._nextIndex;\n    this.isReady = true;\n  }\n}\nclass FileType {\n  /*  MS office  */\n  // tslint:disable-next-line:variable-name\n  static {\n    this.mime_doc = ['application/msword', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template', 'application/vnd.ms-word.document.macroEnabled.12', 'application/vnd.ms-word.template.macroEnabled.12'];\n  }\n  // tslint:disable-next-line:variable-name\n  static {\n    this.mime_xsl = ['application/vnd.ms-excel', 'application/vnd.ms-excel', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template', 'application/vnd.ms-excel.sheet.macroEnabled.12', 'application/vnd.ms-excel.template.macroEnabled.12', 'application/vnd.ms-excel.addin.macroEnabled.12', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'];\n  }\n  // tslint:disable-next-line:variable-name\n  static {\n    this.mime_ppt = ['application/vnd.ms-powerpoint', 'application/vnd.ms-powerpoint', 'application/vnd.ms-powerpoint', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.openxmlformats-officedocument.presentationml.template', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow', 'application/vnd.ms-powerpoint.addin.macroEnabled.12', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'];\n  }\n  /* PSD */\n  // tslint:disable-next-line:variable-name\n  static {\n    this.mime_psd = ['image/photoshop', 'image/x-photoshop', 'image/psd', 'application/photoshop', 'application/psd', 'zz-application/zz-winassoc-psd'];\n  }\n  /* Compressed files */\n  // tslint:disable-next-line:variable-name\n  static {\n    this.mime_compress = ['application/x-gtar', 'application/x-gcompress', 'application/compress', 'application/x-tar', 'application/x-rar-compressed', 'application/octet-stream', 'application/x-zip-compressed', 'application/zip-compressed', 'application/x-7z-compressed', 'application/gzip', 'application/x-bzip2'];\n  }\n  static getMimeClass(file) {\n    let mimeClass = 'application';\n    if (file?.type && this.mime_psd.indexOf(file.type) !== -1) {\n      mimeClass = 'image';\n    } else if (file?.type?.match('image.*')) {\n      mimeClass = 'image';\n    } else if (file?.type?.match('video.*')) {\n      mimeClass = 'video';\n    } else if (file?.type?.match('audio.*')) {\n      mimeClass = 'audio';\n    } else if (file?.type === 'application/pdf') {\n      mimeClass = 'pdf';\n    } else if (file?.type && this.mime_compress.indexOf(file.type) !== -1) {\n      mimeClass = 'compress';\n    } else if (file?.type && this.mime_doc.indexOf(file.type) !== -1) {\n      mimeClass = 'doc';\n    } else if (file?.type && this.mime_xsl.indexOf(file.type) !== -1) {\n      mimeClass = 'xls';\n    } else if (file?.type && this.mime_ppt.indexOf(file.type) !== -1) {\n      mimeClass = 'ppt';\n    }\n    if (mimeClass === 'application' && file?.name) {\n      mimeClass = this.fileTypeDetection(file.name);\n    }\n    return mimeClass;\n  }\n  static fileTypeDetection(inputFilename) {\n    const types = {\n      jpg: 'image',\n      jpeg: 'image',\n      tif: 'image',\n      psd: 'image',\n      bmp: 'image',\n      png: 'image',\n      nef: 'image',\n      tiff: 'image',\n      cr2: 'image',\n      dwg: 'image',\n      cdr: 'image',\n      ai: 'image',\n      indd: 'image',\n      pin: 'image',\n      cdp: 'image',\n      skp: 'image',\n      stp: 'image',\n      '3dm': 'image',\n      mp3: 'audio',\n      wav: 'audio',\n      wma: 'audio',\n      mod: 'audio',\n      m4a: 'audio',\n      compress: 'compress',\n      zip: 'compress',\n      rar: 'compress',\n      '7z': 'compress',\n      lz: 'compress',\n      z01: 'compress',\n      bz2: 'compress',\n      gz: 'compress',\n      pdf: 'pdf',\n      xls: 'xls',\n      xlsx: 'xls',\n      ods: 'xls',\n      mp4: 'video',\n      avi: 'video',\n      wmv: 'video',\n      mpg: 'video',\n      mts: 'video',\n      flv: 'video',\n      '3gp': 'video',\n      vob: 'video',\n      m4v: 'video',\n      mpeg: 'video',\n      m2ts: 'video',\n      mov: 'video',\n      doc: 'doc',\n      docx: 'doc',\n      eps: 'doc',\n      txt: 'doc',\n      odt: 'doc',\n      rtf: 'doc',\n      ppt: 'ppt',\n      pptx: 'ppt',\n      pps: 'ppt',\n      ppsx: 'ppt',\n      odp: 'ppt'\n    };\n    const chunks = inputFilename.split('.');\n    if (chunks.length < 2) {\n      return 'application';\n    }\n    const extension = chunks[chunks.length - 1].toLowerCase();\n    if (types[extension] === undefined) {\n      return 'application';\n    } else {\n      return types[extension];\n    }\n  }\n}\nfunction isFile(value) {\n  return File && value instanceof File;\n}\nclass FileUploader {\n  constructor(options) {\n    this.isUploading = false;\n    this.queue = [];\n    this.progress = 0;\n    this._nextIndex = 0;\n    this.options = {\n      autoUpload: false,\n      isHTML5: true,\n      filters: [],\n      removeAfterUpload: false,\n      disableMultipart: false,\n      formatDataFunction: item => item._file,\n      formatDataFunctionIsAsync: false,\n      url: ''\n    };\n    this.setOptions(options);\n    this.response = new EventEmitter();\n  }\n  setOptions(options) {\n    this.options = Object.assign(this.options, options);\n    this.authToken = this.options.authToken;\n    this.authTokenHeader = this.options.authTokenHeader || 'Authorization';\n    this.autoUpload = this.options.autoUpload;\n    this.options.filters?.unshift({\n      name: 'queueLimit',\n      fn: this._queueLimitFilter\n    });\n    if (this.options.maxFileSize) {\n      this.options.filters?.unshift({\n        name: 'fileSize',\n        fn: this._fileSizeFilter\n      });\n    }\n    if (this.options.allowedFileType) {\n      this.options.filters?.unshift({\n        name: 'fileType',\n        fn: this._fileTypeFilter\n      });\n    }\n    if (this.options.allowedMimeType) {\n      this.options.filters?.unshift({\n        name: 'mimeType',\n        fn: this._mimeTypeFilter\n      });\n    }\n    for (let i = 0; i < this.queue.length; i++) {\n      this.queue[i].url = this.options.url;\n    }\n  }\n  addToQueue(files, _options, filters) {\n    let options = _options;\n    const list = [];\n    for (const file of files) {\n      list.push(file);\n    }\n    const arrayOfFilters = this._getFilters(filters);\n    const count = this.queue.length;\n    const addedFileItems = [];\n    list.map(some => {\n      if (!options) {\n        options = this.options;\n      }\n      const temp = new FileLikeObject(some);\n      if (this._isValidFile(temp, arrayOfFilters, options)) {\n        const fileItem = new FileItem(this, some, options);\n        addedFileItems.push(fileItem);\n        this.queue.push(fileItem);\n        this._onAfterAddingFile(fileItem);\n      } else {\n        if (typeof this._failFilterIndex === 'number' && this._failFilterIndex >= 0) {\n          const filter = arrayOfFilters[this._failFilterIndex];\n          this._onWhenAddingFileFailed(temp, filter, options);\n        }\n      }\n    });\n    if (this.queue.length !== count) {\n      this._onAfterAddingAll(addedFileItems);\n      this.progress = this._getTotalProgress();\n    }\n    this._render();\n    if (this.options.autoUpload) {\n      this.uploadAll();\n    }\n  }\n  removeFromQueue(value) {\n    const index = this.getIndexOfItem(value);\n    const item = this.queue[index];\n    if (item.isUploading) {\n      item.cancel();\n    }\n    this.queue.splice(index, 1);\n    this.progress = this._getTotalProgress();\n  }\n  clearQueue() {\n    while (this.queue.length) {\n      this.queue[0].remove();\n    }\n    this.progress = 0;\n  }\n  uploadItem(value) {\n    const index = this.getIndexOfItem(value);\n    const item = this.queue[index];\n    const transport = this.options.isHTML5 ? '_xhrTransport' : '_iframeTransport';\n    item._prepareToUploading();\n    if (this.isUploading) {\n      return;\n    }\n    this.isUploading = true;\n    this[transport](item);\n  }\n  cancelItem(value) {\n    const index = this.getIndexOfItem(value);\n    const item = this.queue[index];\n    const prop = this.options.isHTML5 ? item._xhr : item._form;\n    if (item && item.isUploading) {\n      prop.abort();\n    }\n  }\n  uploadAll() {\n    const items = this.getNotUploadedItems().filter(item => !item.isUploading);\n    if (!items.length) {\n      return;\n    }\n    items.map(item => item._prepareToUploading());\n    items[0].upload();\n  }\n  cancelAll() {\n    const items = this.getNotUploadedItems();\n    items.map(item => item.cancel());\n  }\n  isFile(value) {\n    return isFile(value);\n  }\n  isFileLikeObject(value) {\n    return value instanceof FileLikeObject;\n  }\n  getIndexOfItem(value) {\n    return typeof value === 'number' ? value : this.queue.indexOf(value);\n  }\n  getNotUploadedItems() {\n    return this.queue.filter(item => !item.isUploaded);\n  }\n  getReadyItems() {\n    return this.queue.filter(item => item.isReady && !item.isUploading).sort((item1, item2) => item1.index - item2.index);\n  }\n  onAfterAddingAll(fileItems) {\n    return {\n      fileItems\n    };\n  }\n  onBuildItemForm(fileItem, form) {\n    return {\n      fileItem,\n      form\n    };\n  }\n  onAfterAddingFile(fileItem) {\n    return {\n      fileItem\n    };\n  }\n  onWhenAddingFileFailed(item, filter, options) {\n    return {\n      item,\n      filter,\n      options\n    };\n  }\n  onBeforeUploadItem(fileItem) {\n    return {\n      fileItem\n    };\n  }\n  onProgressItem(fileItem, progress) {\n    return {\n      fileItem,\n      progress\n    };\n  }\n  onProgressAll(progress) {\n    return {\n      progress\n    };\n  }\n  onSuccessItem(item, response, status, headers) {\n    return {\n      item,\n      response,\n      status,\n      headers\n    };\n  }\n  onErrorItem(item, response, status, headers) {\n    return {\n      item,\n      response,\n      status,\n      headers\n    };\n  }\n  onCancelItem(item, response, status, headers) {\n    return {\n      item,\n      response,\n      status,\n      headers\n    };\n  }\n  onCompleteItem(item, response, status, headers) {\n    return {\n      item,\n      response,\n      status,\n      headers\n    };\n  }\n  onCompleteAll() {\n    return void 0;\n  }\n  _mimeTypeFilter(item) {\n    return !(item?.type && this.options.allowedMimeType && this.options.allowedMimeType?.indexOf(item.type) === -1);\n  }\n  _fileSizeFilter(item) {\n    return !(this.options.maxFileSize && item.size > this.options.maxFileSize);\n  }\n  _fileTypeFilter(item) {\n    return !(this.options.allowedFileType && this.options.allowedFileType.indexOf(FileType.getMimeClass(item)) === -1);\n  }\n  _onErrorItem(item, response, status, headers) {\n    item._onError(response, status, headers);\n    this.onErrorItem(item, response, status, headers);\n  }\n  _onCompleteItem(item, response, status, headers) {\n    item._onComplete(response, status, headers);\n    this.onCompleteItem(item, response, status, headers);\n    const nextItem = this.getReadyItems()[0];\n    this.isUploading = false;\n    if (nextItem) {\n      nextItem.upload();\n      return;\n    }\n    this.onCompleteAll();\n    this.progress = this._getTotalProgress();\n    this._render();\n  }\n  _headersGetter(parsedHeaders) {\n    return name => {\n      if (name) {\n        return parsedHeaders[name.toLowerCase()] || undefined;\n      }\n      return parsedHeaders;\n    };\n  }\n  _xhrTransport(item) {\n    // tslint:disable-next-line:no-this-assignment\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    const xhr = item._xhr = new XMLHttpRequest();\n    let sendable;\n    this._onBeforeUploadItem(item);\n    if (typeof item._file.size !== 'number') {\n      throw new TypeError('The file specified is no longer valid');\n    }\n    if (!this.options.disableMultipart) {\n      sendable = new FormData();\n      this._onBuildItemForm(item, sendable);\n      const appendFile = () => sendable.append(item.alias, item._file, item.file.name);\n      if (!this.options.parametersBeforeFiles) {\n        appendFile();\n      }\n      // For AWS, Additional Parameters must come BEFORE Files\n      if (this.options.additionalParameter !== undefined) {\n        Object.keys(this.options.additionalParameter).forEach(key => {\n          let paramVal = this.options.additionalParameter?.[key];\n          // Allow an additional parameter to include the filename\n          if (typeof paramVal === 'string' && paramVal.indexOf('{{file_name}}') >= 0 && item.file?.name) {\n            paramVal = paramVal.replace('{{file_name}}', item.file.name);\n          }\n          sendable.append(key, paramVal);\n        });\n      }\n      if (appendFile && this.options.parametersBeforeFiles) {\n        appendFile();\n      }\n    } else {\n      if (this.options.formatDataFunction) {\n        sendable = this.options.formatDataFunction(item);\n      }\n    }\n    xhr.upload.onprogress = event => {\n      const progress = Math.round(event.lengthComputable ? event.loaded * 100 / event.total : 0);\n      this._onProgressItem(item, progress);\n    };\n    xhr.onload = () => {\n      const headers = this._parseHeaders(xhr.getAllResponseHeaders());\n      const response = this._transformResponse(xhr.response, headers);\n      const gist = this._isSuccessCode(xhr.status) ? 'Success' : 'Error';\n      const method = `_on${gist}Item`;\n      this[method](item, response, xhr.status, headers);\n      this._onCompleteItem(item, response, xhr.status, headers);\n    };\n    xhr.onerror = () => {\n      const headers = this._parseHeaders(xhr.getAllResponseHeaders());\n      const response = this._transformResponse(xhr.response, headers);\n      this._onErrorItem(item, response, xhr.status, headers);\n      this._onCompleteItem(item, response, xhr.status, headers);\n    };\n    xhr.onabort = () => {\n      const headers = this._parseHeaders(xhr.getAllResponseHeaders());\n      const response = this._transformResponse(xhr.response, headers);\n      this._onCancelItem(item, response, xhr.status, headers);\n      this._onCompleteItem(item, response, xhr.status, headers);\n    };\n    if (item.method && item.url) {\n      xhr.open(item.method, item.url, true);\n    }\n    xhr.withCredentials = item.withCredentials;\n    if (this.options.headers) {\n      for (const header of this.options.headers) {\n        xhr.setRequestHeader(header.name, header.value);\n      }\n    }\n    if (item.headers.length) {\n      for (const header of item.headers) {\n        xhr.setRequestHeader(header.name, header.value);\n      }\n    }\n    if (this.authToken && this.authTokenHeader) {\n      xhr.setRequestHeader(this.authTokenHeader, this.authToken);\n    }\n    xhr.onreadystatechange = function () {\n      if (xhr.readyState == XMLHttpRequest.DONE) {\n        that.response.emit(xhr.responseText);\n      }\n    };\n    if (this.options.formatDataFunctionIsAsync) {\n      sendable.then(result => xhr.send(JSON.stringify(result)));\n    } else {\n      xhr.send(sendable);\n    }\n    this._render();\n  }\n  _getTotalProgress(value = 0) {\n    if (this.options.removeAfterUpload) {\n      return value;\n    }\n    const notUploaded = this.getNotUploadedItems().length;\n    const uploaded = notUploaded ? this.queue.length - notUploaded : this.queue.length;\n    const ratio = 100 / this.queue.length;\n    const current = value * ratio / 100;\n    return Math.round(uploaded * ratio + current);\n  }\n  _getFilters(filters) {\n    if (!filters) {\n      return this.options?.filters || [];\n    }\n    if (Array.isArray(filters)) {\n      return filters;\n    }\n    if (typeof filters === 'string') {\n      const names = filters.match(/[^\\s,]+/g);\n      return this.options?.filters || [].filter(filter => names?.indexOf(filter.name) !== -1);\n    }\n    return this.options?.filters || [];\n  }\n  _render() {\n    return void 0;\n  }\n  _queueLimitFilter() {\n    return this.options.queueLimit === undefined || this.queue.length < this.options.queueLimit;\n  }\n  _isValidFile(file, filters, options) {\n    this._failFilterIndex = -1;\n    return !filters.length ? true : filters.every(filter => {\n      if (typeof this._failFilterIndex === 'number') {\n        this._failFilterIndex++;\n      }\n      return filter.fn.call(this, file, options);\n    });\n  }\n  _isSuccessCode(status) {\n    return status >= 200 && status < 300 || status === 304;\n  }\n  _transformResponse(response, headers) {\n    return response;\n  }\n  _parseHeaders(headers) {\n    const parsed = {};\n    let key;\n    let val;\n    let i;\n    if (!headers) {\n      return parsed;\n    }\n    headers.split('\\n').map(line => {\n      i = line.indexOf(':');\n      key = line.slice(0, i).trim().toLowerCase();\n      val = line.slice(i + 1).trim();\n      if (key) {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    });\n    return parsed;\n  }\n  _onWhenAddingFileFailed(item, filter, options) {\n    this.onWhenAddingFileFailed(item, filter, options);\n  }\n  _onAfterAddingFile(item) {\n    this.onAfterAddingFile(item);\n  }\n  _onAfterAddingAll(items) {\n    this.onAfterAddingAll(items);\n  }\n  _onBeforeUploadItem(item) {\n    item._onBeforeUpload();\n    this.onBeforeUploadItem(item);\n  }\n  _onBuildItemForm(item, form) {\n    item._onBuildForm(form);\n    this.onBuildItemForm(item, form);\n  }\n  _onProgressItem(item, progress) {\n    const total = this._getTotalProgress(progress);\n    this.progress = total;\n    item._onProgress(progress);\n    this.onProgressItem(item, progress);\n    this.onProgressAll(total);\n    this._render();\n  }\n  _onSuccessItem(item, response, status, headers) {\n    item._onSuccess(response, status, headers);\n    this.onSuccessItem(item, response, status, headers);\n  }\n  _onCancelItem(item, response, status, headers) {\n    item._onCancel(response, status, headers);\n    this.onCancelItem(item, response, status, headers);\n  }\n}\nclass FileDropDirective {\n  constructor(element) {\n    this.fileOver = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-on-prefix\n    this.onFileDrop = new EventEmitter();\n    this.element = element;\n  }\n  getOptions() {\n    return this.uploader?.options;\n  }\n  getFilters() {\n    return '';\n  }\n  onDrop(event) {\n    const transfer = this._getTransfer(event);\n    if (!transfer) {\n      return;\n    }\n    const options = this.getOptions();\n    const filters = this.getFilters();\n    this._preventAndStop(event);\n    if (options) {\n      this.uploader?.addToQueue(transfer.files, options, filters);\n    }\n    this.fileOver.emit(false);\n    this.onFileDrop.emit(transfer.files);\n  }\n  onDragOver(event) {\n    const transfer = this._getTransfer(event);\n    if (!this._haveFiles(transfer.types)) {\n      return;\n    }\n    transfer.dropEffect = 'copy';\n    this._preventAndStop(event);\n    this.fileOver.emit(true);\n  }\n  onDragLeave(event) {\n    if (this.element) {\n      if (event.currentTarget === this.element[0]) {\n        return;\n      }\n    }\n    this._preventAndStop(event);\n    this.fileOver.emit(false);\n  }\n  _getTransfer(event) {\n    return event.dataTransfer ? event.dataTransfer : event.originalEvent.dataTransfer; // jQuery fix;\n  }\n  _preventAndStop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  _haveFiles(types) {\n    if (!types) {\n      return false;\n    }\n    if (types.indexOf) {\n      return types.indexOf('Files') !== -1;\n    } else if (types.contains) {\n      return types.contains('Files');\n    } else {\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function FileDropDirective_Factory(t) {\n      return new (t || FileDropDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FileDropDirective,\n      selectors: [[\"\", \"ng2FileDrop\", \"\"]],\n      hostBindings: function FileDropDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function FileDropDirective_drop_HostBindingHandler($event) {\n            return ctx.onDrop($event);\n          })(\"dragover\", function FileDropDirective_dragover_HostBindingHandler($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function FileDropDirective_dragleave_HostBindingHandler($event) {\n            return ctx.onDragLeave($event);\n          });\n        }\n      },\n      inputs: {\n        uploader: \"uploader\"\n      },\n      outputs: {\n        fileOver: \"fileOver\",\n        onFileDrop: \"onFileDrop\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FileDropDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng2FileDrop]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    uploader: [{\n      type: Input\n    }],\n    fileOver: [{\n      type: Output\n    }],\n    onFileDrop: [{\n      type: Output\n    }],\n    onDrop: [{\n      type: HostListener,\n      args: ['drop', ['$event']]\n    }],\n    onDragOver: [{\n      type: HostListener,\n      args: ['dragover', ['$event']]\n    }],\n    onDragLeave: [{\n      type: HostListener,\n      args: ['dragleave', ['$event']]\n    }]\n  });\n})();\nclass FileSelectDirective {\n  constructor(element) {\n    // eslint-disable-next-line @angular-eslint/no-output-on-prefix\n    this.onFileSelected = new EventEmitter();\n    this.element = element;\n  }\n  getOptions() {\n    return this.uploader?.options;\n  }\n  getFilters() {\n    return '';\n  }\n  isEmptyAfterSelection() {\n    return !!this.element.nativeElement.attributes.multiple;\n  }\n  onChange() {\n    const files = this.element.nativeElement.files;\n    const options = this.getOptions();\n    const filters = this.getFilters();\n    this.uploader?.addToQueue(files, options, filters);\n    this.onFileSelected.emit(files);\n    if (this.isEmptyAfterSelection()) {\n      this.element.nativeElement.value = '';\n    }\n  }\n  static {\n    this.ɵfac = function FileSelectDirective_Factory(t) {\n      return new (t || FileSelectDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FileSelectDirective,\n      selectors: [[\"\", \"ng2FileSelect\", \"\"]],\n      hostBindings: function FileSelectDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function FileSelectDirective_change_HostBindingHandler() {\n            return ctx.onChange();\n          });\n        }\n      },\n      inputs: {\n        uploader: \"uploader\"\n      },\n      outputs: {\n        onFileSelected: \"onFileSelected\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FileSelectDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng2FileSelect]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    uploader: [{\n      type: Input\n    }],\n    onFileSelected: [{\n      type: Output\n    }],\n    onChange: [{\n      type: HostListener,\n      args: ['change']\n    }]\n  });\n})();\nclass FileUploadModule {\n  static {\n    this.ɵfac = function FileUploadModule_Factory(t) {\n      return new (t || FileUploadModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FileUploadModule,\n      declarations: [FileDropDirective, FileSelectDirective],\n      imports: [CommonModule],\n      exports: [FileDropDirective, FileSelectDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FileUploadModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [FileDropDirective, FileSelectDirective],\n      exports: [FileDropDirective, FileSelectDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FileDropDirective, FileItem, FileLikeObject, FileSelectDirective, FileUploadModule, FileUploader };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,aAAa;AACvB,SAAK,UAAU;AACf,UAAM,mBAAmB,uBAAuB,mBAAmB,YAAY,QAAQ;AACvF,UAAM,UAAU,OAAO,qBAAqB,WAAW,aAAa;AACpE,UAAM,SAAS,cAAc,OAAO;AACpC,SAAK,MAAM,EAAE,gBAAgB;AAAA,EAC/B;AAAA,EACA,oBAAoB,MAAM;AACxB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AACZ,SAAK,OAAO,QAAQ,KAAK,MAAM,KAAK,YAAY,GAAG,IAAI,CAAC,EAAE,YAAY,CAAC;AACvE,SAAK,OAAO,KAAK,MAAM,KAAK,YAAY,GAAG,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC;AAAA,EAC3E;AAAA,EACA,kBAAkB,QAAQ;AACxB,SAAK,OAAO,OAAO;AACnB,SAAK,OAAO,OAAO;AACnB,SAAK,OAAO,OAAO;AAAA,EACrB;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,UAAU,MAAM,SAAS;AACnC,SAAK,MAAM;AACX,SAAK,UAAU,CAAC;AAChB,SAAK,kBAAkB;AACvB,SAAK,WAAW,CAAC;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO,IAAI,eAAe,IAAI;AACnC,SAAK,QAAQ;AACb,QAAI,SAAS,SAAS;AACpB,WAAK,SAAS,SAAS,QAAQ,UAAU;AACzC,WAAK,QAAQ,SAAS,QAAQ,aAAa;AAAA,IAC7C;AACA,SAAK,MAAM,SAAS,QAAQ;AAAA,EAC9B;AAAA,EACA,SAAS;AACP,QAAI;AACF,WAAK,SAAS,WAAW,IAAI;AAAA,IAC/B,SAAS,GAAG;AACV,WAAK,SAAS,gBAAgB,MAAM,IAAI,GAAG,CAAC,CAAC;AAC7C,WAAK,SAAS,aAAa,MAAM,IAAI,GAAG,CAAC,CAAC;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,SAAS,WAAW,IAAI;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,SAAK,SAAS,gBAAgB,IAAI;AAAA,EACpC;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,UAAU,QAAQ,SAAS;AACnC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,UAAU,QAAQ,SAAS;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,UAAU,QAAQ,SAAS;AAClC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,UAAU,QAAQ,SAAS;AACpC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,aAAa,MAAM;AACjB,SAAK,YAAY,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,WAAW,QAAQ;AAAA,EAC1B;AAAA,EACA,WAAW,UAAU,QAAQ,SAAS;AACpC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,UAAU,UAAU,QAAQ,OAAO;AAAA,EAC1C;AAAA,EACA,SAAS,UAAU,QAAQ,SAAS;AAClC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ,UAAU,QAAQ,OAAO;AAAA,EACxC;AAAA,EACA,UAAU,UAAU,QAAQ,SAAS;AACnC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS,UAAU,QAAQ,OAAO;AAAA,EACzC;AAAA,EACA,YAAY,UAAU,QAAQ,SAAS;AACrC,SAAK,WAAW,UAAU,QAAQ,OAAO;AACzC,QAAI,KAAK,SAAS,QAAQ,mBAAmB;AAC3C,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,SAAK,QAAQ,KAAK,SAAS,EAAE,KAAK,SAAS;AAC3C,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EAGb,OAAO;AACL,SAAK,WAAW,CAAC,sBAAsB,sBAAsB,2EAA2E,2EAA2E,oDAAoD,kDAAkD;AAAA,EAC3T;AAAA,EAEA,OAAO;AACL,SAAK,WAAW,CAAC,4BAA4B,4BAA4B,4BAA4B,qEAAqE,wEAAwE,kDAAkD,qDAAqD,kDAAkD,uDAAuD;AAAA,EACpc;AAAA,EAEA,OAAO;AACL,SAAK,WAAW,CAAC,iCAAiC,iCAAiC,iCAAiC,iCAAiC,6EAA6E,yEAAyE,0EAA0E,uDAAuD,8DAA8D,8DAA8D,yDAAyD;AAAA,EACnmB;AAAA,EAGA,OAAO;AACL,SAAK,WAAW,CAAC,mBAAmB,qBAAqB,aAAa,yBAAyB,mBAAmB,gCAAgC;AAAA,EACpJ;AAAA,EAGA,OAAO;AACL,SAAK,gBAAgB,CAAC,sBAAsB,2BAA2B,wBAAwB,qBAAqB,gCAAgC,4BAA4B,gCAAgC,8BAA8B,+BAA+B,oBAAoB,qBAAqB;AAAA,EACxT;AAAA,EACA,OAAO,aAAa,MAAM;AACxB,QAAI,YAAY;AAChB,QAAI,MAAM,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM,IAAI;AACzD,kBAAY;AAAA,IACd,WAAW,MAAM,MAAM,MAAM,SAAS,GAAG;AACvC,kBAAY;AAAA,IACd,WAAW,MAAM,MAAM,MAAM,SAAS,GAAG;AACvC,kBAAY;AAAA,IACd,WAAW,MAAM,MAAM,MAAM,SAAS,GAAG;AACvC,kBAAY;AAAA,IACd,WAAW,MAAM,SAAS,mBAAmB;AAC3C,kBAAY;AAAA,IACd,WAAW,MAAM,QAAQ,KAAK,cAAc,QAAQ,KAAK,IAAI,MAAM,IAAI;AACrE,kBAAY;AAAA,IACd,WAAW,MAAM,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM,IAAI;AAChE,kBAAY;AAAA,IACd,WAAW,MAAM,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM,IAAI;AAChE,kBAAY;AAAA,IACd,WAAW,MAAM,QAAQ,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM,IAAI;AAChE,kBAAY;AAAA,IACd;AACA,QAAI,cAAc,iBAAiB,MAAM,MAAM;AAC7C,kBAAY,KAAK,kBAAkB,KAAK,IAAI;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,kBAAkB,eAAe;AACtC,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,UAAM,SAAS,cAAc,MAAM,GAAG;AACtC,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,UAAM,YAAY,OAAO,OAAO,SAAS,CAAC,EAAE,YAAY;AACxD,QAAI,MAAM,SAAS,MAAM,QAAW;AAClC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,MAAM,SAAS;AAAA,IACxB;AAAA,EACF;AACF;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,QAAQ,iBAAiB;AAClC;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,cAAc;AACnB,SAAK,QAAQ,CAAC;AACd,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS,CAAC;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,oBAAoB,UAAQ,KAAK;AAAA,MACjC,2BAA2B;AAAA,MAC3B,KAAK;AAAA,IACP;AACA,SAAK,WAAW,OAAO;AACvB,SAAK,WAAW,IAAI,aAAa;AAAA,EACnC;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU,OAAO,OAAO,KAAK,SAAS,OAAO;AAClD,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,kBAAkB,KAAK,QAAQ,mBAAmB;AACvD,SAAK,aAAa,KAAK,QAAQ;AAC/B,SAAK,QAAQ,SAAS,QAAQ;AAAA,MAC5B,MAAM;AAAA,MACN,IAAI,KAAK;AAAA,IACX,CAAC;AACD,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,QAAQ,SAAS,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,IAAI,KAAK;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,KAAK,QAAQ,iBAAiB;AAChC,WAAK,QAAQ,SAAS,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,IAAI,KAAK;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,KAAK,QAAQ,iBAAiB;AAChC,WAAK,QAAQ,SAAS,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,IAAI,KAAK;AAAA,MACX,CAAC;AAAA,IACH;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,WAAK,MAAM,CAAC,EAAE,MAAM,KAAK,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA,EACA,WAAW,OAAO,UAAU,SAAS;AACnC,QAAI,UAAU;AACd,UAAM,OAAO,CAAC;AACd,eAAW,QAAQ,OAAO;AACxB,WAAK,KAAK,IAAI;AAAA,IAChB;AACA,UAAM,iBAAiB,KAAK,YAAY,OAAO;AAC/C,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,iBAAiB,CAAC;AACxB,SAAK,IAAI,UAAQ;AACf,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK;AAAA,MACjB;AACA,YAAM,OAAO,IAAI,eAAe,IAAI;AACpC,UAAI,KAAK,aAAa,MAAM,gBAAgB,OAAO,GAAG;AACpD,cAAM,WAAW,IAAI,SAAS,MAAM,MAAM,OAAO;AACjD,uBAAe,KAAK,QAAQ;AAC5B,aAAK,MAAM,KAAK,QAAQ;AACxB,aAAK,mBAAmB,QAAQ;AAAA,MAClC,OAAO;AACL,YAAI,OAAO,KAAK,qBAAqB,YAAY,KAAK,oBAAoB,GAAG;AAC3E,gBAAM,SAAS,eAAe,KAAK,gBAAgB;AACnD,eAAK,wBAAwB,MAAM,QAAQ,OAAO;AAAA,QACpD;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,KAAK,MAAM,WAAW,OAAO;AAC/B,WAAK,kBAAkB,cAAc;AACrC,WAAK,WAAW,KAAK,kBAAkB;AAAA,IACzC;AACA,SAAK,QAAQ;AACb,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,QAAQ,KAAK,eAAe,KAAK;AACvC,UAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,QAAI,KAAK,aAAa;AACpB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,MAAM,OAAO,OAAO,CAAC;AAC1B,SAAK,WAAW,KAAK,kBAAkB;AAAA,EACzC;AAAA,EACA,aAAa;AACX,WAAO,KAAK,MAAM,QAAQ;AACxB,WAAK,MAAM,CAAC,EAAE,OAAO;AAAA,IACvB;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,QAAQ,KAAK,eAAe,KAAK;AACvC,UAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,UAAM,YAAY,KAAK,QAAQ,UAAU,kBAAkB;AAC3D,SAAK,oBAAoB;AACzB,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,SAAS,EAAE,IAAI;AAAA,EACtB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,QAAQ,KAAK,eAAe,KAAK;AACvC,UAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,UAAM,OAAO,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK;AACrD,QAAI,QAAQ,KAAK,aAAa;AAC5B,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY;AACV,UAAM,QAAQ,KAAK,oBAAoB,EAAE,OAAO,UAAQ,CAAC,KAAK,WAAW;AACzE,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AACA,UAAM,IAAI,UAAQ,KAAK,oBAAoB,CAAC;AAC5C,UAAM,CAAC,EAAE,OAAO;AAAA,EAClB;AAAA,EACA,YAAY;AACV,UAAM,QAAQ,KAAK,oBAAoB;AACvC,UAAM,IAAI,UAAQ,KAAK,OAAO,CAAC;AAAA,EACjC;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,OAAO,KAAK;AAAA,EACrB;AAAA,EACA,iBAAiB,OAAO;AACtB,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,EACrE;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM,OAAO,UAAQ,CAAC,KAAK,UAAU;AAAA,EACnD;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,KAAK,WAAW,EAAE,KAAK,CAAC,OAAO,UAAU,MAAM,QAAQ,MAAM,KAAK;AAAA,EACtH;AAAA,EACA,iBAAiB,WAAW;AAC1B,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,MAAM;AAC9B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,UAAU;AAC1B,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,MAAM,QAAQ,SAAS;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB,UAAU;AAC3B,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,UAAU,UAAU;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,UAAU;AACtB,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,MAAM,UAAU,QAAQ,SAAS;AAC7C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,MAAM,UAAU,QAAQ,SAAS;AAC3C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,MAAM,UAAU,QAAQ,SAAS;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,MAAM,UAAU,QAAQ,SAAS;AAC9C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,EAAE,MAAM,QAAQ,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,QAAQ,KAAK,IAAI,MAAM;AAAA,EAC9G;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,EAAE,KAAK,QAAQ,eAAe,KAAK,OAAO,KAAK,QAAQ;AAAA,EAChE;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,EAAE,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,gBAAgB,QAAQ,SAAS,aAAa,IAAI,CAAC,MAAM;AAAA,EACjH;AAAA,EACA,aAAa,MAAM,UAAU,QAAQ,SAAS;AAC5C,SAAK,SAAS,UAAU,QAAQ,OAAO;AACvC,SAAK,YAAY,MAAM,UAAU,QAAQ,OAAO;AAAA,EAClD;AAAA,EACA,gBAAgB,MAAM,UAAU,QAAQ,SAAS;AAC/C,SAAK,YAAY,UAAU,QAAQ,OAAO;AAC1C,SAAK,eAAe,MAAM,UAAU,QAAQ,OAAO;AACnD,UAAM,WAAW,KAAK,cAAc,EAAE,CAAC;AACvC,SAAK,cAAc;AACnB,QAAI,UAAU;AACZ,eAAS,OAAO;AAChB;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,WAAW,KAAK,kBAAkB;AACvC,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,UAAQ;AACb,UAAI,MAAM;AACR,eAAO,cAAc,KAAK,YAAY,CAAC,KAAK;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc,MAAM;AAGlB,UAAM,OAAO;AACb,UAAM,MAAM,KAAK,OAAO,IAAI,eAAe;AAC3C,QAAI;AACJ,SAAK,oBAAoB,IAAI;AAC7B,QAAI,OAAO,KAAK,MAAM,SAAS,UAAU;AACvC,YAAM,IAAI,UAAU,uCAAuC;AAAA,IAC7D;AACA,QAAI,CAAC,KAAK,QAAQ,kBAAkB;AAClC,iBAAW,IAAI,SAAS;AACxB,WAAK,iBAAiB,MAAM,QAAQ;AACpC,YAAM,aAAa,MAAM,SAAS,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK,IAAI;AAC/E,UAAI,CAAC,KAAK,QAAQ,uBAAuB;AACvC,mBAAW;AAAA,MACb;AAEA,UAAI,KAAK,QAAQ,wBAAwB,QAAW;AAClD,eAAO,KAAK,KAAK,QAAQ,mBAAmB,EAAE,QAAQ,SAAO;AAC3D,cAAI,WAAW,KAAK,QAAQ,sBAAsB,GAAG;AAErD,cAAI,OAAO,aAAa,YAAY,SAAS,QAAQ,eAAe,KAAK,KAAK,KAAK,MAAM,MAAM;AAC7F,uBAAW,SAAS,QAAQ,iBAAiB,KAAK,KAAK,IAAI;AAAA,UAC7D;AACA,mBAAS,OAAO,KAAK,QAAQ;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,UAAI,cAAc,KAAK,QAAQ,uBAAuB;AACpD,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,UAAI,KAAK,QAAQ,oBAAoB;AACnC,mBAAW,KAAK,QAAQ,mBAAmB,IAAI;AAAA,MACjD;AAAA,IACF;AACA,QAAI,OAAO,aAAa,WAAS;AAC/B,YAAM,WAAW,KAAK,MAAM,MAAM,mBAAmB,MAAM,SAAS,MAAM,MAAM,QAAQ,CAAC;AACzF,WAAK,gBAAgB,MAAM,QAAQ;AAAA,IACrC;AACA,QAAI,SAAS,MAAM;AACjB,YAAM,UAAU,KAAK,cAAc,IAAI,sBAAsB,CAAC;AAC9D,YAAM,WAAW,KAAK,mBAAmB,IAAI,UAAU,OAAO;AAC9D,YAAM,OAAO,KAAK,eAAe,IAAI,MAAM,IAAI,YAAY;AAC3D,YAAM,SAAS,MAAM,IAAI;AACzB,WAAK,MAAM,EAAE,MAAM,UAAU,IAAI,QAAQ,OAAO;AAChD,WAAK,gBAAgB,MAAM,UAAU,IAAI,QAAQ,OAAO;AAAA,IAC1D;AACA,QAAI,UAAU,MAAM;AAClB,YAAM,UAAU,KAAK,cAAc,IAAI,sBAAsB,CAAC;AAC9D,YAAM,WAAW,KAAK,mBAAmB,IAAI,UAAU,OAAO;AAC9D,WAAK,aAAa,MAAM,UAAU,IAAI,QAAQ,OAAO;AACrD,WAAK,gBAAgB,MAAM,UAAU,IAAI,QAAQ,OAAO;AAAA,IAC1D;AACA,QAAI,UAAU,MAAM;AAClB,YAAM,UAAU,KAAK,cAAc,IAAI,sBAAsB,CAAC;AAC9D,YAAM,WAAW,KAAK,mBAAmB,IAAI,UAAU,OAAO;AAC9D,WAAK,cAAc,MAAM,UAAU,IAAI,QAAQ,OAAO;AACtD,WAAK,gBAAgB,MAAM,UAAU,IAAI,QAAQ,OAAO;AAAA,IAC1D;AACA,QAAI,KAAK,UAAU,KAAK,KAAK;AAC3B,UAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI;AAAA,IACtC;AACA,QAAI,kBAAkB,KAAK;AAC3B,QAAI,KAAK,QAAQ,SAAS;AACxB,iBAAW,UAAU,KAAK,QAAQ,SAAS;AACzC,YAAI,iBAAiB,OAAO,MAAM,OAAO,KAAK;AAAA,MAChD;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,QAAQ;AACvB,iBAAW,UAAU,KAAK,SAAS;AACjC,YAAI,iBAAiB,OAAO,MAAM,OAAO,KAAK;AAAA,MAChD;AAAA,IACF;AACA,QAAI,KAAK,aAAa,KAAK,iBAAiB;AAC1C,UAAI,iBAAiB,KAAK,iBAAiB,KAAK,SAAS;AAAA,IAC3D;AACA,QAAI,qBAAqB,WAAY;AACnC,UAAI,IAAI,cAAc,eAAe,MAAM;AACzC,aAAK,SAAS,KAAK,IAAI,YAAY;AAAA,MACrC;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,2BAA2B;AAC1C,eAAS,KAAK,YAAU,IAAI,KAAK,KAAK,UAAU,MAAM,CAAC,CAAC;AAAA,IAC1D,OAAO;AACL,UAAI,KAAK,QAAQ;AAAA,IACnB;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,kBAAkB,QAAQ,GAAG;AAC3B,QAAI,KAAK,QAAQ,mBAAmB;AAClC,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,oBAAoB,EAAE;AAC/C,UAAM,WAAW,cAAc,KAAK,MAAM,SAAS,cAAc,KAAK,MAAM;AAC5E,UAAM,QAAQ,MAAM,KAAK,MAAM;AAC/B,UAAM,UAAU,QAAQ,QAAQ;AAChC,WAAO,KAAK,MAAM,WAAW,QAAQ,OAAO;AAAA,EAC9C;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,SAAS;AACZ,aAAO,KAAK,SAAS,WAAW,CAAC;AAAA,IACnC;AACA,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,QAAQ,QAAQ,MAAM,UAAU;AACtC,aAAO,KAAK,SAAS,WAAW,CAAC,EAAE,OAAO,YAAU,OAAO,QAAQ,OAAO,IAAI,MAAM,EAAE;AAAA,IACxF;AACA,WAAO,KAAK,SAAS,WAAW,CAAC;AAAA,EACnC;AAAA,EACA,UAAU;AACR,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,QAAQ,eAAe,UAAa,KAAK,MAAM,SAAS,KAAK,QAAQ;AAAA,EACnF;AAAA,EACA,aAAa,MAAM,SAAS,SAAS;AACnC,SAAK,mBAAmB;AACxB,WAAO,CAAC,QAAQ,SAAS,OAAO,QAAQ,MAAM,YAAU;AACtD,UAAI,OAAO,KAAK,qBAAqB,UAAU;AAC7C,aAAK;AAAA,MACP;AACA,aAAO,OAAO,GAAG,KAAK,MAAM,MAAM,OAAO;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,UAAU,OAAO,SAAS,OAAO,WAAW;AAAA,EACrD;AAAA,EACA,mBAAmB,UAAU,SAAS;AACpC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,SAAS,CAAC;AAChB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,IAAI,EAAE,IAAI,UAAQ;AAC9B,UAAI,KAAK,QAAQ,GAAG;AACpB,YAAM,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,YAAY;AAC1C,YAAM,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK;AAC7B,UAAI,KAAK;AACP,eAAO,GAAG,IAAI,OAAO,GAAG,IAAI,OAAO,GAAG,IAAI,OAAO,MAAM;AAAA,MACzD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,MAAM,QAAQ,SAAS;AAC7C,SAAK,uBAAuB,MAAM,QAAQ,OAAO;AAAA,EACnD;AAAA,EACA,mBAAmB,MAAM;AACvB,SAAK,kBAAkB,IAAI;AAAA,EAC7B;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA,EACA,oBAAoB,MAAM;AACxB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB,IAAI;AAAA,EAC9B;AAAA,EACA,iBAAiB,MAAM,MAAM;AAC3B,SAAK,aAAa,IAAI;AACtB,SAAK,gBAAgB,MAAM,IAAI;AAAA,EACjC;AAAA,EACA,gBAAgB,MAAM,UAAU;AAC9B,UAAM,QAAQ,KAAK,kBAAkB,QAAQ;AAC7C,SAAK,WAAW;AAChB,SAAK,YAAY,QAAQ;AACzB,SAAK,eAAe,MAAM,QAAQ;AAClC,SAAK,cAAc,KAAK;AACxB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,eAAe,MAAM,UAAU,QAAQ,SAAS;AAC9C,SAAK,WAAW,UAAU,QAAQ,OAAO;AACzC,SAAK,cAAc,MAAM,UAAU,QAAQ,OAAO;AAAA,EACpD;AAAA,EACA,cAAc,MAAM,UAAU,QAAQ,SAAS;AAC7C,SAAK,UAAU,UAAU,QAAQ,OAAO;AACxC,SAAK,aAAa,MAAM,UAAU,QAAQ,OAAO;AAAA,EACnD;AACF;AACA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,SAAS;AACnB,SAAK,WAAW,IAAI,aAAa;AAEjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,WAAW,KAAK,aAAa,KAAK;AACxC,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,UAAU,KAAK,WAAW;AAChC,SAAK,gBAAgB,KAAK;AAC1B,QAAI,SAAS;AACX,WAAK,UAAU,WAAW,SAAS,OAAO,SAAS,OAAO;AAAA,IAC5D;AACA,SAAK,SAAS,KAAK,KAAK;AACxB,SAAK,WAAW,KAAK,SAAS,KAAK;AAAA,EACrC;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,WAAW,KAAK,aAAa,KAAK;AACxC,QAAI,CAAC,KAAK,WAAW,SAAS,KAAK,GAAG;AACpC;AAAA,IACF;AACA,aAAS,aAAa;AACtB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,SAAS;AAChB,UAAI,MAAM,kBAAkB,KAAK,QAAQ,CAAC,GAAG;AAC3C;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB,KAAK;AAC1B,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,aAAa,OAAO;AAClB,WAAO,MAAM,eAAe,MAAM,eAAe,MAAM,cAAc;AAAA,EACvE;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,MAAM,SAAS;AACjB,aAAO,MAAM,QAAQ,OAAO,MAAM;AAAA,IACpC,WAAW,MAAM,UAAU;AACzB,aAAO,MAAM,SAAS,OAAO;AAAA,IAC/B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,UAAU,CAAC;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,SAAS,0CAA0C,QAAQ;AAC/E,mBAAO,IAAI,OAAO,MAAM;AAAA,UAC1B,CAAC,EAAE,YAAY,SAAS,8CAA8C,QAAQ;AAC5E,mBAAO,IAAI,WAAW,MAAM;AAAA,UAC9B,CAAC,EAAE,aAAa,SAAS,+CAA+C,QAAQ;AAC9E,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAAA,IAC3B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAAA,IAC/B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,SAAS;AAEnB,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,WAAO,CAAC,CAAC,KAAK,QAAQ,cAAc,WAAW;AAAA,EACjD;AAAA,EACA,WAAW;AACT,UAAM,QAAQ,KAAK,QAAQ,cAAc;AACzC,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,UAAU,KAAK,WAAW;AAChC,SAAK,UAAU,WAAW,OAAO,SAAS,OAAO;AACjD,SAAK,eAAe,KAAK,KAAK;AAC9B,QAAI,KAAK,sBAAsB,GAAG;AAChC,WAAK,QAAQ,cAAc,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,UAAU,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,UAAU,SAAS,gDAAgD;AAC/E,mBAAO,IAAI,SAAS;AAAA,UACtB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,mBAAmB,mBAAmB;AAAA,MACrD,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,mBAAmB,mBAAmB;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,mBAAmB,mBAAmB;AAAA,MACrD,SAAS,CAAC,mBAAmB,mBAAmB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}