import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Toast {
    message: string;
    type: 'success' | 'danger' | 'info' | 'warning';
    customClass: string;
    header: string;
    delay?: number;
}

@Injectable({
    providedIn: 'root',
})
export class ToastService {
    private toastsSubject = new BehaviorSubject<Toast[]>([]);
    toasts$ = this.toastsSubject.asObservable();

    private show(
        message: string,
        type: 'success' | 'danger' | 'info' | 'warning',
        customClass: string,
        header: string,
        delay: number = 5000000,
    ) {
        const newToast: Toast = { message, type, customClass, header, delay };
        this.toastsSubject.next([...this.toastsSubject.getValue(), newToast]);
    }

    success(message: string, header?: string): void {
        this.show(message, 'success', 'bg-success text-light', header ?? 'Alert', 10000);
    }

    error(message: string, header?: string): void {
        this.show(message, 'danger', 'bg-danger text-light', header ?? 'Error', 10000);
    }

    info(message: string, header?: string): void {
        this.show(message, 'info', 'bg-success text-light', header ?? 'Alert', 10000);
    }

    warning(message: string, header?: string): void {
        this.show(message, 'warning', 'bg-success text-light', header ?? 'Alert', 10000);
    }

    remove(toast: Toast) {
        const toasts = this.toastsSubject.getValue().filter((t) => t !== toast);
        this.toastsSubject.next(toasts);
    }
}
