<div class="site-main-container">
    <div class="position-relative">
        <div class="dashboard-main-filter-section px-0">
            <div class="custom-input-group custom-search-bar-outer mb-sm-0">
                <input class="form-control search-form-control custom-search-bar"
                    placeholder="Search By Name and Email..." appDelayedInput #searchInput
                    (delayedInput)="search($event)" [delayTime]="1000">
                <i class="bi bi-search"></i>
            </div>

            <!-- Buttons -->
            <div class="d-flex flex-sm-row gap-2">
                <!-- Filter Button -->
                <!-- <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect>
                    <span>
                        <img src="/assets/images/icons/Filter_icon.svg" alt="filter-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline fw-medium ms-1 me-3 custom-text-button">{{"COMMON.FILTER" |
                        translate}}
                    </span>
                </button> -->

                <!-- Export to Excel Button  -->
                <button type="button" class="btn btn-primary custom-medium-button" appRippleEffect
                    [class.d-none]="records.length < 1" (click)="exportToExcel(filterParam)">
                    <span class="text-center">
                        <img src="/assets/images/icons/export.svg" alt="export-icon" loading="eager">
                    </span>

                    <span class="d-sm-none d-md-inline mx-1 custom-text-button">{{"UserPermission.export" |
                        translate}}</span>
                </button>
            </div>
        </div>

        <!-- Accordion -->
    </div>

    <!-- Table Container -->
    <div class="col-md-12">
        <!-- Table Layout -->
        <div class="site-table-container">
            <div class="table-responsive" [ngClass]="{ 'has-records': contacts
            .length > 0 }">
                <table class="table table-hover site-table-layout" datatable [dtOptions]="dtOptions"
                    [dtTrigger]="dtTrigger">
                    <thead class="table-head">
                        <tr>
                            <th>{{'CUSTOMER.companyName' | translate }}</th>
                            <th>{{'Contacts.personName' | translate }}</th>
                            <th class="width-150">{{'Contacts.contact' | translate}} </th>
                            <th>{{'Contacts.email' | translate }}</th>
                            <th>{{'Contacts.subject' | translate }}</th>
                            <th class="width-300">{{'Contacts.message' | translate }}</th>
                            <th class="text-center">{{'Contacts.status' | translate}}</th>
                            <th class="width-100">{{'COMMON.CREATED_ON' | translate}}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!-- Table Body -->
        <div class="table-container-body">
            <ng-template #companyName let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]=" data?.companyName" ngbTooltip>{{ data?.companyName
                    }}</span>
            </ng-template>

            <ng-template #personName let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]=" data?.fullName" ngbTooltip>{{ data?.fullName
                    }}</span>
            </ng-template>

            <ng-template #contact let-data="adtData">
                <span>{{ data?.phone | mask:phoneMaskPattern }}</span>
            </ng-template>

            <ng-template #email let-data="adtData">
                <span class="text-center ellipsis-2-line" [appTooltipEllipsis]="data?.email" ngbTooltip>{{ data?.email
                    }}</span>
            </ng-template>

            <ng-template #description let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]="data?.message" ngbTooltip>{{ data?.message }}</span>
            </ng-template>

            <ng-template #subject let-data="adtData">
                <span class="ellipsis-2-line" [appTooltipEllipsis]="data?.subject" ngbTooltip>{{ data?.subject }}</span>
            </ng-template>

            <ng-template #status let-data="adtData">
                <div class="d-flex align-items-center justify-content-center">
                    <span [appStatusBadge]="data.status| removeUnderscore" (click)="openModal(content,data)"
                        [editable]="true"></span>
                </div>
            </ng-template>

            <ng-template #createdOn let-data="adtData">
                <span>{{ data.createdOn | dateFormat }}</span>
            </ng-template>

        </div>
    </div>
</div>

<ng-template #content let-modal>
    <div class="modal-content-container">
        <div class="modal-header">
            <h4 class="modal-title">{{'Contacts.changeStatus' | translate }}</h4>
            <button type="button" class="btn-close modal-close" aria-label="Close"
                (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body pt-0">
            <div class="row">

                <!-- PaymentStatus  -->
                <div class="px-md-2 mt-12">
                    <div class="form-group form-floating custom-ng-select">
                        <ng-select bindLabel="name" bindValue="id" name="StatusFilter" #ContactUs="ngModel"
                            [(ngModel)]="newContactUsStatus" [items]="contactUsStatus" required [ngClass]="{
                                        'is-invalid': !ContactUs.valid && onClickValidation 
                                      }">
                        </ng-select>
                        <label for="StatusFilter" class="ng-select-label">{{
                            "Contacts.status" | translate
                            }}</label>
                        <app-validation-message [field]="ContactUs" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4 mb-3">
                    <button class="btn btn-primary custom-small-button" (click)="updateStatus()"
                        appRippleEffect>{{"COMMON.APPLY" |
                        translate}}</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>