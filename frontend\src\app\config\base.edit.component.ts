// Angular core imports
import { Directive, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

// Third-party library imports
import { FileLikeObject, FileUploader, Headers } from '@augwit/ng2-file-upload';

// Project-specific model imports
import { Attachment } from '../models/common/attachment';
import { BaseModel } from './base.model';

// Project-specific manager imports
import { BaseManager } from './base.manager';

// Project-specific service imports
import { LoadingService } from '../services/loading.service';
import { CommonService } from '../shared/services/common.service';
import { ToastService } from '../shared/services/toast.service';

// Project-specific component imports
import { BaseComponent } from './base.component';

// Environment import
import { catchError, of, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { RestResponse } from '../models/common/auth.model';
import { FilterParam } from '../models/common/filter-param';
import { Constant } from './constants';

interface Filter {
    name: string;
    fn: (item: any) => boolean;
}

interface UploaderOptions {
    url: string;
    autoUpload: boolean;
    maxFileSize: number;
    filters: Filter[];
    headers: any;
}

@Directive()
export class BaseEditComponent extends BaseComponent {
    @Input() public onCancel!: () => void;
    @Input() recordId!: string;

    override request: any;
    record!: BaseModel;

    // isPlusButton will be removed
    isPlusButton!: boolean;

    onClickValidation!: boolean;
    isShowAssociated: boolean;

    filterParam: FilterParam;

    messages = Constant.ALERT_MESSAGES;

    constructor(
        protected override manager: BaseManager,
        protected override commonService: CommonService,
        protected override toastService: ToastService,
        protected override loadingService: LoadingService,
        protected route: ActivatedRoute,
        protected override router: Router,
        protected translateService: TranslateService,
    ) {
        super(manager, commonService, toastService, loadingService, router);
        this.isShowAssociated = false;
        this.filterParam = new FilterParam();
        this.request = {} as any;
    }

    override init() {
        this.initializeData();
    }

    save(
        form: any,
        options: {
            hasResponse?: boolean;
            isAddressRequired?: boolean;
            isDocumentRequired?: boolean;
            isPhoneNumberRequired?: boolean;
        } = {},
    ) {
        this.onClickValidation = !form.valid;
        if (!form.valid) {
            this.focusInvalidField();
            return;
        }
        const {
            hasResponse = false,
            isAddressRequired = false,
            isDocumentRequired = false,
            isPhoneNumberRequired = false,
        } = options;

        // Create a deep copy of the forRequest object to modify it for the API payload
        const forRequestData = JSON.parse(JSON.stringify(this.record.forRequest()));

        // Clean up the payload by removing addressDetail and noteDetail if they contain only isDeleted and isActive
        this.cleanPayload(forRequestData);

        // Check for documents if required
        if (forRequestData.documents && forRequestData.documents.length < 1 && isDocumentRequired) {
            return;
        }

        if (
            (!forRequestData.addressDetail?.address ||
                !forRequestData.addressDetail?.city ||
                !forRequestData.addressDetail?.state ||
                !forRequestData.addressDetail?.pin ||
                !forRequestData.addressDetail?.country) &&
            isAddressRequired
        ) {
            this.onClickValidation = true;
            return;
        }

        this.loadingService.show();

        const method = this.request.isNewRecord ? 'save' : 'update';
        this.manager[method](forRequestData, hasResponse).subscribe({
            next: (response: RestResponse) => {
                this.loadingService.hide();
                this.toastService.success(response?.message);
                this.onSaveSuccess(response.data);
            },
            error: (error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            },
        });
    }

    // Utility function to clean payload by removing specific objects
    cleanPayload(obj: any): any {
        const isEmptyObject = (value: any): boolean => {
            return typeof value === 'object' && value !== null && Object.keys(value).length === 0;
        };

        const isFlagOnly = (innerObj: any): boolean => {
            if (typeof innerObj === 'object' && innerObj !== null) {
                const keys = Object.keys(innerObj);
                return keys.length === 2 && 'isDeleted' in innerObj && 'isActive' in innerObj;
            }
            return false;
        };

        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const value = obj[key];

                // Recursively clean nested objects
                if (typeof value === 'object' && value !== null) {
                    this.cleanPayload(value); // Clean nested objects first
                }

                // Remove if value is an empty object or meets the flag condition
                if (isEmptyObject(value) || isFlagOnly(value)) {
                    delete obj[key];
                }
            }
        }

        return obj;
    }

    // remove duplicate from the single array
    isDuplicate(array: any, item: any) {
        return array.some((existingItem: { id: any }) => existingItem.id === item.id);
    }

    // remove duplicates from the multiple select array
    removeDuplicates(existingList: any[], newList: any[]) {
        return newList.filter((newItem) => !existingList.some((existingItem) => existingItem.id === newItem.id));
    }

    // method used when we push object in the existing ng-select when fetch data
    addUniqueItem(list: any[], item: any) {
        if (item?.id && !this.isDuplicate(list, item)) {
            return [...list, item];
        }

        return list;
    }

    navigate(path: string) {
        if (!this.isPlusButton && !this.isNullOrUndefined(path)) {
            this.router.navigateByUrl(path);
            return;
        }
        if (!this.isPlusButton && this.isNullOrUndefined(path)) {
            window.history.back();
            return;
        }
        this.onCancel();
    }

    initializeUploader(
        files: any,
        allowedExtensions: string,
        maxFileSize: number,
        toastService: ToastService,
        header?: Headers[],
        apiUrl?: string,
    ) {
        const uploaderOptions: UploaderOptions = {
            url: environment.BaseApiUrl + (apiUrl ?? '/api/file/group/items/upload'),
            autoUpload: true,
            maxFileSize: maxFileSize * 1024,
            filters: [],
            headers: header,
        };

        if (allowedExtensions && allowedExtensions !== '') {
            uploaderOptions.filters.push({
                name: 'extension',
                fn: (item: any): boolean => {
                    const fileExtension = item.name?.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
                    return allowedExtensions.indexOf(fileExtension) !== -1;
                },
            });
        }

        const uploader = new FileUploader(uploaderOptions);

        // Show loading indicator when file is added to the queue
        uploader.onAfterAddingFile = (item) => {
            item.withCredentials = false;
            this.loadingService.show();
        };

        uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
            this.loadingService.hide();
            switch (filter.name) {
                case 'fileSize':
                    toastService.error(
                        `Warning: The ${item.name} exceeds the maximum allowed size of 5MB. Please upload a smaller file.`,
                    );
                    break;
                case 'extension':
                    toastService.error('This file format is not supported');
            }
        };

        // Show loading indicator when file upload starts
        uploader.onBeforeUploadItem = (fileItem) => {
            this.loadingService.show(); // Show loading indicator when any file starts uploading
        };

        // Handle successful upload for each file
        uploader.onSuccessItem = (fileItem, response) => {
            const uploadResponse = JSON.parse(response);
            const uploadResponseData = uploadResponse.data;

            if (uploadResponseData.length > 0) {
                const image = uploadResponseData[0];
                image.isDeleted = false;

                // Ensure 'files' is initialized if it's not already
                if (this.isNullOrUndefined(files)) {
                    files = [] as any[];
                }

                // Add the uploaded file data to 'files'
                files.push(image);
            }
        };

        // Hide loading indicator when all files are processed
        uploader.onCompleteAll = () => {
            this.allFilesUploadSuccess();
            // Hide loading indicator only after all files have been uploaded
            this.loadingService.hide();

            // Optional: Trigger your success callback after the upload process is completed
            setTimeout(() => {
                this.onUploadSuccess();
            }, 200);
        };

        return uploader;
    }

    allFilesUploadSuccess() { }

    hasFileUploaded(files: Array<Attachment>) {
        if (!files || files.length == 0) {
            return true;
        }
        let result = files.filter((val) => {
            return !val.isDeleted;
        });
        return result.length == 0;
    }

    removeFile(file: any) {
        this.commonService.confirmation(this.messages.deleteMsg, this.removeFileCallback.bind(this), file);
    }

    removeFileCallback(file: { isDeleted: boolean }) {
        file.isDeleted = true;
    }

    setRecord(inputRecord: BaseModel) {
        this.record = inputRecord;
    }

    fetchAssociatedData() { }

    onSaveSuccess(data: any) { }

    pluginInitialization() { }

    onFetchCompleted() { }

    onUploadSuccess() { }

    focusInvalidField() {
        this.commonService.focusInvalidField();
    }

    private fetchExistingRecord(): void {
        this.manager
            .fetch(this.request.recordId)
            .pipe(
                tap((response) => {
                    this.loadingService.hide();

                    this.record = response.data;
                    this.request.isNewRecord = false;

                    this.onFetchCompleted();
                }),
                catchError((error: any) => {
                    this.toastService.error(error instanceof Error ? error.message : 'An unknown error occurred');
                    return of(null);
                }),
            )
            .subscribe();
    }

    private async initializeData() {
        // Show the loader with a small delay to ensure the DOM updates

        setTimeout(() => {
            this.loadingService.show();
        }, 0);

        this.request.recordId = this.recordId ?? this.route.snapshot.paramMap.get('id');
        this.request.isNewRecord = true;

        await this.fetchAssociatedData();

        // If no record ID, stop here and hide the loader
        if (this.request.recordId <= 0) {
            setTimeout(() => {
                this.loadingService.hide();
            }, 0);
            this.pluginInitialization();
            return;
        }

        setTimeout(() => {
            this.loadingService.show();
        }, 0);

        // Fetch the existing record if there is a recordId
        await this.fetchExistingRecord();

        // Hide the loader after all asynchronous tasks are completed
        this.loadingService.hide();
    }
}
