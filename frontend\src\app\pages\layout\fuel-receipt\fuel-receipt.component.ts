// Angular core/common modules
import { CommonModule } from '@angular/common';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

// Third-party modules
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { DataTablesModule } from 'angular-datatables';

// Base components and configuration
import { BaseListServerSideComponent } from '../../../config/base.list.server.side.component';
import { Constant } from '../../../config/constants';

// Model
import { FuelReceipt } from '../../../models/fuel-receipt';

// Services
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/services/common.service';
import { ToastService } from '../../../shared/services/toast.service';

// Directives
import { DelayedInputDirective } from '../../../shared/directives/delayed-input.directive';
import { StatusBadgeDirective } from '../../../shared/directives/status-color-badge.directive';

// Pipes
import { DateFormatPipe } from '../../../shared/pipes/date-format.pipe';
import { RemoveUnderscorePipe } from '../../../shared/pipes/remove-underscore.pipe';

// Local manager
import { FuelReceiptManager } from './fuel-receipt.manager';


@Component({
  selector: 'app-fuel-receipt',
  standalone: true,
  imports: [
    CommonModule,
    DataTablesModule,
    DateFormatPipe,
    DelayedInputDirective,
    NgbAccordionModule,
    RemoveUnderscorePipe,
    RouterModule,
    StatusBadgeDirective,
    TranslateModule
  ],
  templateUrl: './fuel-receipt.component.html',
  styleUrl: './fuel-receipt.component.scss'
})
export class FuelReceiptComponent extends BaseListServerSideComponent {

  @ViewChild('driver') driver!: TemplateRef<string>;
  @ViewChild('vehicle') vehicle!: TemplateRef<string>;
  @ViewChild('meterReading') meterReading!: TemplateRef<string>;
  @ViewChild('fuelInLiters') fuelInLiters!: TemplateRef<string>;
  @ViewChild('fuelCost') fuelCost!: TemplateRef<string>;
  @ViewChild('fuelType') fuelType!: TemplateRef<string>;
  @ViewChild('createdOn') createdOn!: TemplateRef<string>;
  @ViewChild('action') action!: TemplateRef<any>;

  resourceType: string = Constant.RESOURCE_TYPE.FUELRECEIPT;
  fuelReceipt!: Array<FuelReceipt>;

  constructor(
    protected fuelReceiptManager: FuelReceiptManager,
    protected override toastService: ToastService,
    protected override loadingService: LoadingService,
    protected override commonService: CommonService,
    protected route: ActivatedRoute,
    protected override router: Router,
  ) {
    super(fuelReceiptManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.fuelReceipt = new Array<FuelReceipt>();

    this.filterParam.fileName = this.resourceType;

    this.columnOrders = Constant.ORDERING_ENTITY_COLUMNS.FUEL_RECEPIT;
    this.filterParam.columns = Constant.EXPORT_ENTITY_COLUMNS.FUEL_RECEPIT;

    this.init();
  }
  ngAfterViewInit() {
    const templates = {
      driver: this.driver,
      vehicle: this.vehicle,
      meterReading: this.meterReading,
      fuelInLiters: this.fuelInLiters,
      fuelCost: this.fuelCost,
      fuelType: this.fuelType,
      createdOn: this.createdOn,
      action: this.action,
    };

    this.setupColumns(templates);
  }
  override onFetchCompleted() {
    this.fuelReceipt = this.records.map((data) => FuelReceipt.fromResponse(data));
    super.onFetchCompleted();
  }
}
