import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable, tap } from 'rxjs';
import { BaseManager } from '../../../config/base.manager';
import { RestResponse } from '../../../models/common/auth.model';
import { FilterParam } from '../../../models/common/filter-param';
import { LoadingService } from '../../../services/loading.service';
import { CommonUtil } from '../../../shared/common.util';
import { ToastService } from '../../../shared/services/toast.service';
import { NotificationsService } from './notifications.service';

@Injectable({
    providedIn: 'root',
})
export class NotificationsManager extends BaseManager {
    private notificationCountSubject = new BehaviorSubject<number>(0);
    notificationCount$ = this.notificationCountSubject.asObservable();

    private refreshCountSubject = new BehaviorSubject<void>(undefined);
    refreshCount$ = this.refreshCountSubject.asObservable();

    constructor(
        protected notificationsService: NotificationsService,
        protected override loadingService: LoadingService,
        protected override toastService: ToastService,
    ) {
        super(notificationsService, loadingService, toastService);
    }

    /* fetch count from API and update BehaviorSubject */
    fetchNotificationCount(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService.getNotificationCount(filterParam).pipe(
            tap((response: RestResponse) => {
                this.notificationCountSubject.next(response.data?.totalCount); // 👈 update shared state
            }),
            map((response: RestResponse) => response)
        );
    }

    /** fetch list of notifications */
    getNotifications(filterParam: FilterParam): Observable<RestResponse> {
        return this.notificationsService.fetchAll(filterParam).pipe(
            map((response: RestResponse) => response)
        );
    }

    /** single notification action (read/delete etc.) */
    notificationAction(notifications: any, isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAction(notifications, isDeleteAction).pipe(
            tap(() => {
                const filterParam = new FilterParam();
                filterParam.filtering.currentDate = CommonUtil.setTodayDate();
                this.fetchNotificationCount(filterParam).subscribe();
            })
        );
    }

    /** bulk notification action (clear all etc.) */
    notificationActions(isDeleteAction?: boolean): Observable<RestResponse> {
        return this.notificationsService.notificationAllAction(isDeleteAction).pipe(
            tap(() => {
                // 👇 after action, refresh count
                const filterParam = new FilterParam();
                this.fetchNotificationCount(filterParam).subscribe();
            })
        );
    }

    /** helper to refresh count everywhere */
    refreshCount(): void {
        const filterParam = new FilterParam();
        filterParam.filtering.currentDate = CommonUtil.setTodayDate();

        this.fetchNotificationCount(filterParam).subscribe();
        this.refreshCountSubject.next();
    }
}
