// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

// Third-party modules
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

// Custom services
import { LoadingService } from '../../../../../services/loading.service';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

//Constant
import { Constant } from '../../../../../config/constants';

// Models
import { FilterParam } from '../../../../../models/common/filter-param';
import { Customer } from '../../../../../models/customer/customer';

// Custom components
import { DialCodeInputComponent } from '../../../../../shared/common-component/dial-code-input/dial-code-input.component';
import { ValidationMessageComponent } from '../../../../../shared/common-component/validation-message/validation-message.component';

// Custom directives
import { EmailValidatorDirective } from '../../../../../shared/directives/email-validator.directive';
import { RippleEffectDirective } from '../../../../../shared/directives/ripple-effect.directive';

@Component({
    selector: 'app-customer-company-contacts',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TranslateModule,
        ValidationMessageComponent,
        NgSelectModule,
        NgSelectModule,
        DialCodeInputComponent,
        RippleEffectDirective,
        EmailValidatorDirective,
    ],
    templateUrl: './customer-company-contacts.component.html',
    styleUrl: './customer-company-contacts.component.scss',
})
export class CustomerCompanyContactsComponent {
    @Input() customer!: Customer;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    customerStatus = Constant.CUSTOMER_STATUS;

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected route: ActivatedRoute,
        protected loadingService: LoadingService,
    ) { }

    save(form: any) {
        if (this.isFormInvalid(form)) return;
        this.saveButtonClicked.emit();
    }

    onNext(form: any) {
        if (this.isFormInvalid(form)) return;
        this.onNextClick.emit(3);
    }

    onBack() {
        this.onNextOrBackClick.emit(1);
    }

    private isFormInvalid(form: any): boolean {
        if (form.valid) return false;

        this.onClickValidation = true;
        return true;
    }
}
