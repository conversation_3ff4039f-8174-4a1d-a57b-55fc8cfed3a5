// Common CSS for all pages
@import '../../variables.scss';
@import '../scss/mixins.scss';

body::-webkit-scrollbar {
    display: none;
}

$width-values: (
    100,
    150,
    200,
    250,
    300,
    350,
    400
);

@each $value in $width-values {
    .width-#{$value} {
        width: #{$value}px !important;
    }
}

// Define spacing values
$spacing-values: (
    0,
    8,
    10,
    12,
    14,
    15,
    18,
    20,
    22,
    24,
    26,
    30,
    50,
    110,
    15vw
);
$fontSizes: (
    10,
    12,
    14,
    16,
    18,
    20,
    22,
    24,
    26,
    28,
    30
);

@each $value in $fontSizes {
    .font-size-#{$value} {
        font-size: #{$value}px !important;
    }
}

// Generate margin classes
@each $value in $spacing-values {
    .mt-#{$value} {
        margin-top: #{$value}px !important;
    }

    .mb-#{$value} {
        margin-bottom: #{$value}px !important;
    }

    .ml-#{$value} {
        margin-left: #{$value}px !important;
    }

    .mr-#{$value} {
        margin-right: #{$value}px !important;
    }
}

// Generate padding classes
@each $value in $spacing-values {
    .pt-#{$value} {
        padding-top: #{$value}px !important;
    }

    .pb-#{$value} {
        padding-bottom: #{$value}px !important;
    }

    .pl-#{$value} {
        padding-left: #{$value}px !important;
    }

    .pr-#{$value} {
        padding-right: #{$value}px !important;
    }
}

// Font weight classes
$font-weights: (
    400,
    500,
    600,
    700
);

@each $weight in $font-weights {
    .fw-#{$weight} {
        font-weight: $weight !important;
    }
}

.mx-negative-2 {
    margin-inline-end: -0.5rem !important;
    margin-inline-start: -0.5rem !important;
}

// creation of width for 50px to 300px
@for $i from 50 through 300 {
    @if $i % 50==0 {
        .width-#{$i} {
            width: #{$i}px !important;
        }
    }
}

.word-break-all {
    word-break: break-all !important;
}

.display-flex-center {
    @include flex-center;
}

.display-flex-column {
    @include flex-center;
    flex-direction: column;
}

.display-align-center {
    display: flex;
    align-items: center;
}

/*---------------------------------------
  CUSTOM PROPERTIES ( VARIABLES )
-----------------------------------------*/
body {
    background-color: $white-color;
    font-size: $font-size-14;
    color: $body-color;
    font-family: $body-font-family;
    font-weight: 400;
    line-height: normal;
}

/*---------------------------------------
  TYPOGRAPHY
  -----------------------------------------*/
h2,
h3,
h4,
h5,
h6 {
    color: $body-color;
    font-family: $body-font-family;
}

a {
    text-decoration: none;

    &:hover {
        color: var(--primary-color);
        text-decoration: none;
    }
}

p {
    letter-spacing: 0;
    margin-bottom: 0px;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

// add ripple effect
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

// Yellow Button Ripple
.btn-primary .ripple {
    background: rgba(255, 255, 255, 0.6); // White ripple for yellow button
}

// White Button with Yellow Border Ripple
.btn-secondary .ripple {
    background: rgba(255, 193, 7, 0.4); // Yellow ripple for white button
}

.logout-btn .ripple {
    background: rgba(255, 255, 255, 0.6);
}

.disabled-container {
    background-color: $disable-color;
    cursor: not-allowed;
}

.dropdown-item-content {
    position: relative;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

// Ripple Animation (slower)
@keyframes ripple-animation {
    to {
        transform: scale(3);
        opacity: 0;
    }
}

.dashboard-main-filter-section {
    gap: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .custom-input-group {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        max-width: 300px;
        min-width: 300px;
        width: 100%;

        &.search-field-size {
            max-width: 270px;
            min-width: 270px;
        }

        .search-form-control {
            font-size: $font-size-14;
            padding-right: 2.5rem;
            height: 48px !important;
            min-height: 48px !important;
        }

        .bi-search {
            position: absolute;
            right: 18px;
            top: 14px;
            font-size: 18px;
        }
    }
}

.page-name-container {
    margin-left: 16px;
    max-width: calc(100vw - 180px);

    .project-name {
        margin: 0px !important;
        color: #337ab7 !important;
    }

    .breadcrumb {
        background: $white-color;
        margin-bottom: 0px !important;
        padding: 0px !important;
        max-width: calc(100% - 0px);
        overflow-x: auto;
        flex-wrap: nowrap;
        -ms-overflow-style: none;
        scrollbar-width: none;

        ::-webkit-scrollbar {
            display: none;
        }

        li {
            font-size: 12px;
            font-weight: $font-weight-500;
            cursor: $cursor-pointer;
            display: flex;

            a {
                display: inline;
                white-space: nowrap;
                max-width: 100%;
            }

            &.active {
                color: $black-color !important;
            }

            &:last-child {
                cursor: unset;
            }
        }
    }

    .menu-icon-button {
        display: inline-block !important;
        vertical-align: middle !important;
        margin-right: 20px;

        img {
            width: 20px;
        }
    }

    .project-name-container {
        display: inline-block !important;
        vertical-align: middle !important;
        border-left: 1px solid #bbb;
        padding-left: 10px;
    }
}

input::placeholder {
    font-weight: $font-weight-600;
}

// status Badges
.status-color-badge {
    border-radius: $border-radius-12;
    padding: 8px 14px;
    min-width: 90px;
    max-width: 160px;
    width: auto;
    display: inline-flex;
    align-items: center;
    font-weight: $font-weight-600;
    font-size: 12px;
    vertical-align: middle;
    text-align: center;
    border: 1px solid;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease-in-out;
    @include flex-center;

    // ✅ Make the status badge editable
    &.editable {
        cursor: pointer;

        &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
    }

    // 🔒 Non-editable badges (e.g., Position column)
    &.non-editable {
        cursor: default;
        opacity: 0.7;
    }

    // Apply dynamic colors based on the status
    @each $status, $colors in $status-colors {
        &.badge-#{$status} {
            background-color: nth($colors, 1);
            color: $white-color;
        }
    }

    .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 4px;
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
    }

    &.cargo-type {
        background-color: var(--primary-color);
        color: $white-color;
        width: fit-content;
    }
}

.eye-icon {
    top: 10px;
    font-size: 24px;
    right: 20px;
    color: #71828a;
    position: absolute;
    cursor: $cursor-pointer;
}

.eye-icon-left {
    right: 30px !important;
}

// File Uploader Css Start
.file-uploader {
    width: 100%;
    border-radius: $border-radius-12;
    border: 1px dashed var(--uploader-dashed-color);
    background-color: var(--uploader-background-color);
    min-height: 110px;
    padding: 12px;
    cursor: $cursor-pointer;
    @include flex-center;

    .file-uploader-header {
        font-size: 12px;
    }
}

.file-preview-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    background-color: #efefef;
    margin-top: 1rem;
    padding: 0.5rem 0;
    border: 1px solid #efefef;
    border-radius: 12px;
}

.cursor-not-allowed {
    cursor: not-allowed !important;
}

.no-hover:hover {
    background: transparent !important;
}

.no-hover:hover svg {
    color: $black-color !important; // reset color
    transform: none !important; // cancel scale
}

.width-fit {
    width: fit-content;
}

.file-item {
    padding: 4px 8px;

    .file-item-content {
        background-color: $white-color;
        display: flex;
        justify-content: start;
        align-items: center;
        width: 100%;
        border-radius: 10px;
        gap: 8px;
        white-space: nowrap;
        min-height: 70px;
        padding: 0 10px;
        margin: 0;
        position: relative;
        z-index: 0;
        cursor: $cursor-pointer;
        text-transform: capitalize;

        .file-icon-container {
            width: fit-content;
            border: 2px solid #efefef;
            padding: 4px;
            border-radius: 10px;

            i {
                font-size: 40px;
            }

            img {
                min-height: 40px;
                min-width: 40px;
            }
        }

        .file-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 45%;

            .file-name {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
                display: block;
            }

            .file-size {
                font-size: 12px;
            }
        }

        .file-remove-btn {
            z-index: 3;
            padding: 6px;
            position: absolute;
            right: 4px;
            border-radius: 50%;

            &:hover {
                background-color: $tab-background;
            }
        }

        i {
            font-size: 17px;
        }

        &.selected-file {
            background-color: $black-color !important;
            color: $white-color !important;
        }
    }
}

// File Uploader Css End

.site-main-container {
    padding: 18px 24px 12px;

    .edit-button-container {
        background: var(--primary-gradient-color);
    }

    .action-button-container {
        height: 40px;
        width: 40px;
        margin: 0 4px;
        border-radius: 12px !important;

        .action-button {
            margin-right: 5px !important;
            border-radius: 10px !important;
        }
    }

    .action-button.btn-primary {
        color: $white-color;
        background-color: #2a2a2a !important;
        border: 1px solid #2a2a2a !important;
    }

    .action-button i {
        color: $white-color !important;
    }

    .dashboard-content-container {
        min-height: 600px;
        background-color: $white-color;
        position: relative;
        margin: auto;

        &.section-edit-form {
            min-height: auto;
        }
    }
}

//ngb date-picker css Start

.date-picker-cls {
    width: 280px !important;
}

ngb-datepicker {
    position: absolute;
    background-color: var($white-color);

    @media screen and (max-width: 340px) {
        &.modal-date-picker {
            width: 260px !important;
        }
    }

    .ngb-dp-header {
        background-color: var(--primary-color);
        padding: 4px;
    }

    select {
        background-color: var(--primary-color);
        color: var(--primary-color);
        margin-right: 4px;

        &:focus {
            border: none !important;
        }
    }

    .btn-link {
        --bs-btn-font-weight: 400;
        --bs-btn-color: var(--bs-white);
        --bs-btn-bg: transparent;
        --bs-btn-border-color: transparent;
        --bs-btn-hover-color: var(--bs-white);
        --bs-btn-hover-border-color: transparent;
        --bs-btn-active-color: var(--bs-white);
        --bs-btn-active-border-color: transparent;
        --bs-btn-disabled-color: #6c757d;
        --bs-btn-disabled-border-color: transparent;
        --bs-btn-box-shadow: 0 0 0 $black-color;
        --bs-btn-focus-shadow-rgb: 49, 132, 253;
        text-decoration: underline;
    }

    .ngb-dp-weekday.ngb-dp-weekday {
        color: var(--primary-color);
    }

    .bg-primary {
        background-color: var(--primary-color) !important;
    }
}

.ngb-dp-month {
    width: 100%;
}

.ngb-dp-weekdays,
.ngb-dp-week {
    justify-content: space-evenly;
}

@media screen and (max-width: 340px) {
    .date-picker-range-cls {
        min-width: 260px !important;
    }
}

.date-picker-range-cls {
    min-width: 300px;

    .dp-hidden {
        width: 0;
        margin: 0;
        border: none;
        padding: 0;
    }

    .custom-day {
        text-align: center;
        padding: 0.185rem 0.25rem;
        display: inline-block;
        height: 2rem;
        width: 2rem;
    }

    .custom-day.focused {
        background-color: #e6e6e6;
    }

    .custom-day.range,
    .custom-day:hover {
        background-color: var(--primary-color) !important;
        color: $white-color;
    }

    .custom-day.faded {
        background-color: var(--primary-color) !important;
    }
}

//ngb date-picker css End

//Sweet Alert Css Start
.swal2-container {
    background: left top no-repeat rgba(0, 0, 0, 0.4) !important;

    .swal2-popup {
        border-radius: 16px !important;

        .swal2-error {
            border-color: red !important;

            .swal2-x-mark-line-left {
                background-color: red !important;
            }

            .swal2-x-mark-line-right {
                background-color: red !important;
            }
        }

        .swal2-icon {
            .swal2-success-line-tip {
                background-color: var(--primary-color) !important;
            }

            .swal2-success-line-long {
                background-color: var(--primary-color) !important;
            }

            .swal2-success-ring {
                border-color: var(--primary-color) !important;
            }
        }

        #swal2-title {
            font-size: 1.5rem;
        }

        .swal2-actions {
            .swal2-confirm {
                background: var(--primary-color) !important;
                border: 1px solid transparent;
                box-shadow: none !important;
            }

            .swal2-cancel {
                background: $white-color;
                color: $black-color;
                font-weight: $font-weight-600;
                border: 1px solid $light-grey;
            }
        }
    }
}

//Sweet Alert Css End

.form-floating>.form-control.is-invalid~.input-right-icon {
    top: 16px !important;
}

.td-align-right {
    width: 100%;
    text-align: end;
    padding-right: 20px;
}

// filter section Css
.filter-container {
    background: $white-color 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #b1a6a629;
    border: 1px solid $light-grey;
    border-radius: $border-radius-12;
    z-index: 52;
}

.row-wrapper {
    margin-left: -0.5rem; // match .row's left negative margin
    margin-right: -0.5rem; // match .row's right negative margin
    padding-left: 0.5rem; // balance it back
    padding-right: 0.5rem;
}

.accordion-collapse {
    transition-duration: 0.1s;
}

// ellipsis
.ellipsis-2-line {
    -webkit-line-clamp: 2;
    @include text-ellipsis-line;
}

.ellipsis-1-line {
    -webkit-line-clamp: 1;
    @include text-ellipsis-line;
}

//ngb modal css start
.modal-close {
    position: absolute;
    top: 1.5rem !important;
    right: 1.5rem !important;
    font-size: 24px !important;
    background-color: $white-color !important;
    border-radius: 0.25rem !important;
    opacity: 1 !important;
    padding: 0 !important;
    box-shadow: 0 0.0625rem 0.375rem 0 rgba(47, 43, 61, 0.1) !important;
    transition: all 0.23s ease 0.1s !important;
    transform: translate(23px, -25px) !important;
    background-image: url('../images/icons/modal_close.svg') !important;
    background-size: 1em 1em !important;
    background-repeat: no-repeat !important;
    background-position: center !important;

    &:hover {
        transform: translate(20px, -20px) !important;
    }
}

.modal-header {
    border: none;
    background-color: $white-color !important;
}

.modal-footer {
    border: none !important;
}

.modal-content {
    border-radius: 0.5rem !important;
}

.modal-title {
    font-size: 20px;
}

// ngb model css end

.more-option-popup {
    .popover-body {
        padding: 4px !important;
    }
}

// Special Request Button CSS
.special-request-button {
    .radio-button-custom {
        position: absolute;
        top: -8px;
        left: 20px;
        background: $white-color;
        font-weight: $font-weight-600;
        font-size: 13px;
        padding: 0 8px;
        color: #495057;
        z-index: 1;
    }

    .input-field-radio {
        background-color: $white-color;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: box-shadow 0.3s ease-in-out;

        &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .radio-buttons {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 14px;

            .form-check {
                display: flex;
                align-items: center;

                .form-check-input {
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    border: 1px solid #adb5bd;
                    transition: all 0.2s ease-in-out;
                    cursor: pointer;

                    & :checked {
                        background-color: var(--primary-color) !important;
                    }
                }

                .form-check-label {
                    margin-left: 8px;
                    color: #212529;
                    font-size: $font-size-14;
                    font-weight: $font-weight-500;
                }
            }
        }
    }
}

.modal-content-container {
    padding: 0 16px;
}