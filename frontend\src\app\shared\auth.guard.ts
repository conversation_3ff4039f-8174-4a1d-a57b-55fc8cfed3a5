import { inject } from '@angular/core';
import {
    ActivatedRouteSnapshot,
    CanActivateChildFn,
    CanActivateFn,
    Router,
    RouterStateSnapshot,
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from './services/auth.services';

export class AuthGuard {
    static canActivate(): CanActivateFn {
        return (
            route: ActivatedRouteSnapshot,
            state: RouterStateSnapshot,
        ): Observable<boolean> | Promise<boolean> | boolean => {
            const authService = inject(AuthService);
            const router = inject(Router);
            const roles: string[] = route.data['roles'] || [];

            return authService.isAuthorizedUser(roles).then((response: any) => {
                if (roles.includes('ROLE_ANONYMOUS')) {
                    if (response.hasAccess) {
                        router.navigate(['dashboard']);
                        return false;
                    }
                    return true;
                }

                if (!response.hasAccess) {
                    router.navigate(['/login'], { queryParams: { redirectTo: state.url } });
                    return false;
                }

                if (!response.hasRoleAccess) {
                    router.navigate(['403']);
                    return false;
                }

                return true;
            });
        };
    }

    static canActivateChild(): CanActivateChildFn {
        return async (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> => {
            const authService = inject(AuthService);
            const router = inject(Router);
            const roles = route.data['roles'] as Array<string>;
            // Ensure permissions are loaded or fetched
            return authService.isAuthorizedUser(roles).then((response: any) => {
                if (!response.hasPermissionAccess) {
                    // Optionally navigate to an error page
                    router.navigate(['404']);
                    return false;
                }
                return true;
            });
        };
    }
}
