import { AccountService } from './../../services/account.service';
// Angular core modules
import { CommonModule } from '@angular/common';
import { Component, NgZone } from '@angular/core';

// Angular routing modules
import { ActivatedRoute, Router } from '@angular/router';

// External third-party modules
import { NgbDropdownModule, NgbProgressbarModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { LegendPosition, NgxChartsModule } from '@swimlane/ngx-charts';

// Application constants and utilities
import { Constant } from '../../config/constants';
import { CommonUtil } from '../../shared/common.util';

// Application services
import { LoadingService } from '../../services/loading.service';
import { LayoutComponent } from '../layout/layout.component';

// Shared components
import { ROLES } from '../../config/roles';
import { FilterParam } from '../../models/common/filter-param';
import { AuthService } from '../../shared/services/auth.services';
import { LocalStorageService } from '../../shared/services/local-storage.service';

@Component({
    selector: 'app-dashboard',
    standalone: true,
    imports: [
        CommonModule,
        NgxChartsModule,
        NgbProgressbarModule,
        TranslateModule,
        NgbDropdownModule,
    ],
    templateUrl: './dashboard.component.html',
    styleUrl: './dashboard.component.scss',
    providers: [FilterParam]
})
export class DashboardComponent {
    LegendPosition = LegendPosition;

    dashboardStats = [] as any;
    shipmentStatusCount = [] as any;
    shipmentStatus = [] as any;
    quotationStatus = [] as any;
    quotationStatusCount = [] as any;
    disabled = false;
    data: any;
    selectedDuration: string = ''; // or set default e.g. 'today'

    durationPeriod: number = 30; // default

    roles = ROLES;

    showBlankBox = false;

    view: [number, number] = [400, 300];
    totalShipments: number = 0;
    pendingDeliveries: number = 0;
    // New properties for statistics cards
    totalQuotations: number = 0;
    totalRevenue: string = '0';
    totalRateSheets: string = '0';

    customShipmentColors = [
        { name: 'New', value: '#ec0b0b' },
        { name: 'Assigned', value: '#007bff' },
        { name: 'in transit', value: '#ffc107' },
        { name: 'Delivered', value: '#28a745' },
        { name: 'Completed', value: '#198754' }
    ];

    customQuotationColors = [
        { name: 'Requested', value: '#0ba5ec' },
        { name: 'Client Approval', value: '#ffc107' },
        { name: 'Confirmed', value: '#28a745' },
        { name: 'Rejected', value: '#ff4405' },
    ]

    durationOptions = [
        { value: 30, label: 'DashboardFilter.30Days' },
        { value: 60, label: 'DashboardFilter.60Days' },
        { value: 90, label: 'DashboardFilter.90Days' },
        { value: 180, label: 'DashboardFilter.180Days' },
        { value: 365, label: 'DashboardFilter.1Year' },
        { value: 0, label: 'DashboardFilter.all' }
    ];

    constructor(
        public commonUtil: CommonUtil,
        public accountService: AccountService,
        public loadingService: LoadingService,
        public layoutComponent: LayoutComponent,
        public localStorageService: LocalStorageService,
        public authService: AuthService,
        public router: Router,
        protected ngZone: NgZone,
        public filterParam: FilterParam,
        protected route: ActivatedRoute,
    ) { }

    ngOnInit() {
        this.filterParam = new FilterParam();

        if (this.durationPeriod !== 0) {
            this.filterParam.filtering.createdWithinDays = this.durationPeriod ?? 0;
        }

        this.fetchData(this.durationPeriod);

        this.localStorageService.onStorageChange().subscribe(() => {
            this.fetchData(this.durationPeriod);
        });

        if (window.innerWidth < 576) {
            this.view = [300, 250];
        }
    }

    formatStatus(text: string): string {
        return text
            .toLowerCase()
            .replace(/_/g, ' ') // Replace underscores with spaces
            .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
    }

    fetchData(tab: number): void {
        this.loadingService.show();
        this.filterParam.filtering.createdWithinDays = (tab === 0 ? null : tab);

        this.accountService.getDashboardDataByFilter(this.filterParam).subscribe((response: any) => {

            this.shipmentStatusCount = response?.data?.shipmentStatusCount;
            this.quotationStatusCount = response?.data?.quotationStatusCount;

            this.shipmentStatus = this.sanitizeChartData(
                response?.data?.shipmentStatusCount,
                this.formatStatus
            );

            this.quotationStatus = this.sanitizeChartData(
                response?.data?.quotationStatusCount,
                this.formatStatus
            );

            if (!this.shipmentStatus.length && !this.quotationStatus.length) {
                this.showBlankBox = true;
            }

            this.dashboardStats = response?.data;
            this.loadingService.hide();
        });
    }

    // New method to export chart data
    exportChart(chartType: 'shipment' | 'quotation'): void {
        const data = chartType === 'shipment' ? this.shipmentStatus : this.quotationStatus;
        const filename = `${chartType}_chart_data_${new Date().toISOString().split('T')[0]}.json`;

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);
    }

    // Helper method to calculate total from status count array
    private calculateTotal(statusCount: any[]): number {
        if (!statusCount || !Array.isArray(statusCount)) return 0;
        return statusCount.reduce((total, item) => total + (item.count || 0), 0);
    }

    // Helper method to calculate pending deliveries
    private calculatePendingDeliveries(shipmentStatusCount: any[]): number {
        if (!shipmentStatusCount || !Array.isArray(shipmentStatusCount)) return 0;
        const pendingStatuses = ['New', 'Assigned', 'in transit'];
        return shipmentStatusCount
            .filter(item => pendingStatuses.includes(item.status))
            .reduce((total, item) => total + (item.count || 0), 0);
    }

    // Helper method to calculate total revenue
    private calculateTotalRevenue(data: any): string {
        // This is a placeholder - you would implement actual revenue calculation
        // based on your data structure
        const revenue = data?.totalRevenue || 0;
        return revenue.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
    }

    private handleCardClick(event: any, filtering: number, path: string): void {
        if (filtering && filtering > 0) {
            this.ngZone.run(() =>
                this.router.navigate([`/dashboard/${path}`], {
                    state: { Filtering: this.durationPeriod },
                })
            );
        } else {
            this.router.navigate([`/dashboard/${path}`])
        }
    }

    getFilterParam(tab: number): number {
        const validTabs = [30, 60, 90, 180, 365];
        return validTabs.includes(tab) ? tab : 60;
    }

    onShipmentSelect(event: any): void {
        this.handleChartClick(
            event,
            Constant.SHIPMENT_STATUS_OPTIONS,
            this.durationPeriod,
            'shipments'
        );
    }

    onCustomerSelect(event: any) {
        const value = this.durationPeriod ?? this.durationPeriod;
        this.handleCardClick(event, value, 'customers');
    }

    onRateCardSelect(event: any) {
        this.handleCardClick(event, this.durationPeriod, 'rate-sheets');
    }

    onEmployeeSelect(event: any) {
        this.handleCardClick(event, this.durationPeriod, 'employees');
    }

    onQuotationSelect(event: any): void {
        this.handleChartClick(
            event,
            Constant.QUOTATION_STATUS,
            this.durationPeriod,
            'quotations'
        );
    }

    setActiveTab(tab: number): void {
        this.durationPeriod = tab;
        this.loadingService.show();

        this.filterParam.filtering.createdWithinDays = (tab === 0 ? null : tab);
        this.fetchData(tab);
    }

    // Add this new method to get the selected option's label
    getSelectedOptionLabel(): string {
        const selectedOption = this.durationOptions.find(option => option.value === this.durationPeriod);
        return selectedOption ? selectedOption.label : "DashboardFilter.durationFilter";
    }

    private getStatusId(
        statusList: { id: string; name: string }[],
        label: string
    ): string | undefined {
        return statusList.find(
            (status) => status.name.toLowerCase() === label.toLowerCase()
        )?.id;
    }

    private handleChartClick(
        event: any,
        statusList: { id: string; name: string }[],
        duration: number,
        path: string
    ): void {
        const label = event?.name?.toLowerCase();
        const route = this.getStatusId(statusList, label);

        if (route) {
            this.ngZone.run(() =>
                this.router.navigate([`/dashboard/${path}`], { state: { data: route, Filtering: duration } })
            );
        }
    }

    private sanitizeChartData(
        data: any[],
        formatter: (status: any) => string
    ): { name: string; value: number }[] {
        return (data || [])
            .filter(
                (item) =>
                    item &&
                    item.status !== undefined &&
                    item.count !== undefined &&
                    item.count !== null
            )
            .map((item) => ({
                name: formatter(item.status),
                value: Number(item.count),
            }))
            .filter(
                (item) =>
                    typeof item.value === 'number' &&
                    !isNaN(item.value) &&
                    isFinite(item.value) &&
                    item.value > 0
            );
    }
}