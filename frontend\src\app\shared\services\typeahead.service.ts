import { Injectable } from '@angular/core';
import { Observable, Subject, of } from 'rxjs';
import { debounceTime, switchMap, tap } from 'rxjs/operators';
import { FilterParam } from '../../models/common/filter-param';

export interface TypeaheadSearchConfig<T extends { id: any }> {
    subject: Subject<string>;
    fetchFunction: (param: FilterParam, endpoint?: string) => Observable<T[]>; // update to accept optional endpoint
    updateResults: (results: T[]) => void;
    updateLoading: (isLoading: boolean) => void;
    selectedItemGetter?: () => T | undefined;
    endpoint?: string; // add optional endpoint
}

@Injectable({
    providedIn: 'root' // This makes the service available globally
})
export class TypeAheadFilterService {
    setupSearchSubscription<T extends { id: any }>(
        searchSubject: Subject<string>,
        filterParam: FilterParam,
        getDataFn: (param: FilterParam, endpoint?: string) => Observable<T[]>,
        resultCallback: (result: T[]) => void,
        loadingCallback: (isLoading: boolean) => void,
        selectedItemGetter?: () => T | undefined,
        endpoint?: string // ✅ pass endpoint to function
    ) {
        searchSubject.pipe(
            debounceTime(300),
            tap(() => loadingCallback(true)),
            switchMap(searchTerm => {
                if (!searchTerm?.trim()) {
                    loadingCallback(false);

                    const selected = selectedItemGetter?.();
                    return of(selected ? [selected] : []);
                }

                filterParam.filtering.searchTerm = searchTerm.trim();
                return getDataFn(filterParam, endpoint); // ✅ pass endpoint here
            })
        ).subscribe(results => {
            const selected = selectedItemGetter?.();
            const resultsWithSelected = selected && !results.some(r => r.id === selected.id)
                ? [selected, ...results]
                : results;

            resultCallback(resultsWithSelected);
            loadingCallback(false);
        });
    }

}
