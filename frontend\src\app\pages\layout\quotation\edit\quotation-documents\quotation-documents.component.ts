import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FilterParam } from '../../../../../models/common/filter-param';
import { Quotation } from '../../../../../models/quotation/quotation';
import { LoadingService } from '../../../../../services/loading.service';
import { FileUploaderComponent } from '../../../../../shared/common-component/file-uploader/file-uploader.component';
import { AuthService } from '../../../../../shared/services/auth.services';
import { CommonService } from '../../../../../shared/services/common.service';

@Component({
    selector: 'app-quotation-documents',
    standalone: true,
    imports: [FormsModule, TranslateModule, CommonModule, FileUploaderComponent, RouterModule],
    templateUrl: './quotation-documents.component.html',
    styleUrl: './quotation-documents.component.scss',
})
export class QuotationDocumentsComponent {
    @Input() quotation!: Quotation;
    @Input() onClickValidation!: boolean;
    @Input() request!: any;
    @Input() filterParam!: FilterParam;
    @Input() disabled!: boolean;

    @Output() onNextClick = new EventEmitter<number>();
    @Output() onNextOrBackClick = new EventEmitter<number>();
    @Output() saveButtonClicked = new EventEmitter<void>();

    uploaderVisibility: boolean = true;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['shipment']?.currentValue?.id) {
            this.uploaderVisibility = false;
            setTimeout(() => {
                this.uploaderVisibility = true;
            });
        }
    }

    constructor(
        protected commonService: CommonService,
        protected authService: AuthService,
        protected route: ActivatedRoute,
        protected loadingService: LoadingService,
        protected router: Router,
    ) { }

    onNext(form: any) {
        if (this.isFormInvalid(form)) return;
        this.onNextClick.emit(6);
    }

    onBack() {
        this.onNextOrBackClick.emit(5);
    }

    save(form: any) {
        if (this.isFormInvalid(form)) return;
        this.saveButtonClicked.emit();
    }

    handleCancelClick() {
        this.router.navigate(['/dashboard/quotations']);
    }

    private isFormInvalid(form: any): boolean {
        if (form.valid) return false;
        this.onClickValidation = true;
        return true;
    }
}
