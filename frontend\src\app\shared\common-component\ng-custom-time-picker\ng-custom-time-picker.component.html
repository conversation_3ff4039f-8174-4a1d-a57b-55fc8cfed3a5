<!-- Time picker input -->
<div class="form-floating" [class.cursor-not-allowed]="disabled">
    <input class="form-control margin-bottom-12 pe-none " name="time" [ngxTimepicker]="toggleTimepicker"
        [disableClick]="true" [format]="24" [(ngModel)]="selectedTime" [disabled]="readOnly" [required]="isRequired"
        placeholder="{{ labelName | translate }}" (ngModelChange)="onTimeSelect($event)" #Time="ngModel"
        [required]="isRequired" datepickerClass="date-picker-cls" [value]="selectedTime" [ngClass]="{
                    'is-invalid': Time.invalid && isRequired && onClickValidation,  
    'cursor-not-allowed': readOnly
                }" [disabled]="disabled">

    <ngx-material-timepicker-toggle class="toggle-position" [for]="toggleTimepicker"></ngx-material-timepicker-toggle>
    <ngx-material-timepicker #toggleTimepicker [theme]="customTheme"></ngx-material-timepicker>

    <label for="time">{{labelName | translate}}</label>

    <!-- Validation message -->
    <app-validation-message [onClickValidation]="onClickValidation"></app-validation-message>
</div>