<div class="site-page-container mt-3">
    <div class="site-card">
        <form #shipmentForm="ngForm" novalidate="novalidate">
            <div class="custom-responsive-row row justify-content-center">
                <div class="col-md-9">
                    <div class="custom-responsive-row row">

                        <!-- start of section for the basic information -->
                        <ng-container title="Shipment Information">
                            <div class="label-wrap-box mt-14">
                                <span>{{"SHIPMENT.objName" | translate}} {{"USERS.Information" | translate}}</span>
                            </div>

                            <!-- Rate Sheet -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="rateSheets"
                                        [(ngModel)]="shipment.rateSheet" #RateSheet="ngModel" name="RateSheet"
                                        [ngClass]="{
                                        'is-invalid': !RateSheet.valid && onClickValidation && !disabled
                                      }" [clearable]="false" [typeahead]="searchRateSheetSubject"
                                        [loading]="loadingRateSheetNgSelect" (change)="onRateSheetChange($event)"
                                        [disabled]="disabled">
                                    </ng-select>

                                    <label for="RateSheet" class="ng-select-label">{{"RATE_SHEET.objName" |
                                        translate}}</label>

                                    <app-validation-message [field]="RateSheet"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>

                            <!-- Assigned Vehicle -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="name" bindValue="id" [items]="vehicles"
                                        [(ngModel)]="shipment.vehicle" #vehicle="ngModel" name="vehicle" [ngClass]="{
                                        'is-invalid': !vehicle.valid && onClickValidation && !disabled
                                      }" [typeahead]="searchVehicleSubject" [loading]="loadingVehicleNgSelect"
                                        [disabled]="disabled">
                                        <ng-template ng-footer-tmp>
                                            <div (click)="openModal(content, 'Add New vehicle', true)">
                                                {{"COMMON.ADDNEW" | translate}} <span><i
                                                        class="bi bi-plus-lg"></i></span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <label for="vehicle" class="ng-select-label">{{"SHIPMENT.assigned" | translate }}
                                        {{"SHIPMENT.vehicle" |
                                        translate }}</label>
                                    <app-validation-message [field]="vehicle"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>

                            <!-- Barcode -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <div class="form-group form-floating mb-14 custom-ng-select">
                                    <ng-select bindLabel="barcodeNo" bindValue="id" [items]="barcodes"
                                        [(ngModel)]="shipment.barcode" #Barcode="ngModel" name="barcode" disabled>
                                    </ng-select>
                                    <label for="barcode" class="ng-select-label">
                                        {{"BARCODE.objName" |
                                        translate }}</label>
                                    <app-validation-message [field]="Barcode"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>

                            <!-- Shipment Secured Properly -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <label class="mb-1 d-block"
                                    for="securedYes"><strong>{{"SHIPMENT.shipmentSecuredProperly" |
                                        translate}}</strong></label>

                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="secured"
                                        [(ngModel)]="shipment.isSecured" [value]="true" id="securedYes"
                                        [disabled]="disabled">
                                    <label class="form-check-label" for="securedYes">Yes</label>
                                </div>

                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="secured"
                                        [(ngModel)]="shipment.isSecured" [value]="false" id="securedNo"
                                        [disabled]="disabled">
                                    <label class="form-check-label" for="securedNo">No</label>
                                </div>
                            </div>

                            <!-- Shipment Start Date -->
                            <div class="col-sm-6 col-xl-3 px-0 ps-md-0 pe-md-2 mb-14">
                                <app-ng-custom-date-picker name="startDate" ngDefaultControl
                                    [labelName]="'SHIPMENT.startDate'" [onClickValidation]="onClickValidation"
                                    [selectedDate]="shipment.startDate" (setDate)="setStartDate($event)"
                                    (click)="setActiveDatePicker('startDate')"
                                    (datePickerClosed)="setActiveDatePicker(null)" [disabled]="true"
                                    [customZIndex]="openDatePicker === 'startDate' ? 'z-index-4' : 'z-index-3'">
                                </app-ng-custom-date-picker>
                            </div>

                            <!-- shipment Start Time -->
                            <div class="col-sm-6 col-xl-3 px-0 ps-md-2 ps-xl-0 pe-md-0 pe-xl-2 mb-14">
                                <app-ng-custom-time-picker name="formTime" ngDefaultControl
                                    [onClickValidation]="onClickValidation" [disabled]="true"
                                    [labelName]="('SHIPMENT.startTime' | translate)"
                                    (setTime)="shipment.startTime = $event"
                                    [selectedTime]="shipment.startTime"></app-ng-custom-time-picker>
                            </div>

                            <!-- Shipment End Date -->
                            <div class="col-sm-6 col-xl-3 px-0 ps-xl-2 pe-md-2 pe-xl-0 mb-14">
                                <app-ng-custom-date-picker name="endDate" ngDefaultControl
                                    [labelName]="'SHIPMENT.endDate'" [selectedDate]="shipment.endDate"
                                    (setDate)="setEndDate($event, 'ENDDATE')" [onClickValidation]="onClickValidation"
                                    [customZIndex]="openDatePicker === 'endDate' ? 'z-index-4' : 'z-index-3'"
                                    (click)="setActiveDatePicker('endDate')" [disabled]="true">
                                </app-ng-custom-date-picker>
                            </div>

                            <!-- shipment End Time -->
                            <div class="col-sm-6 col-xl-3 px-0 ps-md-2 pe-md-0 mb-14">
                                <app-ng-custom-time-picker name="formTime" ngDefaultControl
                                    [onClickValidation]="onClickValidation" [selectedTime]="shipment.endTime"
                                    [labelName]="('SHIPMENT.endTime' | translate)" [disabled]="true"
                                    (setTime)="shipment.endTime = $event"></app-ng-custom-time-picker>
                            </div>

                            <!-- ETD -->
                            <div class="col-md-6 px-0 ps-md-0 pe-md-2">
                                <app-ng-custom-date-picker name="etd" ngDefaultControl [labelName]="'ETD'"
                                    [selectedDate]="shipment.etd" (setDate)="setEndDate($event, 'ETD')"
                                    [onClickValidation]="onClickValidation" (click)="setActiveDatePicker('etd')"
                                    [customZIndex]="openDatePicker === 'etd' ? 'z-index-4' : 'z-index-3'"
                                    [disabled]="disabled" [minDate]="getFormattedDate(shipment.createdOn)">
                                </app-ng-custom-date-picker>
                            </div>

                            <!-- Delay on Loading -->
                            <div class="col-md-6 px-0 ps-md-2 pe-md-0">
                                <div class="form-floating mb-14">
                                    <input class="form-control" type="text" name="delayOnLoading"
                                        [(ngModel)]="shipment.loadingDelay" placeholder="" #delayOnLoading="ngModel"
                                        [allowedValue]="'.'" mask="separator.2" maxlength="8" appAllowNumberOnly
                                        [ngClass]="{'is-invalid': !delayOnLoading.valid && onClickValidation}"
                                        [disabled]="disabled" (keyup)="onKeyUpLoadingDelay()">
                                    <label for="delayOnLoading">{{"SHIPMENT.delayOnLoading" | translate}}(in
                                        Hrs)</label>
                                    <app-validation-message [field]="delayOnLoading"
                                        [onClickValidation]="onClickValidation"></app-validation-message>
                                </div>
                            </div>
                        </ng-container>
                        <!-- end of section for the basic information -->

                        <!-- Buttons -->
                        <div class="clearfix"></div>
                        <div class="col-md-12 custom-buttons-container">
                            <button class="btn cancel-button" appRippleEffect type="button"
                                (click)="handleCancelClick()">
                                {{ "COMMON.CANCEL" | translate }}
                            </button>

                            <button class="btn custom-medium-button save-button" appRippleEffect type="button"
                                (click)="onBack()">
                                <div class="site-button-inner">
                                    {{ "COMMON.BACK" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="save(shipmentForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVE" | translate }}
                                </div>
                            </button>
                            <button class="btn custom-medium-button save-button" *ngIf="!disabled" appRippleEffect
                                type="button" (click)="onNext(shipmentForm.form)">
                                <div class="site-button-inner">
                                    {{ "COMMON.SAVEANDNEXT" | translate }}
                                </div>
                            </button>

                            <button class="btn custom-medium-button save-button" *ngIf="disabled" appRippleEffect
                                type="button" (click)="onNextOrBackClick.emit(4)">
                                <div class="site-button-inner">
                                    {{ "COMMON.NEXT" | translate }}
                                </div>
                            </button>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- modal for certificateType -->
<ng-template #content let-modal>
    <div class="modal-header mx-2 mx-md-4">
        <h4 class="modal-title">{{title }}</h4>
        <button type="button" class="btn-close modal-close" aria-label="Close"
            (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body pt-0 mx-2 mx-md-4">
        <app-vehicle-edit [isOpenedInModal]="true" (newVehicleAdded)="getSavedVehicle($event)"
            [vehicleModalRef]="vehicleModelRef" [recordId]="'0'"></app-vehicle-edit>
    </div>
</ng-template>